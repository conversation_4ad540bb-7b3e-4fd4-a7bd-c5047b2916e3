
import { AwsClientConfig, ClientConfigWrapper } from "@/models";
import { CancelAbsenceModel, SubmitAbsenceResponse } from "@/models/AbsenceModel";
import { DroppedSessionModel } from "@/models/DroppedSessionModel";
import { AbsenceApiService, CallLog, LocalConfiguration, ScopedHttpService } from "@/services";
import { ClaimStatus } from "@/util";
import moment from "moment";
const callLog = new CallLog();
const localConfig = new LocalConfiguration();
const absApiService = new AbsenceApiService();
let lambdaName = 'DroppedSession';
export const handler = async(event: any) => {
    callLog.Logger.log('Inside DroppedSessionMonitor - Input', event); 
    const openedSession: DroppedSessionModel[] = await callLog.getOpenedSessionsAsync();
    
    
    var possibleDeadSession: DroppedSessionModel[] = [];
    
   for(const current of openedSession) {       
       
        let timeToSend =  process.env.SessionMonitorCallInterval ?? 30;
        
        if(await checkDeadSession(current, +timeToSend))
            possibleDeadSession.push(current);

    }
    let toCancelInApi: any[] = [];
    let isRemoved: boolean = false;
    if(possibleDeadSession && Array.isArray(possibleDeadSession) && possibleDeadSession.length >0){   
        //**Below functionality sends all incomplete/dropped claims to Abiliti system
       await  possibleDeadSession.forEach( (dSession:DroppedSessionModel) =>{
             validateAndSend(dSession);            
        })
         //*****below functionality removes incomplete/dropped calls from all systems     
        // for (const deadSession of possibleDeadSession) {
        //      await  callLog.deleteCallDataAsync(deadSession.ContactId);
        //     if(deadSession.AbsenceIncident.AbsenceIncidentId)
        //         toCancelInApi.push(deadSession.AbsenceIncident.AbsenceIncidentId);
        // }
        
        // if(Array.isArray(toCancelInApi) && toCancelInApi.length >0){
        //     let cancelRequest: CancelAbsenceModel = {
        //         CancelComment: "CallDropped in IVR",
        //         CancelReason: "Call Dropped in IVR"
        //     }
        //     var result = true// await absApiService.cancelIncidentsAsync(toCancelInApi, cancelRequest);
        //     callLog.Logger.log(`claims cancelled success: ${result}`);
        //     return result;
        // }
    }
        
}
async function checkDeadSession(session: DroppedSessionModel, timeToCancel: number): Promise<boolean> {
   if(timeToCancel === 0)
        return false;
   const lastSessionEvent = session.CallStageLog ? session.CallStageLog[session.CallStageLog.length - 1] : '';
   if (session.CallStageLog &&
        session.CallStageLog.length > 0 &&
        moment(session.CallStageLog[session.CallStageLog.length - 1].TimeStamp).add(timeToCancel, 'minutes').isBefore( moment(new Date())) &&
        session.IsSubmitted !== ClaimStatus.Submitted){            
        return true;
        }

    return false; 
   
}

async function validateAndSend(session: DroppedSessionModel){
    if(session.AbsenceIncident)
    {
        let callData = await callLog.getCallDataAsync(session.ContactId);
        
        if(session.AbsenceIncident.PrimaryReason && session.AbsenceIncident.SecondaryReason && !session.AbsenceIncident.AbsenceIncidentId)
        {
            let savedIncident =  (await absApiService.createIncidentAsync(session.ClientCode, session.ContactId, session.AbsenceIncident));
            savedIncident = { ...session.AbsenceIncident, ...savedIncident };
            
            if (callData.Absences && Array.isArray(callData.Absences)){
                //remove the exsiting
                callData.Absence.pop();
                //add the new absence
                callData.Absences.push(savedIncident.AbsenceIncident);
            }
            await callLog.setCallDataAsync(callData.ContactId, {                
                IsSaved: "true",
                Absences: callData.Absences ?? [savedIncident]
              })
        }
        if(session.AbsenceIncident.PrimaryReason && session.AbsenceIncident.SecondaryReason && session.AbsenceIncident.AbsenceIncidentId)
        {
            let currentAbsence = callData.Absences.peekLast();
            if(currentAbsence.AbsenceIncident && Array.isArray(currentAbsence.AbsenceIncident.AbsenceDates) && currentAbsence.AbsenceIncident.AbsenceDates.length > 0)
            {
                let submittedIncident: SubmitAbsenceResponse = await absApiService.submitIncidentAsync(callData.ClientCode, callData.ContactId, currentAbsence.AbsenceIncident);
                currentAbsence.ValidationErrors = [];
                if(submittedIncident.ValidationErrors.length > 0)
                {
                currentAbsence.ValidationErrors = submittedIncident.ValidationErrors;
                callLog.Logger.error(JSON.stringify(submittedIncident.ValidationErrors));
                await callLog.logStageAsync(callData.ContactId, lambdaName, submittedIncident.ValidationErrors);                
                }
                else{      
                currentAbsence.AbsenceIncident = submittedIncident.AbsenceIncident;
                currentAbsence.ClosingScripts ? currentAbsence.ClosingScripts?.push(submittedIncident.AbsenceClosingScriptModel) : currentAbsence.ClosingScripts =  [submittedIncident.AbsenceClosingScriptModel]
                
                callLog.Logger.log(`The confirmation number is ${submittedIncident.AbsenceIncident.AbsenceIncidentId} inside dropped session`);            
                callData.Absences.pop();
                callData.Absences.push(currentAbsence);
                await callLog.setCallDataAsync(callData.ContactId, {
                    IsSaved: "true",
                    Absences: callData.Absences,
                    IsSubmitted: ClaimStatus.Submitted
                    });
                 }
            }
        }
    
    }
    
}