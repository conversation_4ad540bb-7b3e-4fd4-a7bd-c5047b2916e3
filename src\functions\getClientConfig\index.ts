import { ConnectContactFlowEvent } from "aws-lambda";
import { CallLog, LocalConfiguration, ScopedHttpService, CallFlowConfig} from '@/services'
import CallFlowObjQa from '../../../config/qa/CallFlow.json'
import { Constants } from '@/util'
import CallFlowObjUat from '../../../config/uat/CallFlow.json'

import CallFlowObjProd from '../../../config/prod/CallFlow.json'

import { AwsClientConfig, ClientConfigWrapper, CallflowConfig } from "@/models";


const localConfig = new LocalConfiguration();
const callService =  new CallLog();
const callFLowService = new CallFlowConfig();

export const handler = async(event: ConnectContactFlowEvent) => {
    localConfig.Logger.log('Input data', event);
    const clientCode = event.Details.Parameters.ClientCode;
    const selectedLang = event.Details.Parameters.Language.toUpperCase();
    const contactId = event.Details.ContactData.ContactId;
    localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
    const config = await localConfig.getClientConfigAsync(clientCode);

    let toReturn: AwsClientConfig = {
        ClientCode: clientCode,

    };
    let callFlowArn: CallflowConfig = {};
    if (process.env.environment === 'QA') {
        callFlowArn = CallFlowObjQa

    }
    if (process.env.environment === 'UAT') {
        callFlowArn = CallFlowObjUat

    }
    if (process.env.environment === 'PROD') {
        callFlowArn = CallFlowObjProd

    }

    await callFLowService.putCallFlowConfig(process.env.environment ?? "QA", callFlowArn);
    await callService.setCallDataAsync(contactId, {
        IsSaved: "true",
        SelectedLang: selectedLang,
    });
    //localConfig.Logger.log('Config data', JSON.stringify(config));
    MapClientConfig(config, toReturn, selectedLang, callFlowArn);
    //localConfig.Logger.log('Result data', JSON.stringify(toReturn));
    return toReturn;
}

function MapClientConfig(config: ClientConfigWrapper, toReturn: AwsClientConfig, language: string, callFlowArns: CallflowConfig) {
    //localConfig.Logger.log('Language inside mapclientconfig', language);
    //localConfig.Logger.log('Config data inside mapclientconfig', JSON.stringify(config));
    //localConfig.Logger.log('Result data inside mapclientconfig before', JSON.stringify(toReturn));
    toReturn.DefaultLanguage = language.toUpperCase();
    toReturn.MaxRetryLambda1 = config.clientConfig.MaxRetryValue?.toString(); //used by IVR to call the lambdas incase of timeout error - amount of retry from IVR lambdas
    toReturn.MaxRetryLambda2 = config.clientConfig.MaxRetryValue?.toString(); //not used currently 
    toReturn.MaxRetryMenu1 = config.clientConfig.MaxRetryValue?.toString(); //used by IVR for failures and retry
    toReturn.MaxRetryMenu2 = config.clientConfig.MaxRetryValue?.toString(); // not used by anyone
    toReturn.MaxTotalMissedAbsenceDays = config.clientConfig.MaxTotalMissedAbsenceDays?.toString();
    toReturn.MaxAllowAbsenceDaysPast = config.clientConfig.MaxAllowAbsenceDaysPast?.toString();
    toReturn.MaxAllowAbsenceDaysFuture = config.clientConfig.MaxAllowAbsenceDaysFuture?.toString();
    toReturn.DefaultDateFormatType = config.clientConfig.ClientDateFormat == "MMDDYYYY" ? "0" : "1";
    toReturn.DefaultTimeFormatType = config.clientConfig.TimeFormatMask == "HHMM" ? "0" : "1";
    toReturn.IsEnterEmployeePhoneNumberRequired = config.clientConfig.IsEnterEmployeePhoneNumberRequired ?? false;
    toReturn.PromptTransferNumberGeneralErrorSSML = "";
    toReturn.PromptEnterEmployeeIdSSML = config.getPromptText(config.clientConfig.EnterEmployeeIdText, language) ?? '';
    toReturn.PromptInvalidEmployeeIdSSML1 = config.getPromptText(config.clientConfig.InvalidEmployeeIdText1, language) ?? '';
    toReturn.PromptInvalidEmployeeIdSSML2 = config.getPromptText(config.clientConfig.InvalidEmployeeIdText2, language) ?? '';
    toReturn.PromptEnterDobSSML = config.getPromptText(config.clientConfig.EnterDobText, language) ?? '';
    toReturn.PromptCarrierTransferMsgSSML = config.getPromptText(config.clientConfig.CarrierTransferText, language) ?? '';
    toReturn.PromptIntroPrimaryReasonMenuSSML = config.getPromptText(config.clientConfig.PrimaryReasonIntroText, language) ?? '';
    toReturn.PromptIntroSecondaryReasonMenuSSML = config.getPromptText(config.clientConfig.SecondaryReasonIntroText, language) ?? '';
    toReturn.PromptEnterReturnToWorkSSML = config.getPromptText(config.clientConfig.EnterRtwDateText, language) ?? '';
    toReturn.PromptIsReturnToWorkSSML2 = config.getPromptText(config.clientConfig.RtwText2, language) ?? '';
    toReturn.CarrierTransferNumber = config.clientConfig.CarrierTransferNumber;
    toReturn.MaxValidAbsenceDays = config.clientConfig.MaxTotalMissedAbsenceDays ?? 60;
    toReturn.MaxBeforeAbsenceDays = config.clientConfig.MaxAllowAbsenceDaysPast ?? 2;
    toReturn.PromptMaxRetrySSML = config.getPromptText(config.clientConfig.MaxRetryText, language) ?? '';
    toReturn.PromptUnexpectedErrorSSML = config.getPromptText(config.clientConfig.UnexpectedErrorText, language) ?? '';
    toReturn.PromptIsRTWKnownSSML = config.getPromptText(config.clientConfig.IsRtwKnownText, language) ?? '';
    toReturn.PromptEnterAbsenceDateSSML = config.getPromptText(config.clientConfig.EnterAbsenceDateText, language) ?? '';
    toReturn.PromptIsAMorPMSSML = config.getPromptText(config.clientConfig.IsAMorPMText, language) ?? '';
    toReturn.PromptInvalidEntryAndRetrySSML = config.getPromptText(config.clientConfig.InvalidEntryAndRetryText, language) ?? '';
    toReturn.PromptInvalidAbsenceDateSSML = config.getPromptText(config.clientConfig.InvalidAbsenceDateText, language) ?? '';
    toReturn.PromptTransferToDisconnectSSML = config.getPromptText(config.clientConfig.TransferToDisconnectText, language) ?? '';
    toReturn.PromptAuthEmployeeFailSSML1 = config.getPromptText(config.clientConfig.AuthEmployeeFailText1, language) ?? '';
    toReturn.PromptAuthEmployeeFailSSML2 = config.getPromptText(config.clientConfig.AuthEmployeeFailText2, language) ?? '';
    toReturn.PromptEnterTotalAbsenceDaysSSML1 = config.getPromptText(config.clientConfig.EnterTotalAbsenceDaysText, language, true, [{ key: Constants.MERGECODE_TOTAL_ABSENCEDAYS, value: toReturn.MaxTotalMissedAbsenceDays }],) ?? '';
    toReturn.PromptSecondaryReasonSpecialMsgSSML = config.getPromptText(config.clientConfig.SecondaryReasonSpecialText, language) ?? '';
    toReturn.PromptValidateIDandDobFailSSML = config.getPromptText(config.clientConfig.InvalidAuthenticationText, language) ?? '';
    toReturn.PromptInvalidReturnToWorkSSML = config.getPromptText(config.clientConfig.InvalidRtwDateText, language) ?? '';
    toReturn.PromptFailTrasnferToCarrierSSML = config.getPromptText(config.clientConfig.FailTransferToCarrierText, language) ?? '';
    toReturn.PromptConfirmAbsenceDateSSML1 = config.getPromptText(config.clientConfig.ConfirmAbseneDate1Text, language) ?? '';
    toReturn.IsRTWTimeRequiredForIVR = config.clientConfig.IsRtwShiftTimeEnabled ?? false;
    toReturn.PromptTransferToCarrierSSML = config.getPromptText(config.clientConfig.TransferToCarrierText, language) ?? '';
    toReturn.PromptEnterStartShiftTimeSSML = config.getPromptText(config.clientConfig.EnterShiftStartText, language) ?? '';
    toReturn.PromptEnterEndShiftTimeSSML = config.getPromptText(config.clientConfig.EnterShiftEndText, language) ?? '';
    toReturn.PromptTransferToAgentSSML = config.getPromptText(config.clientConfig.TransferToAgentText, language) ?? '';
    toReturn.PromptProvideConfirmNumberSSML2 = config.getPromptText(config.clientConfig.ConfirmationNumber2Text, language) ?? '';
    toReturn.PromptEnterEmployeePhoneNumberSSML = config.getPromptText(config.clientConfig.EmployeePhoneNumberText, language) ?? '';
    toReturn.PromptEnterReturnToWorkSSML = config.getPromptText(config.clientConfig.EnterRtwDateText, language) ?? '';
    toReturn.PromptConfirmReturnToWorkSSML1 = config.getPromptText(config.clientConfig.ConfirmRtwDateText, language) ?? '';
    toReturn.PromptFullorPartialAbsenceSSML = config.getPromptText(config.clientConfig.IntermittentContinuousText, language) ?? '';
    toReturn.PromptEnterFirstAbsenceDateSSML = config.getPromptText(config.clientConfig.FdaText, language) ?? '';
    toReturn.PromptEnterLastAbsenceDateSSML = config.getPromptText(config.clientConfig.LdaText, language) ?? '';
    toReturn.EnableVoicemail = config.clientConfig.EnableVoicemail ?? false;
    toReturn.PromptLeaveVoicemailSSML = config.getPromptText(config.clientConfig.VoiceMailOptionText, language) ?? '';
    toReturn.PromptRecordMessageSSML = config.getPromptText(config.clientConfig.VoiceMailRecordingText, language) ?? '';
    toReturn.CallFlowGetOpenLeaveCaseARN = callFlowArns.CallFlowGetOpenLeaveCaseARN;
    toReturn.CallFlowSTDLeaveARN = callFlowArns.CallFlowSTDLeaveARN;
    toReturn.CallFlowGetOpeningMsgV1ARN = callFlowArns.CallFlowGetOpeningMsgV1ARN ?? '';
    toReturn.CallFlowDynamicFlowV1ARN = callFlowArns.CallFlowDynamicFlowV1ARN;
    toReturn.CallFlowValidateEmployeeARN = callFlowArns.CallFlowValidateEmployeeARN;
    toReturn.CallFlowEnterTotalAbsenceDaysARN = callFlowArns.CallFlowEnterTotalAbsenceDaysARN;
    toReturn.CallFlowEnterReasonARN = callFlowArns.CallFlowEnterReasonARN;
    toReturn.CallFlowEnterAbsenceDateARN = callFlowArns.CallFlowEnterAbsenceDateARN;
    toReturn.CallFlowEnterRTWARN = callFlowArns.CallFlowEnterRTWARN;
    toReturn.CallFlowEnterEmployeePhoneNumberARN = callFlowArns.CallFlowEnterEmployeePhoneNumberARN;
    toReturn.CallFlowSubmitAbsenceARN = callFlowArns.CallFlowSubmitAbsenceARN;
    toReturn.CallFlowSubmitAbsenceIfErrorARN = callFlowArns.CallFlowSubmitAbsenceIfErrorARN;
    toReturn.CallFlowTransferToExternalARN = callFlowArns.CallFlowTransferToExternalARN;
    toReturn.CallFlowTransferToCSRARN = callFlowArns.CallFlowTransferToCSRARN;
    toReturn.CallFlowClosingARN = callFlowArns.CallFlowClosingARN;
    toReturn.CallFlowEnterNextAbsenceDateARN = callFlowArns.CallFlowEnterNextAbsenceDateARN;
    toReturn.CallFlowLinkAbsenceInAbilitiARN = callFlowArns.CallFlowLinkAbsenceInAbilitiARN; 
    toReturn.CallFlowDynamicFlowV2ARN = callFlowArns.CallFlowDynamicFlowV2ARN; 
    toReturn.CallFlowEnterAbsenceDateV2ARN = callFlowArns.CallFlowEnterAbsenceDateV2ARN; 
    toReturn.CallFlowEnterNextAbsenceDateV2ARN = callFlowArns.CallFlowEnterNextAbsenceDateV2ARN; 
    toReturn.CallFlowEnterDateRange1ARN = callFlowArns.CallFlowEnterDateRange1ARN; 
    toReturn.CallFlowEnterDateRange2ARN = callFlowArns.CallFlowEnterDateRange2ARN;
    toReturn.CallFlowVoicemailMessageARN = callFlowArns.CallFlowVoicemailMessageARN;  
    toReturn.LambdaGetOpeningMsgV1ARN = callFlowArns.LambdaGetOpeningMsgV1ARN;
    toReturn.LambdaGetClientConfigARN = callFlowArns.LambdaGetClientConfigARN;
    toReturn.LambdaValidateEmployeeIdARN = callFlowArns.LambdaValidateEmployeeIdARN;
    toReturn.LambdaValidateDoBARN = callFlowArns.LambdaValidateDoBARN;
    toReturn.LambdaAuthenticateEmployeeARN = callFlowArns.LambdaAuthenticateEmployeeARN;
    toReturn.LambdaSetReturnToWork = callFlowArns.LambdaSetReturnToWork;
    toReturn.LambdaSaveTotalAbsenceDaysARN = callFlowArns.LambdaSaveTotalAbsenceDaysARN;
    toReturn.LambdaGetPrimaryReasonMenuARN = callFlowArns.LambdaGetPrimaryReasonMenuARN;
    toReturn.LambdaGetSecondaryReasonMenuARN = callFlowArns.LambdaGetSecondaryReasonMenuARN;    
    toReturn.LambdaSaveSecondaryReasonARN = callFlowArns.LambdaSaveSecondaryReasonARN;
    toReturn.LambdaGetCarrierTransferNumberARN = callFlowArns.LambdaGetCarrierTransferNumberARN;
    toReturn.LambdaValidateAbsenceDateARN = callFlowArns.LambdaValidateAbsenceDateARN;
    toReturn.LambdaSaveAbsenceDateARN = callFlowArns.LambdaSaveAbsenceDateARN;
    toReturn.LambdaValidateShiftStartTimeARN = callFlowArns.LambdaValidateShiftStartTimeARN;
    toReturn.LambdaValidateShiftEndTimeARN = callFlowArns.LambdaValidateShiftEndTimeARN;
    toReturn.LambdaCheckAllAbsReportedARN = callFlowArns.LambdaCheckAllAbsReportedARN;
    toReturn.LambdaValidateReturnToWorkDateARN = callFlowArns.LambdaValidateReturnToWorkDateARN;
    toReturn.LambdaValidateRTWShiftStartTimeARN = callFlowArns.LambdaValidateRTWShiftStartTimeARN;
    toReturn.LambdaValidateEmployeePhoneNumberARN = callFlowArns.LambdaValidateEmployeePhoneNumberARN;
    toReturn.LambdaSubmitAbsenceARN = callFlowArns.LambdaSubmitAbsenceARN;
    toReturn.LambdaResolveConflictAbsenceARN = callFlowArns.LambdaResolveConflictAbsenceARN;
    toReturn.LambdaSetReturnToWorkARN = callFlowArns.LambdaSetReturnToWorkARN;
    toReturn.LambdaSaveReturnToWorkDateARN = callFlowArns.LambdaSaveReturnToWorkDateARN;
    toReturn.LambdaClearLastAddedAbsenceDateARN = callFlowArns.LambdaClearLastAddedAbsenceDateARN;
    toReturn.LambdaDroppedSessionMonitorARN = callFlowArns.LambdaDroppedSessionMonitorARN;
    toReturn.LambdaCheckOpenCasesARN = callFlowArns.LambdaCheckOpenCasesARN;
    toReturn.LambdaCheckEESTDARN = callFlowArns.LambdaCheckEESTDARN;
    toReturn.LambdaSaveSTDResponseARN = callFlowArns.LambdaSaveSTDResponseARN;
    toReturn.LambdaSaveThirdLevelResponseARN = callFlowArns.LambdaSaveThirdLevelResponseARN;
    toReturn.LambdaLinkAbsenceInAbilitiARN = callFlowArns.LambdaLinkAbsenceInAbilitiARN;
    toReturn.LambdaSaveLeaveResponseARN = callFlowArns.LambdaSaveLeaveResponseARN;
    toReturn.LambdaVMCallbackARN = callFlowArns.LambdaVMCallbackARN;
}


