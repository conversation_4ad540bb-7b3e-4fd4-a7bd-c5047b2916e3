export  interface AuthResponse {
    IsEmployeeValid?: boolean;
    IsDoBValid?: boolean;
    LastAbsenceDate?: string | null;
    IsPrevAbsWithinAADays?: boolean;
    ValidationMessage?: string | null;
    AA?: string | number;
    PromptIsReturnToWorkSSML1?: string | null;
}

export interface RtwResponse{
    IsSaveSuccess?: boolean ,
    IsRTWValid?: boolean |null,
    ReturnDayToWorkplayable?: string | null,
    RTWNotValidReason?: string | null
    PromptConfirmReturnToWorkSSML1?: string | null
}

