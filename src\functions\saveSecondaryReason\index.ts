import { ReasonMenuService, ScopedHttpService, CallLog, AbsenceReportingService, AbsenceApiService, LocalConfiguration } from '@/services';
import { ConnectContactFlowEvent } from 'aws-lambda';
import { ReasonWrapper } from '@/models/ReasonModel';
import { AbsenceConfig, AbsenceIncident, PageModel, PageSectionModel, QuestionModel, ReasonFlow } from '@/models/AbsenceModel';
import { ClaimStatus, Constants, PromptHelper } from '@/util';
import { ClientConfigModel, ClientConfigWrapper, EmployeeData, EmployeeDataDetails } from '@/models';
import { GetCountry, GetProvinceCode } from '@/models/EmployeeData';
import { SimpleObject } from '@/types';
import * as he from 'he';

const reasonConfig = new ReasonMenuService();
const callService = new CallLog();
const absenceService = new AbsenceReportingService();
const absenceApiService = new AbsenceApiService();
const localConfig = new LocalConfiguration();
absenceService.CallLog = callService;
export const handler = async (event: ConnectContactFlowEvent) => {
  const lambdaName = 'saveSecondaryReason';
  reasonConfig.Logger.log("PERFORMANCE: saveSecondaryReason start");

  reasonConfig.Logger.log('Input data', event);

  const clientCode = event.Details.Parameters.ClientCode;
  const contactId = event.Details.ContactData.ContactId;
  const selectedOption: number = parseInt(event.Details.Parameters.SelectedSecondaryReason,);
  
  let result ={
    IsSaveSuccess: false,
    IsExternalTransfer: false,
    IsPlayClaimsDetail: false,
    PromptAlertSSML: '',
    PromptThirdLevelMenuOptionsSSML:'',
    NumberOfOptions: '',
    OptionToRpeat: '',
    PromptSpecialMsgSSML:'',
    PromptIntroThirdLevelMenuSSML: '',
    IsThirdLevelMenuRequired: false,
    IsSTDCheck: false,
    AbsenceRecordStatus: ClaimStatus.InProgress
  }
  let thirdLevelMenu = {
    QuestionName: '',
    NumberOfOptions: '',
    PromptIntroThirdLevelMenuSSML: '',
    PromptThirdLevelMenuOptionsSSML: '',
    IsThirdLevelMenuRequired: false,
    Options: {}
  };
  
  let callData = await callService.getCallDataAsync(contactId);
  reasonConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
  localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);

  let employeeNumber: string = '';
  if (callData && callData.EmployeeInfo)
  {
    employeeNumber = callData.EmployeeInfo.employeeNumber;
  }

  let primaryReasons: ReasonWrapper = await reasonConfig.getReasonMenuAsync(clientCode, employeeNumber);
  let reasonFlow: ReasonFlow = callData.Reasons;
  const selectedLang = callData.SelectedLang ?? 'EN';
  let absConfig : AbsenceConfig = {};
  const configuration = await localConfig.getClientConfigAsync(callData.ClientCode);
  
  if(!Number.isNaN(selectedOption))
  {
    let selectedItem = primaryReasons.traverseReasons(reasonFlow, selectedOption);
    absConfig = selectedItem.AbsenceConfig;
    reasonFlow = ReasonMenuService.advanceFlow(reasonFlow, selectedItem, selectedOption,selectedLang);
    result.IsExternalTransfer = selectedItem.answerModel.Ar3IvrReasonSettings?.IsTransfer ?? false;
    result.IsSTDCheck = (selectedItem.answerModel.AnswerCode != null || undefined) && (configuration.clientConfig.EnableStdCreation ??false) ? true: false;
    result.IsPlayClaimsDetail =  (selectedItem.answerModel.AnswerCode != null || undefined) && (configuration.clientConfig.EnableLeaveCreation ??false) ? true: false;
  }
  else{
    let selectedOption = reasonFlow.ReasonEntries[0].IVROptionNumber;
    let selectedItem = primaryReasons.WrappedAnswers[+selectedOption - 1];
    result.IsExternalTransfer = selectedItem.answerModel.Ar3IvrReasonSettings?.IsTransfer ?? false;
    result.IsSTDCheck = (selectedItem.answerModel.AnswerCode != null || undefined) && (configuration.clientConfig.EnableStdCreation ??false) ? true: false;
    result.IsPlayClaimsDetail =  (selectedItem.answerModel.AnswerCode != null || undefined) && (configuration.clientConfig.EnableLeaveCreation ??false) ? true: false;
  }
 
  let promptAlert = '';
  if((callData.Absences ||  (Array.isArray(callData.Absences) && callData.Absences.length == 0) && callData.IsSubmitted == ClaimStatus.Created) )
  {
    promptAlert = he.decode(callData.Absences.slice(0,1).shift().AlertMessages?.peekLast()?.AlertText.find((x:any) => x?.Locale.toLowerCase() == selectedLang.toLowerCase())?.Text || '');

    thirdLevelMenu = getThirdLevelMenu(callData.Absences.peekLast().QuestionReponse, selectedLang, configuration);
        
    if(callData.Absences.length > 1)  callData.Absences.pop();
    await callService.setCallDataAsync(contactId,callData);
  }
  else if(callData.IsSubmitted == ClaimStatus.InProgress)
  {
    await new Promise(resolve => setTimeout(resolve, 3000));
            return {
              AbsenceRecordStatus: ClaimStatus.InProgress
            }
  }
  else 
  {
      await callService.setCallDataAsync(contactId, {
        IsSaved: "true",
        IsSubmitted: ClaimStatus.InProgress
        });
      const newIncident: AbsenceIncident = await absenceService.generateAbsenceIncident(contactId, clientCode, reasonFlow.ReasonEntries[reasonFlow.Count-1], selectedLang);
      newIncident.ReasonConfig = absConfig;
      let savedIncident =  await absenceApiService.createIncidentAsync(clientCode, contactId, newIncident);
      promptAlert = he.decode(savedIncident.AlertMessages?.peekLast()?.AlertText.find((x) => x?.Locale.toLowerCase() == selectedLang.toLowerCase())?.Text || '');
      if(savedIncident.QuestionReponse && savedIncident.QuestionReponse.length > 0)
      {
        reasonConfig.Logger.log("After creation the question responses", JSON.stringify(savedIncident.QuestionReponse));
        let leaveQuestions: any = savedIncident.QuestionReponse?.filter((page: PageModel) => page.PageId === Constants.FIXED_TRIGGER_QUESTION_PAGE_ID).peekLast()  ;
            let section: PageSectionModel = leaveQuestions?.Sections?.filter((sec: PageSectionModel) => sec.SectionId === Constants.FIXED_TRIGGER_QUESTION_PAGE_ID).peekLast();

            //Leave association question
            let leaveAssociationQ = section?.Questions?.filter((q) => q.Name === `${Constants.CompanySpecificQuestions_Prefix}${Constants.AskForExistingLeaveCase}`).peekLast();
             
        reasonConfig.Logger.log("After creation the association leave question responses", JSON.stringify(leaveAssociationQ ?? ''));
        //build warning msg response
        thirdLevelMenu = getThirdLevelMenu(savedIncident.QuestionReponse, selectedLang, configuration);
        
      }
    
      savedIncident = { ...newIncident, ...savedIncident };
      if (callData.Absences && Array.isArray(callData.Absences)){
        callData.Absences.push(savedIncident.AbsenceIncident);
      }

      await callService.setCallDataAsync(contactId, {
        ThirdLevelMenu: thirdLevelMenu,
        Reasons: reasonFlow,
        IsSaved: "true",
        Absences: callData.Absences ?? [savedIncident],
        IsSubmitted: ClaimStatus.Created
      });
  }
  
  result.IsSaveSuccess = true;
  result.PromptAlertSSML = PromptHelper.wrapSSML([promptAlert]);
  result.NumberOfOptions = thirdLevelMenu.NumberOfOptions;
  result.PromptIntroThirdLevelMenuSSML = thirdLevelMenu.PromptIntroThirdLevelMenuSSML;
  result.PromptThirdLevelMenuOptionsSSML = thirdLevelMenu.PromptThirdLevelMenuOptionsSSML;
  result.IsThirdLevelMenuRequired = thirdLevelMenu.IsThirdLevelMenuRequired;
  result.AbsenceRecordStatus = ClaimStatus.Success;
  reasonConfig.Logger.log('Result', result);
  reasonConfig.Logger.log("PERFORMANCE: saveSecondaryReason END");
  return result;
  
};

function getThirdLevelMenu( questions: any, selectedLang: string, configuration: ClientConfigWrapper ){
  let result = {
    QuestionName:"",
    IsThirdLevelMenuRequired: false,
    NumberOfOptions: '',
    PromptIntroThirdLevelMenuSSML: '',
    PromptThirdLevelMenuOptionsSSML: '',
    Options: {}
    
  }
  if(configuration.clientConfig.SpecialMessageEnabled ?? false)
  {
    reasonConfig.Logger.log('inside getThirdLevelMenu: third Lvl message enabled');
    let companyQuestions: any = questions?.filter((question: PageModel) => question.PageId === Constants.COMPANY_QUESTION_PAGE_ID).peekLast()  ;
    let section: PageSectionModel = companyQuestions.Sections?.filter((sec: PageSectionModel) => sec.SectionId === Constants.COMPANY_QUESTION_PAGE_ID).peekLast();
    //assuming that the question which has AbsenceSource=IVR in criteria has been returned in case of multiple org question
    let thirdLvlQuestion = section.Questions?.peekLast();
    let thirdLvlQuestionText = thirdLvlQuestion != undefined? (thirdLvlQuestion.Label.length > 0 ? configuration.getPromptText(thirdLvlQuestion.Label, selectedLang):''): '';
    result.QuestionName = thirdLvlQuestion != undefined? thirdLvlQuestion.Name : "";
    reasonConfig.Logger.log('inside getThirdLevelMenu: thirdLvlQuestion returned after submitting', thirdLvlQuestion);
    let t: SimpleObject = {};
    let optText :string[]= [];
        //optionTExt = "if {optiontext}, press {number}"
        if(thirdLvlQuestion != undefined && Array.isArray(thirdLvlQuestion.Options)){
          result.NumberOfOptions = thirdLvlQuestion.Options ? thirdLvlQuestion.Options.length.toString() : '';
          
          thirdLvlQuestion.Options.forEach((x, idx) => {
           let txt: string = configuration.getPromptText(configuration.clientConfig.WarningMsgOptionSelectionText, selectedLang, false);
           txt = txt.replace('{OptionText}', configuration.getPromptText(x.Text, selectedLang, false)).replace('{Number}', (idx+1).toString());           
           optText.push(txt);
           t[(idx+1)] = x.Value;
        });
        result.Options = t;
        result.PromptThirdLevelMenuOptionsSSML = PromptHelper.wrapSSML(optText);
        result.IsThirdLevelMenuRequired = true;
        }
    result.PromptIntroThirdLevelMenuSSML = thirdLvlQuestionText;      
   
  }
  reasonConfig.Logger.log("inside getThirdLevelMenu: result for 3rd level menu", result);
  return result;
}
