import * as config  from './responses.json'
import sinon from 'sinon'
import axios, { AxiosStatic, AxiosInstance, Axios } from 'axios'
import { MockApiConfig, MockEntry } from '.'
import {instance}  from '../../../src/services/mixins/HttpClient' //TODO investigate/refactor...using both static and scoped for now
import {AbsenceIncident} from '../../../src/models'
const mockObj = config;
/**
 * absenceApiEndpoint="https://abiliti-api-connect.qa.morneaushepell.com/api/v1/absenceincident/"
eeApiEndpoint="https://abiliti-api-connect.qa.morneaushepell.com/api/v1/"
configApiEndpoint="https://abiliti-api-config.qa.morneaushepell.com/api/v1/"
authApiUrl="https://abiliti-api-auth.qa.morneaushepell.com/connect/token"
 */
let get: any;
let post: any;
let put: any;
let instanceGet: any;
let instancePost: any;
let instancePut: any;

export function MockAll() {
 sinon.restore();
 sinon.createSandbox();
 get = sinon.stub(axios, 'get');
 post = sinon.stub(axios, 'post');
 put = sinon.stub(axios, 'put');
 instanceGet = sinon.stub(instance, 'get')
 instancePost = sinon.stub(instance, 'post')
 instancePut = sinon.stub(instance, 'put')

 Mock(process?.env?.absenceApiEndpoint || "https://abiliti-api-connect.qa.morneaushepell.com/api/v1/absenceincident/", <MockEntry[]>mockObj.absenceApiEndpoint.GET, 'get')
 Mock(process?.env?.absenceApiEndpoint || "https://abiliti-api-connect.qa.morneaushepell.com/api/v1/absenceincident/", <MockEntry[]>mockObj.absenceApiEndpoint.POST, 'post')
 Mock(process?.env?.absenceApiEndpoint || "https://abiliti-api-connect.qa.morneaushepell.com/api/v1/absenceincident/", <MockEntry[]>mockObj.absenceApiEndpoint.PUT, 'put')

 Mock(process?.env?.eeApiEndpoint || "https://abiliti-api-connect.qa.morneaushepell.com/api/v1/", <MockEntry[]>mockObj.eeApiEndpoint.GET, 'get')
 Mock(process?.env?.eeApiEndpoint || "https://abiliti-api-connect.qa.morneaushepell.com/api/v1/", <MockEntry[]>mockObj.eeApiEndpoint.POST, 'post')

 Mock(process?.env?.configApiEndpoint || "https://abiliti-api-config.qa.morneaushepell.com/api/v1/", <MockEntry[]>mockObj.configApiEndpoint.GET, 'get')
 Mock(process?.env?.configApiEndpoint || "https://abiliti-api-config.qa.morneaushepell.com/api/v1/", <MockEntry[]>mockObj.configApiEndpoint.POST, 'post')

 Mock(process?.env?.authApiUrl || "https://abiliti-api-connect.qa.morneaushepell.com/api/v1/", <MockEntry[]>mockObj.authApiUrl.GET, 'get')
 Mock(process?.env?.authApiUrl || "https://abiliti-api-connect.qa.morneaushepell.com/api/v1/", <MockEntry[]>mockObj.authApiUrl.POST, 'post')
}

export function TearDown() {
 sinon.restore();
}

function Mock(url: string, entries: MockEntry[], method: string) {
entries.forEach((e) => {
  ////&console.log(JSON.stringify(e));
  if (method === 'post') {
    post.withArgs(`${url}${e.endpoint}`, e?.calledWith?.payload|| sinon.match.any, e?.calledWith?.config).returns(Promise.resolve({ data: e.data}));
    instancePost.withArgs(`${url}${e.endpoint}`, e?.calledWith?.payload|| sinon.match.any, e?.calledWith?.config).returns(Promise.resolve({ data: e.data}));
  }
  if (method === 'get') {
    get.withArgs(axios, method).withArgs(`${url}${e.endpoint}`, e?.calledWith?.config).returns(Promise.resolve({ data: e.data}));
    instanceGet.withArgs(instance, method).withArgs(`${url}${e.endpoint}`, e?.calledWith?.config).returns(Promise.resolve({ data: e.data}));
  }
  if (method === 'put') {
    get.withArgs(axios, method).withArgs(`${url}${e.endpoint}`, e?.calledWith?.config).returns(Promise.resolve({ data: e.data}));
    instanceGet.withArgs(instance, method).withArgs(`${url}${e.endpoint}`, e?.calledWith?.config).returns(Promise.resolve({ data: e.data}));
  }
})
}


////&console.log(mockObj.authApiUrl);
//MockAll();
export const flowEvent: any = {
  Details: {
    ContactData: {
      Attributes: {},
      ContactId: 'k6gkw3e3-a66b-4362-af33-65c752a6844d',
    },
    Parameters: {
      ClientCode: 'GOODYEAR',
      EmployeeId: '80083033',
      DateOfBirth: '01011980',
    },
  },
};

export const generateDate = (daysInFuture: number = 0) => {
        let d = new Date();
        d.setDate(d.getDate() + daysInFuture)
        
        let mm = d.getMonth()+1;
        let dd = d.getDate();
        let yyyy = d.getFullYear();
      
        let dateString = `${mm<10? `0${mm}` : mm }${dd}${yyyy}`;
        return dateString;
}

  
