import {  BaseService } from '@/services'
import { PinpointClient, PhoneNumberValidateCommand } from "@aws-sdk/client-pinpoint";
import { HttpClient } from './mixins';



export default class AmazonPinpoint extends HttpClient(BaseService) {
    public async isEmployeePhoneValid(employeePhoneNum: string): Promise<any> {
    try{  
        
        const payload = JSON.stringify({
            PhoneNumber: employeePhoneNum,
            IsoCountryCode: "US"
        });
        const region:string =  process.env.AWS_REGION || '';
        
        const input = {
            NumberValidateRequest:{
            PhoneNumber: employeePhoneNum,
            IsoCountryCode: "US"
            }
        }
         const client = new PinpointClient({region: region});
        const command = new PhoneNumberValidateCommand(input);
        const response = await client.send(command); 
        return response?.NumberValidateResponse   ;
    }catch(error: any){
        this.Logger.error('AmazonPinpoint error', error);
    }
    }

}