import { DocumentManagementEntityTypes } from "@/util"

export interface ParsedVoiceMail{
    Body: string,
    Subject: string,
    Attachment: AttachmentModel[],
}

export interface AttachmentModel{
    FileName: string,
    ContentType: string,
    Content: Buffer,
    Size: number
}
export interface UploadDocumentWithModel{
    Data: UploadDocumentData,
    File: File
}
export interface UploadDocumentData{
    EntityId?: string, //this the absence incident id
    EntityType?: DocumentManagementEntityTypes,
    Description?: string,
    AdditionalMetaData?: string,
    Source?: string
}

export interface UploadDocumentReturnModel{
    ValidationErrors: ValidationErrors[],
    Success: boolean,
    DocumentId: string
}
export interface ValidationErrors{
    ErrorCode: string,
    ErrorDescription: string,
    ErrorSeverity: number,
    ErrorProvider: string,
    ErrorParameters?: ErrorParameter | null,
    ErrorFields?: string[],
    MetaData?: any
  }
  
  interface ErrorParameter{
    PropertyName: string | null,
    PropertyValue: any
  }