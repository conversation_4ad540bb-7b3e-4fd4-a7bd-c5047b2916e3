'use strict';

import {ConnectContactFlowEvent} from 'aws-lambda';
import {AbsenceApiService, AbsenceReportingService, CallLog, LocalConfiguration, ScopedHttpService} from '@/services';
import {LogService, DateHelper, ValidationErrors, PromptHelper, Constants, RefAbsenceType} from '@/util';
import { AbsenceDates, AbsenceIncident, ClientConfigModel } from '@/models';
import moment from 'moment';

const callService = new CallLog();
const dateHelperService = new DateHelper();
const log = new LogService({ logToConsole: process.env.Debug });
const localConfig = new LocalConfiguration();
const absenceApi = new AbsenceApiService();
export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'validateShiftStartTime';
    
    callService.Logger.log('Input data', event);
    let contactId;
    let isValid: boolean;
    const result = {
        IsShiftStartTimeValid: false,
        IsSaveSuccess: false,
        ShiftTimeValidation: true,
        PromptStartTimeValidationSSML: ''
    };
    try {
        contactId = event.Details.ContactData.ContactId;
        const shiftStartTime = event.Details.Parameters.ShiftStartTime;

        const aMorPM = +event.Details.Parameters.AMorPM === 1 ? 'AM' : 'PM';
        localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
        if (isValid = shiftStartTime.length === 4) {
            const [hour, min] = [shiftStartTime.slice(0,2), shiftStartTime.slice(2,4)];

        isValid = isValid && dateHelperService.is12HClockCorrect(parseInt(hour), parseInt(min), aMorPM === 'AM');
        result.IsShiftStartTimeValid = isValid;
        callService.Logger.log(`${hour} hour, ${min} min is valid 12h clock time: ${isValid}`);
        
        const callData = await callService.getCallDataAsync(contactId);
        const currentIncident = (callData.Absences)?.peekLast();
        let selectedLang = callData.SelectedLang ?? 'en';
        if (isValid && currentIncident.AbsenceIncident && currentIncident.AbsenceIncident.AbsenceDates) {
          const lastReported = currentIncident.AbsenceIncident.AbsenceDates.peekLast();
          if (lastReported) {
            const inputTime = `${shiftStartTime} ${aMorPM}`;
            const StartDate = dateHelperService.convertTo24HClock(inputTime);
            callService.Logger.log( `Entered time ${inputTime} converted to 24h format: ${StartDate}`,);
              await AbsenceReportingService.updateAbsenceDate(lastReported, { StartDate });      
              let config = await localConfig.getClientConfigAsync(callData.ClientCode);
              let shiftDurationInMin = moment(lastReported.ShiftStartTime).minute();
              callService.Logger.log(`shift duration: ${shiftDurationInMin} minutes`);
              if (shiftDurationInMin % (config.clientConfig.AbsenceDurationMinutes ?? 15) != 0) {
                  result.IsShiftStartTimeValid = false;
                  result.IsSaveSuccess = false;
                  callService.Logger.log('Result', result);
                  return result;
              };
              
              let maxShiftLengthThresholdInSec = (config.clientConfig?.MaxShiftLengthThresholdInMin ?? 1439) * 60;
            lastReported.ShiftEndTime = moment(lastReported.ShiftStartTime).add(maxShiftLengthThresholdInSec, 's').format('YYYY-MM-DD HH:mm:ss');
            lastReported.EndDate = moment(lastReported.ShiftStartTime).add(maxShiftLengthThresholdInSec, 's').format('YYYY-MM-DD HH:mm:ss');

            if((config.clientConfig.EnableMultipleShift ?? false) )
            {
                let minShiftLengthThresholdInSec = (config.clientConfig.AbsenceDurationMinutes ?? 15) * 60;
                lastReported.ShiftEndTime = moment(lastReported.ShiftStartTime).add(minShiftLengthThresholdInSec, 's').format('YYYY-MM-DD HH:mm:ss');
                lastReported.EndDate = moment(lastReported.ShiftStartTime).add(minShiftLengthThresholdInSec, 's').format('YYYY-MM-DD HH:mm:ss');
              callService.Logger.log('Multiple shift enabled- input for api', lastReported);
              var validShift = await absenceApi.validateAbsenceShift(contactId, currentIncident.AbsenceIncident.AbsenceIncidentId,lastReported );
              callService.Logger.log('Conflict shift checked with result', JSON.stringify(validShift));
              if(!validShift.Success)
              {
                let conflictAbsenceDt = validShift.ValidationErrors?.filter(e => e.ErrorCode == Constants.Error_Code_Absence_Shift_Start)?.peekLast()?.MetaData;                
                if(conflictAbsenceDt == null || undefined )
                    conflictAbsenceDt = validShift.ValidationErrors?.filter(e => e.ErrorCode == Constants.Error_Code_Absence_WholeShift)?.peekLast()?.MetaData;
                if(conflictAbsenceDt != null || undefined){
                let startTimeAMPM = moment.parseZone(conflictAbsenceDt.startDate).format('hh:mm A');
                callService.Logger.log('conflict shift start time as ', startTimeAMPM);
                let endTimeAMPM = moment.parseZone(conflictAbsenceDt.endDate).format('hh:mm A');
                callService.Logger.log('conflict shift end time as ', endTimeAMPM);
                let conflictPromt = PromptHelper.getMergedPrompt(config.clientConfig.ConflictingAbsenceShiftText ?? [],selectedLang,  "{AbsenceDate}", moment.parseZone(conflictAbsenceDt.startDate, ).startOf('day').format("YYYY-MM-DD"), 'date', true);
                
                conflictPromt = conflictPromt.replaceAll('{ShiftStartTime}', startTimeAMPM).replace('{ShiftEndTime}', endTimeAMPM);
                result.PromptStartTimeValidationSSML = conflictPromt;
                result.ShiftTimeValidation = false;
                result.IsSaveSuccess = false;
                callService.Logger.log('Result', result);
                return result;
                }
              }
            }
            (callData.Absences).pop();
            callData.Absences.push(currentIncident);
            result.IsSaveSuccess = true;
          }
          await callService.setCallDataAsync(contactId, {
            IsSaved: "true",
            Absences: callData.Absences,
          }); 
        }
    }

        //await callService.logStageAsync(
        //    contactId,
        //    lambdaName,
        //    result.IsShiftStartTimeValid ? null : { Validation: ValidationErrors.InvalidShiftStartTime },
        //    result
        //);

        callService.Logger.log('Result:', result);
        return  result;
    } catch (error: any) {
        callService.Logger.error('ValidateShiftStartTime Error:', error);
        
        let result = {
            IsShiftStartTimeValid: false,
        }
        return result;
    }
};
