{"name": "arivr", "version": "1.0.0", "description": "IVR Lambda library", "main": "server.js", "scripts": {"alias": "tsc-alias", "buildlambda": "esbuild ./src/functions/authenticateEmployee/index.ts --bundle --minify --sourcemap --platform=node --target=es2020 --outfile=dist/index.js", "clearDependencies": "rm -rf node_modules && rm -rf build", "build-production": "npm run clearDependencies && npm install --production && tsc ", "build": "rimraf ./build && tsc --project ./ && tsc-alias", "start:dev": "npx nodemon", "start": "npm run build && node -r tsconfig-paths/register build/src/devserver/server.js", "lint": "eslint . --ext .ts", "aws-debug": "npm run build && SET SLS_DEBUG=* && node --inspect C:\\\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\serverless\\bin\\serverless offline -s dev", "prettier-format": "run-script-os", "prettier-format:win32": "prettier --config .prettierrc \"./src/**/*.ts\" --write", "prettier-format:darwin:linux": "prettier --config .prettierrc 'src/**/*.ts' --write", "prettier-format:default": "prettier --config .prettierrc 'src/**/*.ts' --write", "prettier-watch": "run-script-os", "prettier-watch:win32": "onchange \"src/**/*.ts\" -- prettier --write {{changed}}", "prettier-watch:darwin:linux": "onchange 'src/**/*.ts' -- prettier --write {{changed}}", "prettier-watch:default": "onchange 'src/**/*.ts' -- prettier --write {{changed}}", "test": "jest", "test:dev": "jest --watchAll"}, "husky": {"hooks": {"pre-commit": "npm run test && npm run prettier-format && npm run lint"}}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@automapper/classes": "^8.7.7", "@automapper/core": "^8.7.7", "@babel/preset-typescript": "^7.18.6", "@types/express": "^4.17.21", "@types/node": "^18.19.100", "@types/sinon": "^10.0.13", "@typescript-eslint/eslint-plugin": "^5.31.0", "@typescript-eslint/parser": "^5.31.0", "aws-sdk": "^2.1318.0", "babel": "^6.23.0", "esbuild": "^0.25.1", "eslint": "^8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^26.6.0", "eslint-plugin-prettier": "^4.2.1", "express": "^4.21.2", "husky": "^8.0.1", "i": "^0.3.7", "jest": "^29.7.0", "jest-cucumber": "^4.5.0", "jest-dynalite": "^3.6.1", "nodemon": "^2.0.19", "onchange": "^7.1.0", "prettier": "^2.7.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "run-script-os": "^1.1.6", "sinon": "^15.0.1", "ts-jest": "^29.2.6", "ts-node": "^10.9.2"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.272.0", "@aws-sdk/client-pinpoint": "^3.281.0", "@aws-sdk/client-s3": "^3.637.0", "@aws-sdk/lib-dynamodb": "^3.272.0", "@types/aws-lambda": "^8.10.110", "@types/axios": "^0.14.0", "@types/he": "^1.2.3", "@types/jest": "^29.5.14", "@types/mailparser": "^3.4.4", "@types/uuid": "^9.0.1", "axios": "^1.3.2", "dotenv": "^16.0.3", "he": "^1.2.0", "jsonpath-plus": "^10.3.0", "jwt-decode": "^3.1.2", "mailparser": "^3.7.2", "moment": "^2.29.4", "moment-timezone": "^0.5.40", "tsc-alias": "^1.8.2", "tsconfig-paths": "^4.1.2", "typescript": "^5.8.3"}, "overrides": {"semver": "^7.5.3"}, "directories": {"test": "tests"}, "repository": {"type": "git", "url": "https://morneaushepell.visualstudio.com/MSW/_git/abiliti-absenceapi-ivrlambdas"}}