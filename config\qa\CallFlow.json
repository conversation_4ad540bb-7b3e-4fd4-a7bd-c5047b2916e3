{"CallFlowDynamicFlowV1ARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/contact-flow/78498ff6-24e1-433c-a281-0aed5c8cc131", "CallFlowEnterNextAbsenceDateARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/b170eaa3-e9bf-4381-bb9f-5ecc966a5c39", "CallFlowEnterReasonARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/efc54cc2-50e0-466d-a5fe-07025c2f12ef", "CallFlowSubmitAbsenceIfErrorARN": "", "CallFlowClosingARN": "", "CallFlowGetOpeningMsgV1ARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/ec9c237c-1a16-4eed-a729-66061cdf0e53", "CallFlowEnterEmployeePhoneNumberARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/c1ca3f73-1856-4a26-a47a-29c822c35544", "CallFlowTransferToCSRARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/9122bd51-891d-4003-840b-d739f158123a", "CallFlowEnterAbsenceDateARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/9d449bf4-7ec8-4bca-bd85-6dcaa03ba4db", "CallFlowValidateEmployeeARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/f5467b78-f173-46fc-83b0-06391bdfc027", "CallFlowEnterTotalAbsenceDaysARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/548881cb-cde8-4fee-a655-2d5db7a3df5f", "CallFlowSubmitAbsenceARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/32d32b28-db2c-43e9-9305-61da411b22cb", "CallFlowEnterRTWARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/fe309e1e-ec1f-4b1a-ace3-d9d8c980d6c5", "CallFlowTransferToExternalARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/93ee0e01-7f59-4d2b-852c-53e5de28f589", "CallFlowGetOpenLeaveCaseARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/e8066367-a3d1-4292-8619-381e2fefcaf9", "CallFlowSTDLeaveARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/a65fe30c-6b0a-4453-9774-0f6cb7a8eac6", "CallFlowLinkAbsenceInAbilitiARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/bd8502ac-d99c-440b-ab35-5f89b294f08d", "CallFlowDynamicFlowV2ARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/contact-flow/e826c1ab-82f9-4ac5-92f4-6d241cbe5913", "CallFlowEnterAbsenceDateV2ARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/6d2c3b36-e2c9-458d-a471-386802d86ebf", "CallFlowEnterNextAbsenceDateV2ARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/280dc77b-4f1e-4c26-afe0-c9bec3ec1cbd", "CallFlowEnterDateRange1ARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/3627fd6d-68ba-4150-9a79-3d7412d29bf7", "CallFlowEnterDateRange2ARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/flow-module/9d8db81b-7b93-4838-97d2-8e14877921ef", "CallFlowVoicemailMessageARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/contact-flow/47b397bd-a5dd-4247-8ec7-e341e3650c7b", "LambdaValidateEmployeeIdARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateEmployeeId", "LambdaValidateEmployeePhoneNumberARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateEmployeePhoneNumber", "LambdaSubmitAbsenceARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SubmitAbsence", "LambdaGetPrimaryReasonMenuARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetPrimaryReasonMenu", "LambdaValidateAbsenceDateARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateAbsenceDate", "LambdaValidateShiftStartTimeARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateShiftStartTime", "LambdaValidateShiftEndTimeARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateShiftEndTime", "LambdaResolveConflictAbsenceARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ResolveConflictAbsence", "LambdaSaveSecondaryReasonARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveSecondaryReason", "LambdaAuthenticateEmployeeARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-AuthenticateEmployee", "LambdaSetReturnToWorkARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SetReturnToWork", "LambdaValidateReturnToWorkDateARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateReturnToWorkDate", "LambdaSaveReturnToWorkDateARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveReturnToWorkDate", "LambdaGetClientConfigARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetClientConfig", "LambdaValidateDoBARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateDoB", "LambdaGetSecondaryReasonMenuARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetSecondaryReasonMenu", "LambdaCheckAllAbsReportedARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-CheckAllAbsReported", "LambdaValidateRTWShiftStartTimeARN": "", "LambdaGetOpeningMsgV1ARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetOpeningMsgV1", "LambdaSaveTotalAbsenceDaysARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveTotalAbsenceDays", "LambdaGetCarrierTransferNumberARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetCarrierTransferNumber", "LambdaSaveAbsenceDateARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveAbsenceDate", "LambdaClearLastAddedAbsenceDateARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ClearLastAddedAbsenceDate", "LambdaDroppedSessionMonitorARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-DroppedSessionMonitor", "LambdaCheckOpenCasesARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-CheckOpenCases", "LambdaCheckEESTDARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-CheckEESTD", "LambdaSaveSTDResponseARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveSTDResponse", "LambdaSaveThirdLevelResponseARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveThirdLevelResponse", "LambdaLinkAbsenceInAbilitiARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-CheckCanBeLinked", "LambdaSaveLeaveResponseARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveLeaveResponse", "LambdaVMCallbackARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-VmCallback"}