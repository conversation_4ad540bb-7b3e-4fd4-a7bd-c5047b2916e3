import { ConnectContactFlowEvent } from "aws-lambda";
import { CallLog, LocalConfiguration, ScopedHttpService } from '@/services'
import { Constants } from '@/util'
const localConfig = new LocalConfiguration();

export const handler = async (event: ConnectContactFlowEvent) => {
    localConfig.Logger.log('Input data', event);
    const clientName = event.Details.Parameters.ClientName;
    const clientCode = event.Details.Parameters.ClientCode;
    const contactId = event.Details.ContactData.ContactId;
    localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
    const config = await localConfig.getClientConfigAsync(clientCode, clientName.toUpperCase())
         
    const welcomeMsg = config.clientConfig.WelcomeMessage;   
    //Add merge codes
    var mergeDictionary = [];
    mergeDictionary.push({ key: Constants.MERGECODE_TOTAL_ABSENCEDAYS, value: `${config.clientConfig.MaxTotalMissedAbsenceDays}` });
    mergeDictionary.push({ key: Constants.MERGECODE_MAX_PASTDAYS, value: `${config.clientConfig.MaxAllowAbsenceDaysPast}` });
    mergeDictionary.push({ key: Constants.MERGECODE_MAX_FUTUREDAYS, value: `${config.clientConfig.MaxAllowAbsenceDaysFuture}` });
    localConfig.Logger.log(mergeDictionary);
    
    let promptSpecialOpeningMsg1SSML, promptSpecialOpeningMsg2SSML = "";
    let promptOpeningMsg1SSML       , promptOpeningMsg2SSML        = "";
    let promptSubOpening1SSML       , promptSubOpening2SSML        = "";
    let promptLanugageSelectionS3URI = "";


    promptSpecialOpeningMsg1SSML = config.getPromptText(config.clientConfig.SpecialOpeningStatement
                                    , Constants.LocaleString_English
                                    , true, mergeDictionary) ?? '';
    promptOpeningMsg1SSML        = config.getPromptText(config.clientConfig.OpeningStatement
                                    , Constants.LocaleString_English
                                    , true, mergeDictionary) ?? '';
    promptSubOpening1SSML        = config.getPromptText(config.clientConfig.SubOpeningStatement
                                    , Constants.LocaleString_English
                                    , true, mergeDictionary) ?? '';

    switch (config.clientConfig.MultiLanguageId)
    {
        case Constants.MultiLanguage_EN_ES:
            promptSpecialOpeningMsg2SSML = config.getPromptText(config.clientConfig.SpecialOpeningStatement
                                            , Constants.LocaleString_Spanish
                                            , true, mergeDictionary) ?? '';
            promptOpeningMsg2SSML        = config.getPromptText(config.clientConfig.OpeningStatement
                                            , Constants.LocaleString_Spanish
                                            , true, mergeDictionary) ?? '';
            promptSubOpening2SSML        = config.getPromptText(config.clientConfig.SubOpeningStatement
                                            , Constants.LocaleString_Spanish
                                            , true, mergeDictionary) ?? '';
            promptLanugageSelectionS3URI = event.Details.Parameters.PromptBucketName ? 
            "s3://" + event.Details.Parameters.PromptBucketName  + "/Prompts/CICUSIVRPromptLangSelectEngSpa.wav" 
            : "Error: PromptBucketName not found!";
            break;
        case Constants.MultiLanguage_EN_FR:
            promptSpecialOpeningMsg2SSML = config.getPromptText(config.clientConfig.SpecialOpeningStatement
                                            , Constants.LocaleString_French
                                            , true, mergeDictionary) ?? '';
            promptOpeningMsg2SSML        = config.getPromptText(config.clientConfig.OpeningStatement
                                            , Constants.LocaleString_French
                                            , true, mergeDictionary) ?? '';
            promptSubOpening2SSML        = config.getPromptText(config.clientConfig.SubOpeningStatement
                                            , Constants.LocaleString_French
                                            , true, mergeDictionary) ?? '';
            
            promptLanugageSelectionS3URI = event.Details.Parameters.PromptBucketName ? 
            "s3://" + event.Details.Parameters.PromptBucketName  + "/Prompts/CICUSIVRPromptLangSelectEngFre.wav" 
            : "Error: PromptBucketName not found!";
            break;
        case Constants.MultiLanguage_EN:
        default:
            break;
    }

    let isAllowInteruppted = false;
    localConfig.Logger.log('Result config', JSON.stringify(config));
    return {
        "PromptSpecialOpeningMsg1SSML"  : promptSpecialOpeningMsg1SSML,//`<speak><amazon:domain name='conversational'>${promptSpeicalOpeningMsgSSML?? ''}</amazon:domain></speak>`,
        "PromptOpening1SSML"            : promptOpeningMsg1SSML,
        "PromptSubOpening1SSML"         : promptSubOpening1SSML,
        "PromptSpecialOpeningMsg2SSML"  : promptSpecialOpeningMsg2SSML,
        "PromptOpening2SSML"            : promptOpeningMsg2SSML,
        "PromptSubOpening2SSML"         : promptSubOpening2SSML,
        "IsAllowInteruppted"            : isAllowInteruppted ?? false,
        "MutliLanguageId"               : config.clientConfig.MultiLanguageId,
        "PromptLanugageSelectionS3URI"  : promptLanugageSelectionS3URI ?? "",
        "PromptWelcomeMsgSSML"          : welcomeMsg ?? ""
    };

}