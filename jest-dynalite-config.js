module.exports = {
    tables: [
      {
        TableName: 'ablarivr-call-session',
        KeySchema: [{AttributeName: 'ContactId', KeyType: 'HASH'}],
        AttributeDefinitions: [{AttributeName: 'ContactId', AttributeType: 'S'},{AttributeName: 'IsSubmitted',AttributeType: 'S'}],
        ProvisionedThroughput: {ReadCapacityUnits: 1, WriteCapacityUnits: 1},
        GlobalSecondaryIndexes: [
          {
            IndexName: "IsSubmittedIdx",
            KeySchema: [
              {
                AttributeName: "IsSubmitted",
                KeyType: "HASH"
              }
            ],
            Projection: {
              ProjectionType: "ALL"
            },
            ProvisionedThroughput: {
              ReadCapacityUnits: 1,
              WriteCapacityUnits: 1
            }
          }
        ],
        data:[{
          "EmployeeClaims": {
            "items": [
              {
                "claimDates": [
                  {
                    "isPartialAbsence": false,
                    "unpaidTimeInMinutes": null,
                    "endDate": "2023-03-20T17:00:00-04:00",
                    "startDate": "2023-03-20T09:00:00-04:00"
                  },
                  {
                    "isPartialAbsence": false,
                    "unpaidTimeInMinutes": null,
                    "endDate": "2023-03-21T17:00:00-04:00",
                    "startDate": "2023-03-21T09:00:00-04:00"
                  }
                ],
                "lastDateOfAbsence": "2023-03-21T13:00:00Z",
                "incidentId": 22369,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [
                  {
                    "isPartialAbsence": false,
                    "unpaidTimeInMinutes": null,
                    "endDate": "2023-04-16T17:30:00-04:00",
                    "startDate": "2023-04-16T09:30:00-04:00"
                  }
                ],
                "lastDateOfAbsence": "2023-04-16T13:30:00Z",
                "incidentId": 22582,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22382,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22384,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22387,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22386,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22385,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22392,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22391,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22390,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              }
            ]
          },
          "CallStageLog": [
            {
              "Error": {},
              "LambdaName": "saveAbsenceDate",
              "TimeStamp": "2023-04-28T14:37:04.290Z",
              "Result": null
            },
            {
              "Error": {},
              "LambdaName": "saveAbsenceDate",
              "TimeStamp": "2023-04-28T14:37:38.613Z",
              "Result": null
            },
            {
              "Error": {},
              "LambdaName": "saveAbsenceDate",
              "TimeStamp": "2023-04-28T14:37:59.833Z",
              "Result": null
            }
          ],
          "TotalAbsenceDays": 1,
          "ClientCode": "GOODYEAR",
          "Absences": [
            {
              "ReasonConfig": {
                "ReportableDaysInFuture": 60,
                "ReportableDaysInPast": 2,
                "MaxDaysAllowed": 120
              },
              "AbsenceQuestionResponse": {
                "preQualifyingQuestions": [],
                "questions": [
                  {
                    "pageHeadingDescription": [],
                    "pageHeading": [],
                    "pageId": "1FA",
                    "sections": [
                      {
                        "questions": [],
                        "sectionId": "1FA",
                        "sectionHeading": [],
                        "sectionHeadingDescription": []
                      }
                    ]
                  },
                  {
                    "pageHeadingDescription": [],
                    "pageHeading": [],
                    "pageId": "1C",
                    "sections": [
                      {
                        "questions": [],
                        "sectionId": "1C",
                        "sectionHeading": [],
                        "sectionHeadingDescription": []
                      }
                    ]
                  },
                  {
                    "pageHeadingDescription": [],
                    "pageHeading": [],
                    "pageId": "1F",
                    "sections": [
                      {
                        "questions": [],
                        "sectionId": "1F",
                        "sectionHeading": [],
                        "sectionHeadingDescription": []
                      }
                    ]
                  }
                ],
                "reasonOfAbsenceAlerts": [
                  {
                    "alertLevel": "Secondary",
                    "alertText": [
                      {
                        "locale": "EN",
                        "description": "<p>If you or your family member is hospitalized or you expect your absence to be greater than 3 days due to an illness or injury, you will need to go back and select the &quot;STD, Accident &amp; Sickness or FMLA Absence&quot; prompt . Once you have reported your absence(s), you are required to call UNUM at 1.888.621.0038 or visit www.unum.com to provide additional information. For Texarkana employees, you are also required to contact HR directly for return to work instructions.</p>\n",
                        "shortDescription": null
                      }
                    ],
                    "alertId": "cf7d147f-ed8d-46c6-8301-92488999b211"
                  }
                ]
              },
              "SecondaryReason": "5818e815-3466-470c-87f2-2258b5a7ef80",
              "ValidationErrors": [],
              "PrimaryReason": "72bd21d7-efc8-45ba-bf23-1fea48fa002b",
              "ClientCode": "GOODYEAR",
              "EmployeeNumber": "80083033",
              "AbsenceIncident": {
                "AbsenceType": "continuous",
                "QuestionResponses": {},
                "SecondaryReason": "5818e815-3466-470c-87f2-2258b5a7ef80",
                "FirstName": "Qantrell",
                "NextScheduledShift": null,
                "BestPhoneNumber": null,
                "EmployeeNumber": "80083033",
                "AbsenceIncidentId": 22679,
                "MaxReportableDays": 120,
                "RequestDate": "2023-04-28T14:36:02.9094149Z",
                "LinkedIncident": null,
                "PrimaryReason": "72bd21d7-efc8-45ba-bf23-1fea48fa002b",
                "ClaimStatus": "I",
                "MaxDaysAllowed": 120,
                "ClientCode": "GOODYEAR",
                "LastName": "Archie",
                "ProvinceCode": "AR",
                "AbsenceDates": [],
                "ReportedBy": "Employee",
                "ReturnToWorkDate": null
              },
              "AlertMessages": [
                {
                  "AlertId": "cf7d147f-ed8d-46c6-8301-92488999b211",
                  "AlertText": [
                    {
                      "Locale": "EN",
                      "Text": "<p>If you or your family member is hospitalized or you expect your absence to be greater than 3 days due to an illness or injury, you will need to go back and select the &quot;STD, Accident &amp; Sickness or FMLA Absence&quot; prompt . Once you have reported your absence(s), you are required to call UNUM at 1.888.621.0038 or visit www.unum.com to provide additional information. For Texarkana employees, you are also required to contact HR directly for return to work instructions.</p>\n"
                    }
                  ]
                }
              ]
            }
          ],
          "Reasons": {
            "ReasonEntries": [
              {
                "IsLeaf": false,
                "IVROptionNumber": 1,
                "Description": "Illness or Injury (not FMLA)",
                "Id": "72bd21d7-efc8-45ba-bf23-1fea48fa002b",
                "Child": {
                  "IsLeaf": true,
                  "IVROptionNumber": 1,
                  "Description": "Self",
                  "Id": "5818e815-3466-470c-87f2-2258b5a7ef80",
                  "Child": null
                }
              }
            ],
            "Count": 1
          },
          "ContactId": "4af1910b-97dd-44bf-b480-aff21060aada",
          "EmployeeInfo": {
            "workPostalCode": null,
            "jobTitle": null,
            "supervisorEmail": null,
            "language": "EN",
            "employeeNumberPadded": "000000080083033",
            "employeeNumber": "80083033",
            "homeProvinceCode": null,
            "organizationId": 142502,
            "workPhoneNumber": null,
            "supervisorFirstName": null,
            "businessEmail": null,
            "updatedBy": "g_migration",
            "organizationName": "SUP_40001035-TEX HRLY 219 ASSEMBLY",
            "homeAddressLine2": null,
            "employeeAddresses": [
              {
                "addressTypeCode": "HOME",
                "addressTypeId": 2,
                "countryCode": "US",
                "postalCode": null,
                "addressLine1": null,
                "provinceStateCode": null,
                "addressLine2": null
              },
              {
                "addressTypeCode": "OFFICE",
                "addressTypeId": 1,
                "countryCode": "US",
                "postalCode": "71854",
                "addressLine1": "3500 Washington St",
                "provinceStateCode": "AR",
                "addressLine2": null
              }
            ],
            "homeAddressLine1": null,
            "supervisorWCBEmail": null,
            "supervisorId": 0,
            "workAddressLine1": null,
            "wageUnit": null,
            "workAddressLine2": null,
            "firstName": "Qantrell",
            "companyId": 1576,
            "homeEmail": null,
            "supervisorLastName": null,
            "dateOfHire": "2016-03-09T00:00:00",
            "phoneNumber": "4302008100",
            "salutation": null,
            "updateDateTime": "2023-01-17T16:56:29.403",
            "homeCountry": null,
            "hash": null,
            "supervisorName": null,
            "lastName": "Archie",
            "gender": "U",
            "workCountry": null,
            "supervisorAREmail": null,
            "middleInitial": null,
            "supervisorEmployeeNumber": null,
            "workAltPhoneNumber": null,
            "isSearchable": true,
            "recordFrom": "DynamicForms",
            "payRateFrequency": null,
            "employmentType": "FULLTIMEREG",
            "emP_Department": null,
            "provinceCode": "AR",
            "employmentTypeDescription": null,
            "employeeId": 1337386,
            "union": null,
            "birthDate": "1987-07-24T00:00:00",
            "homePostalCode": null,
            "createDateTime": "2023-01-17T09:16:55.973",
            "workProvinceCode": null,
            "altPhoneNumber": "9999999999",
            "createdBy": "g_migration",
            "clientCode": "GOODYEAR",
            "supervisorSTDEmail": null,
            "employeeCustomFields": [],
            "employeeWorkStatusId": 0
          },
          "IsSaved": "false"
        },
        {
          "EmployeeClaims": {
            "items": [
              {
                "lastDateOfAbsence": "2023-04-28T13:30:00Z",
                "claimDates": [
                  {
                    "isPartialAbsence": false,
                    "unpaidTimeInMinutes": null,
                    "endDate": "2023-04-28T17:30:00-04:00",
                    "startDate": "2023-04-28T09:30:00-04:00"
                  }
                ],
                "incidentId": 22449,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": "2023-05-03T13:30:00Z",
                "claimDates": [
                  {
                    "isPartialAbsence": false,
                    "unpaidTimeInMinutes": null,
                    "endDate": "2023-05-03T17:30:00-04:00",
                    "startDate": "2023-05-03T09:30:00-04:00"
                  }
                ],
                "incidentId": 22669,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22448,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22452,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22453,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22467,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22466,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22465,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22464,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22475,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              }
            ]
          },
          "CallStageLog": [
            {
              "Error": {},
              "LambdaName": "authenticateEmployee",
              "TimeStamp": "2023-04-27T14:57:09.138Z",
              "Result": null
            },
            {
              "Error": {},
              "LambdaName": "authenticateEmployee",
              "TimeStamp": "2023-04-27T14:58:03.211Z",
              "Result": null
            },
            {
              "Error": null,
              "LambdaName": "authenticateEmployee",
              "TimeStamp": "2023-04-27T14:58:21.095Z",
              "Result": {
                "AA": "2",
                "LastAbsenceDate": "2023-05-03",
                "IsDoBValid": true,
                "IsPrevAbsWithinAADays": false,
                "IsEmployeeValid": true
              }
            },
            {
              "Error": null,
              "LambdaName": "saveTotalAbsenceDays",
              "TimeStamp": "2023-04-27T14:58:30.707Z",
              "Result": {
                "IsTotalAbsenceDaysValid": true,
                "IsSaveSuccess": true
              }
            },
            {
              "Error": null,
              "LambdaName": "saveAbsenceDate",
              "TimeStamp": "2023-04-27T14:59:21.100Z",
              "Result": {
                "IsSaveSuccess": true,
                "IsAbsDateWithinValidRange": true
              }
            },
            {
              "Error": null,
              "LambdaName": "validateShiftStartTime",
              "TimeStamp": "2023-04-27T14:59:59.984Z",
              "Result": {
                "IsShiftStartTimeValid": true,
                "IsSaveSuccess": true
              }
            },
            {
              "Error": null,
              "LambdaName": "validateShiftEndTime",
              "TimeStamp": "2023-04-27T15:00:25.491Z",
              "Result": {
                "IsShiftEndTimeValid": true,
                "ShiftTimeNotValidReason": null,
                "IsSaveSuccess": false
              }
            },
            {
              "Error": null,
              "LambdaName": "validateShiftEndTime",
              "TimeStamp": "2023-04-27T15:00:25.535Z",
              "Result": {
                "IsShiftEndTimeValid": true,
                "ShiftTimeNotValidReason": null,
                "IsSaveSuccess": true
              }
            },
            {
              "Error": [
                {
                  "ErrorDescription": "Duplicated absence shift dates.",
                  "ErrorSeverity": 0,
                  "ErrorProvider": "AbsenceIncidentValidator",
                  "ErrorParameters": {
                    "PropertyName": "Absence Dates",
                    "PropertyValue": [
                      {
                        "isPartialAbsence": false,
                        "absenceDateId": 0,
                        "scheduledShiftEndTime": null,
                        "absenceIncidentId": 0,
                        "unpaidTimeInMinutes": null,
                        "shiftStartTime": "2023-04-29T09:30:00-04:00",
                        "scheduledShiftStartTime": null,
                        "shiftEndTime": "2023-04-29T00:00:00-04:00",
                        "shiftDuration": null,
                        "absenceIncident": null
                      }
                    ]
                  },
                  "ErrorCode": "absence_incident-duplicated_date"
                },
                {
                  "ErrorDescription": "Shift start time is later than end.",
                  "ErrorSeverity": 0,
                  "ErrorProvider": "AbsenceIncidentValidator",
                  "ErrorParameters": {
                    "PropertyName": "Absence Dates",
                    "PropertyValue": [
                      {
                        "isPartialAbsence": false,
                        "absenceDateId": 0,
                        "scheduledShiftEndTime": null,
                        "absenceIncidentId": 0,
                        "unpaidTimeInMinutes": null,
                        "shiftStartTime": "2023-04-29T09:30:00-04:00",
                        "scheduledShiftStartTime": null,
                        "shiftEndTime": "2023-04-29T00:00:00-04:00",
                        "shiftDuration": null,
                        "absenceIncident": null
                      }
                    ]
                  },
                  "ErrorCode": "absence_incident-shift_start_adter_end"
                }
              ],
              "LambdaName": "saveAbsenceDate",
              "TimeStamp": "2023-04-27T19:18:25.506Z",
              "Result": null
            },
            {
              "Error": null,
              "LambdaName": "validateShiftEndTime",
              "TimeStamp": "2023-04-27T19:25:41.306Z",
              "Result": {
                "IsShiftEndTimeValid": true,
                "ShiftTimeNotValidReason": null,
                "IsSaveSuccess": false
              }
            },
            {
              "Error": null,
              "LambdaName": "validateShiftEndTime",
              "TimeStamp": "2023-04-27T19:25:41.345Z",
              "Result": {
                "IsShiftEndTimeValid": true,
                "ShiftTimeNotValidReason": null,
                "IsSaveSuccess": true
              }
            },
            {
              "Error": null,
              "LambdaName": "saveAbsenceDate",
              "TimeStamp": "2023-04-27T19:28:24.677Z",
              "Result": {
                "IsSaveSuccess": true,
                "IsAbsDateWithinValidRange": true
              }
            },
            {
              "Error": null,
              "LambdaName": "saveAbsenceDate",
              "TimeStamp": "2023-04-27T19:41:28.672Z",
              "Result": {
                "IsSaveSuccess": true,
                "IsAbsDateWithinValidRange": true
              }
            },
            {
              "Error": null,
              "LambdaName": "validateShiftStartTime",
              "TimeStamp": "2023-04-27T19:41:45.853Z",
              "Result": {
                "IsShiftStartTimeValid": true,
                "IsSaveSuccess": true
              }
            },
            {
              "Error": null,
              "LambdaName": "validateShiftEndTime",
              "TimeStamp": "2023-04-27T19:42:34.924Z",
              "Result": {
                "IsShiftEndTimeValid": true,
                "ShiftTimeNotValidReason": null,
                "IsSaveSuccess": true
              }
            }
          ],
          "TotalAbsenceDays": 1,
          "ClientCode": "GOODYEAR",
          "Absences": [
            {
              "ReasonConfig": {
                "ReportableDaysInFuture": 60,
                "ReportableDaysInPast": 2,
                "MaxDaysAllowed": 120
              },
              "AbsenceQuestionResponse": {
                "preQualifyingQuestions": [],
                "questions": [
                  {
                    "pageHeadingDescription": [],
                    "pageHeading": [],
                    "pageId": "1FA",
                    "sections": [
                      {
                        "questions": [],
                        "sectionId": "1FA",
                        "sectionHeading": [],
                        "sectionHeadingDescription": []
                      }
                    ]
                  },
                  {
                    "pageHeadingDescription": [],
                    "pageHeading": [],
                    "pageId": "1C",
                    "sections": [
                      {
                        "questions": [],
                        "sectionId": "1C",
                        "sectionHeading": [],
                        "sectionHeadingDescription": []
                      }
                    ]
                  },
                  {
                    "pageHeadingDescription": [],
                    "pageHeading": [],
                    "pageId": "1F",
                    "sections": [
                      {
                        "questions": [],
                        "sectionId": "1F",
                        "sectionHeading": [],
                        "sectionHeadingDescription": []
                      }
                    ]
                  }
                ],
                "reasonOfAbsenceAlerts": []
              },
              "SecondaryReason": "5818e815-3466-470c-87f2-2258b5a7ef80",
              "PrimaryReason": "72bd21d7-efc8-45ba-bf23-1fea48fa002b",
              "EmployeeNumber": "80083033",
              "ClientCode": "GOODYEAR",
              "AbsenceIncident": {
                "AbsenceType": "continuous",
                "QuestionResponses": {},
                "SecondaryReason": "5818e815-3466-470c-87f2-2258b5a7ef80",
                "FirstName": "Qantrell",
                "NextScheduledShift": null,
                "EmployeeNumber": "80083033",
                "BestPhoneNumber": null,
                "AbsenceIncidentId": 22735,
                "RequestDate": "2023-05-03T18:24:58.6524815Z",
                "MaxReportableDays": 120,
                "LinkedIncident": null,
                "PrimaryReason": "72bd21d7-efc8-45ba-bf23-1fea48fa002b",
                "ClaimStatus": "I",
                "MaxDaysAllowed": 120,
                "ClientCode": "GOODYEAR",
                "LastName": "Archie",
                "ProvinceCode": "OH",
                "AbsenceDates": [
                  {
                    "ShiftStartTime": "2023-05-05 08:00:00",
                    "StartDate": "2023-05-05 08:00:00",
                    "ShiftDuration": null,
                    "ScheduledShiftEndTime": null,
                    "ScheduledShiftStartTime": null,
                    "IsUpdated": false,
                    "EndDate": "2023-05-05 17:00:00",
                    "IncidentId": 22735,
                    "ShiftEndTime": "2023-05-05 17:00:00"
                  }
                ],
                "ReturnToWorkDate": "2023-05-06 00:00:00",
                "ReportedBy": "Employee"
              },
              "AlertMessages": []
            }
          ],
          "Reasons": {
            "ReasonEntries": [
              {
                "IVROptionNumber": 2,
                "IsLeaf": false,
                "Id": "d31f99ac-deb9-441b-be53-faf4c1ccba00",
                "Description": "STD/Accident & Sickness or FMLA Absence",
                "Child": {
                  "IVROptionNumber": 2,
                  "IsLeaf": true,
                  "Id": "a60cc0b5-e69e-4909-8b72-3fbd4fdaf9a8",
                  "Description": "Associated with an existing intermittent claim",
                  "Child": null
                }
              }
            ],
            "Count": 1
          },
          "ContactId": "24a9d991-cbed-4b83-bab5-35d38cbf5191",
          "IsSaved": "true",
          "EmployeeInfo": {
            "workPostalCode": null,
            "jobTitle": null,
            "supervisorEmail": null,
            "language": "EN",
            "employeeNumberPadded": "000000080081254",
            "employeeNumber": "80081254",
            "organizationId": 142480,
            "homeProvinceCode": null,
            "workPhoneNumber": null,
            "supervisorFirstName": null,
            "businessEmail": null,
            "updatedBy": "g_migration",
            "organizationName": "SUP_40001012-FIN HRLY VRP Unistage Tir",
            "homeAddressLine2": null,
            "supervisorWCBEmail": null,
            "employeeAddresses": [
              {
                "addressTypeCode": "HOME",
                "addressTypeId": 2,
                "countryCode": "US",
                "postalCode": null,
                "addressLine1": null,
                "provinceStateCode": null,
                "addressLine2": null
              },
              {
                "addressTypeCode": "OFFICE",
                "addressTypeId": 1,
                "countryCode": "US",
                "postalCode": "45840",
                "addressLine1": "701 Lima Avenue",
                "provinceStateCode": "OH",
                "addressLine2": null
              }
            ],
            "homeAddressLine1": null,
            "supervisorId": 0,
            "workAddressLine1": null,
            "wageUnit": null,
            "workAddressLine2": null,
            "firstName": "Joshua",
            "homeEmail": null,
            "supervisorLastName": null,
            "companyId": 1576,
            "dateOfHire": "2014-06-03T00:00:00",
            "phoneNumber": "5672081231",
            "salutation": null,
            "updateDateTime": "2023-01-17T16:58:59.547",
            "homeCountry": null,
            "hash": null,
            "supervisorName": null,
            "lastName": "Routzon",
            "gender": "U",
            "workCountry": null,
            "supervisorAREmail": null,
            "middleInitial": null,
            "supervisorEmployeeNumber": null,
            "workAltPhoneNumber": null,
            "isSearchable": true,
            "recordFrom": "DynamicForms",
            "payRateFrequency": null,
            "employmentType": "FULLTIMEREG",
            "emP_Department": null,
            "provinceCode": "OH",
            "employmentTypeDescription": null,
            "employeeId": 1338208,
            "union": null,
            "birthDate": "1993-10-20T00:00:00",
            "homePostalCode": null,
            "createDateTime": "2023-01-17T09:19:34.247",
            "workProvinceCode": null,
            "altPhoneNumber": "9999999999",
            "createdBy": "g_migration",
            "clientCode": "GOODYEAR",
            "supervisorSTDEmail": null,
            "employeeCustomFields": [],
            "employeeWorkStatusId": 0
          }
        },
        {
          "EmployeeClaims": {
            "items": [
              {
                "claimDates": [
                  {
                    "isPartialAbsence": false,
                    "unpaidTimeInMinutes": null,
                    "endDate": "2023-03-20T17:00:00-04:00",
                    "startDate": "2023-03-20T09:00:00-04:00"
                  },
                  {
                    "isPartialAbsence": false,
                    "unpaidTimeInMinutes": null,
                    "endDate": "2023-03-21T17:00:00-04:00",
                    "startDate": "2023-03-21T09:00:00-04:00"
                  }
                ],
                "lastDateOfAbsence": "2023-03-21T13:00:00Z",
                "incidentId": 22369,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22382,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22384,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22387,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22386,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22385,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22392,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22391,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22390,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "claimDates": [],
                "lastDateOfAbsence": null,
                "incidentId": 22389,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              }
            ]
          },
          "CallStageLog": [
            {
              "Error": null,
              "LambdaName": "authenticateEmployee",
              "TimeStamp": "2023-04-06T17:13:35.734Z",
              "Result": {
                "AA": "2",
                "LastAbsenceDate": "2023-03-21",
                "IsDoBValid": true,
                "IsPrevAbsWithinAADays": false,
                "IsEmployeeValid": true
              }
            },
            {
              "Error": {
                "Validation": null
              },
              "LambdaName": "validateReturnToWorkDate",
              "TimeStamp": "2023-04-06T17:16:44.928Z",
              "Result": {
                "IsRTWValid": true,
                "ReturnDayToWorkplayable": "2023-04-10",
                "RTWNotValidReason": null,
                "IsSaveSuccess": true
              }
            },
            {
              "Error": {
                "Validation": null
              },
              "LambdaName": "validateReturnToWorkDate",
              "TimeStamp": "2023-04-06T17:21:10.669Z",
              "Result": {
                "IsRTWValid": true,
                "ReturnDayToWorkplayable": "2023-04-10",
                "RTWNotValidReason": null,
                "IsSaveSuccess": true
              }
            }
          ],
          "TotalAbsenceDays": 1,
          "ClientCode": "GOODYEAR",
          "Absences": [
            {
              "ReasonConfig": {
                "ReportableDaysInFuture": 60,
                "ReportableDaysInPast": 2,
                "MaxDaysAllowed": 120
              },
              "AbsenceQuestionResponse": {
                "preQualifyingQuestions": [],
                "questions": [
                  {
                    "pageHeadingDescription": [],
                    "pageHeading": [],
                    "pageId": "1FA",
                    "sections": [
                      {
                        "questions": [],
                        "sectionId": "1FA",
                        "sectionHeading": [],
                        "sectionHeadingDescription": []
                      }
                    ]
                  },
                  {
                    "pageHeadingDescription": [],
                    "pageHeading": [],
                    "pageId": "1C",
                    "sections": [
                      {
                        "questions": [],
                        "sectionId": "1C",
                        "sectionHeading": [],
                        "sectionHeadingDescription": []
                      }
                    ]
                  },
                  {
                    "pageHeadingDescription": [],
                    "pageHeading": [],
                    "pageId": "1F",
                    "sections": [
                      {
                        "questions": [],
                        "sectionId": "1F",
                        "sectionHeading": [],
                        "sectionHeadingDescription": []
                      }
                    ]
                  }
                ],
                "reasonOfAbsenceAlerts": [
                  {
                    "alertLevel": "Secondary",
                    "alertText": [
                      {
                        "locale": "EN",
                        "description": "<p>If you or your family member is hospitalized or you expect your absence to be greater than 3 days due to an illness or injury, you will need to go back and select the &quot;STD, Accident &amp; Sickness or FMLA Absence&quot; prompt . Once you have reported your absence(s), you are required to call UNUM at 1.888.621.0038 or visit www.unum.com to provide additional information. For Texarkana employees, you are also required to contact HR directly for return to work instructions.</p>\n",
                        "shortDescription": null
                      }
                    ],
                    "alertId": "cf7d147f-ed8d-46c6-8301-92488999b211"
                  }
                ]
              },
              "SecondaryReason": "5818e815-3466-470c-87f2-2258b5a7ef80",
              "PrimaryReason": "72bd21d7-efc8-45ba-bf23-1fea48fa002b",
              "ClientCode": "GOODYEAR",
              "EmployeeNumber": "80083033",
              "AbsenceIncident": {
                "AbsenceType": null,
                "QuestionResponses": {},
                "SecondaryReason": "5818e815-3466-470c-87f2-2258b5a7ef80",
                "FirstName": "Qantrell",
                "NextScheduledShift": null,
                "BestPhoneNumber": null,
                "EmployeeNumber": "80083033",
                "AbsenceIncidentId": 22536,
                "MaxReportableDays": 120,
                "RequestDate": "2023-04-06T17:14:32.6475903Z",
                "LinkedIncident": null,
                "PrimaryReason": "72bd21d7-efc8-45ba-bf23-1fea48fa002b",
                "ClaimStatus": "I",
                "MaxDaysAllowed": 120,
                "ClientCode": "GOODYEAR",
                "LastName": "Archie",
                "ProvinceCode": "AR",
                "AbsenceDates": [
                  {
                    "StartDate": "2023-04-09 09:00:00",
                    "ShiftDuration": "08:00:00",
                    "ScheduledShiftEndTime": null,
                    "IsUpdated": false,
                    "EndDate": "2023-04-09 17:00:00",
                    "ScheduledShiftStartTime": null
                  }
                ],
                "ReportedBy": "Employee",
                "ReturnToWorkDate": null
              },
              "AlertMessages": [
                {
                  "AlertId": "cf7d147f-ed8d-46c6-8301-92488999b211",
                  "AlertText": [
                    {
                      "Locale": "EN",
                      "Text": "<p>If you or your family member is hospitalized or you expect your absence to be greater than 3 days due to an illness or injury, you will need to go back and select the &quot;STD, Accident &amp; Sickness or FMLA Absence&quot; prompt . Once you have reported your absence(s), you are required to call UNUM at 1.888.621.0038 or visit www.unum.com to provide additional information. For Texarkana employees, you are also required to contact HR directly for return to work instructions.</p>\n"
                    }
                  ]
                }
              ]
            }
          ],
          "Reasons": {
            "ReasonEntries": [
              {
                "IsLeaf": false,
                "IVROptionNumber": 1,
                "Description": "Illness or Injury (not FMLA)",
                "Id": "72bd21d7-efc8-45ba-bf23-1fea48fa002b",
                "Child": {
                  "IsLeaf": true,
                  "IVROptionNumber": 1,
                  "Description": "Self",
                  "Id": "5818e815-3466-470c-87f2-2258b5a7ef80",
                  "Child": null
                }
              }
            ],
            "Count": 1
          },
          "ContactId": "f76bc7a7-fb6c-4aee-9a47-3368dcdaf006",
          "ReturnToWorkDate": "2023-04-10T00:00:00.000Z",
          "EmployeeInfo": {
            "workPostalCode": null,
            "jobTitle": null,
            "supervisorEmail": null,
            "language": "EN",
            "employeeNumberPadded": "000000080083033",
            "employeeNumber": "80083033",
            "homeProvinceCode": null,
            "organizationId": 142502,
            "workPhoneNumber": null,
            "supervisorFirstName": null,
            "businessEmail": null,
            "updatedBy": "g_migration",
            "organizationName": "SUP_40001035-TEX HRLY 219 ASSEMBLY",
            "homeAddressLine2": null,
            "employeeAddresses": [
              {
                "addressTypeCode": "HOME",
                "addressTypeId": 2,
                "countryCode": "US",
                "postalCode": null,
                "addressLine1": null,
                "provinceStateCode": null,
                "addressLine2": null
              },
              {
                "addressTypeCode": "OFFICE",
                "addressTypeId": 1,
                "countryCode": "US",
                "postalCode": "71854",
                "addressLine1": "3500 Washington St",
                "provinceStateCode": "AR",
                "addressLine2": null
              }
            ],
            "homeAddressLine1": null,
            "supervisorWCBEmail": null,
            "supervisorId": 0,
            "workAddressLine1": null,
            "wageUnit": null,
            "workAddressLine2": null,
            "firstName": "Qantrell",
            "companyId": 1576,
            "homeEmail": null,
            "supervisorLastName": null,
            "dateOfHire": "2016-03-09T00:00:00",
            "phoneNumber": "4302008100",
            "salutation": null,
            "updateDateTime": "2023-01-17T16:56:29.403",
            "homeCountry": null,
            "hash": null,
            "supervisorName": null,
            "lastName": "Archie",
            "gender": "U",
            "workCountry": null,
            "supervisorAREmail": null,
            "middleInitial": null,
            "supervisorEmployeeNumber": null,
            "workAltPhoneNumber": null,
            "isSearchable": true,
            "recordFrom": "DynamicForms",
            "payRateFrequency": null,
            "employmentType": "FULLTIMEREG",
            "emP_Department": null,
            "provinceCode": "AR",
            "employmentTypeDescription": null,
            "employeeId": 1337386,
            "union": null,
            "birthDate": "1987-07-24T00:00:00",
            "homePostalCode": null,
            "createDateTime": "2023-01-17T09:16:55.973",
            "workProvinceCode": null,
            "altPhoneNumber": "9999999999",
            "createdBy": "g_migration",
            "clientCode": "GOODYEAR",
            "supervisorSTDEmail": null,
            "employeeCustomFields": [],
            "employeeWorkStatusId": 0
          },
          "IsSaved": "false"
        },
        {
          "EmployeeClaims": {
            "items": [
              {
                "lastDateOfAbsence": "2023-04-28T13:30:00Z",
                "claimDates": [
                  {
                    "isPartialAbsence": false,
                    "unpaidTimeInMinutes": null,
                    "endDate": "2023-04-28T17:30:00-04:00",
                    "startDate": "2023-04-28T09:30:00-04:00"
                  }
                ],
                "incidentId": 22449,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": "2023-04-29T13:30:00Z",
                "claimDates": [
                  {
                    "isPartialAbsence": false,
                    "unpaidTimeInMinutes": null,
                    "endDate": "2023-04-29T17:30:00-04:00",
                    "startDate": "2023-04-29T09:30:00-04:00"
                  }
                ],
                "incidentId": 22675,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": "2023-04-30T13:30:00Z",
                "claimDates": [
                  {
                    "isPartialAbsence": false,
                    "unpaidTimeInMinutes": null,
                    "endDate": "2023-04-30T17:30:00-04:00",
                    "startDate": "2023-04-30T09:30:00-04:00"
                  }
                ],
                "incidentId": 22678,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": "2023-05-03T13:30:00Z",
                "claimDates": [
                  {
                    "isPartialAbsence": false,
                    "unpaidTimeInMinutes": null,
                    "endDate": "2023-05-03T17:30:00-04:00",
                    "startDate": "2023-05-03T09:30:00-04:00"
                  }
                ],
                "incidentId": 22669,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": "2023-05-07T12:00:00Z",
                "claimDates": [
                  {
                    "isPartialAbsence": false,
                    "unpaidTimeInMinutes": null,
                    "endDate": "2023-05-07T17:00:00-04:00",
                    "startDate": "2023-05-07T08:00:00-04:00"
                  }
                ],
                "incidentId": 22737,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22448,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22452,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22453,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22467,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              },
              {
                "lastDateOfAbsence": null,
                "claimDates": [],
                "incidentId": 22466,
                "leaveEndDate": null,
                "incidentTypeCssClass": "INCIDENT_MYABILITI_AR"
              }
            ]
          },
          "CallStageLog": [
            {
              "Error": null,
              "LambdaName": "authenticateEmployee",
              "TimeStamp": "2023-05-08T15:05:14.640Z",
              "Result": {
                "AA": "2",
                "LastAbsenceDate": "2023-05-07",
                "IsDoBValid": true,
                "IsPrevAbsWithinAADays": true,
                "IsEmployeeValid": true
              }
            },
            {
              "Error": null,
              "LambdaName": "saveTotalAbsenceDays",
              "TimeStamp": "2023-05-08T15:28:21.238Z",
              "Result": {
                "IsTotalAbsenceDaysValid": true,
                "IsSaveSuccess": true
              }
            },
            {
              "Error": null,
              "LambdaName": "saveAbsenceDate",
              "TimeStamp": "2023-05-08T15:35:31.127Z",
              "Result": {
                "IsSaveSuccess": true,
                "IsAbsDateWithinValidRange": true
              }
            },
            {
              "Error": null,
              "LambdaName": "validateShiftStartTime",
              "TimeStamp": "2023-05-08T15:37:50.153Z",
              "Result": {
                "IsShiftStartTimeValid": true,
                "IsSaveSuccess": true
              }
            },
            {
              "Error": {},
              "LambdaName": "validateShiftEndTime",
              "TimeStamp": "2023-05-08T15:38:04.521Z",
              "Result": null
            },
            {
              "Error": {},
              "LambdaName": "validateShiftEndTime",
              "TimeStamp": "2023-05-08T15:40:23.695Z",
              "Result": null
            },
            {
              "Error": null,
              "LambdaName": "validateShiftEndTime",
              "TimeStamp": "2023-05-08T18:33:01.339Z",
              "Result": {
                "IsShiftEndTimeValid": true,
                "ShiftTimeNotValidReason": null,
                "IsSaveSuccess": true
              }
            },
            {
              "Error": null,
              "LambdaName": "saveAbsenceDate",
              "TimeStamp": "2023-05-08T18:42:19.895Z",
              "Result": {
                "IsSaveSuccess": true,
                "IsAbsDateWithinValidRange": true
              }
            },
            {
              "Error": null,
              "LambdaName": "validateShiftStartTime",
              "TimeStamp": "2023-05-08T18:42:52.716Z",
              "Result": {
                "IsShiftStartTimeValid": true,
                "IsSaveSuccess": true
              }
            },
            {
              "Error": null,
              "LambdaName": "saveAbsenceDate",
              "TimeStamp": "2023-05-08T19:02:42.885Z",
              "Result": {
                "IsSaveSuccess": true,
                "IsAbsDateWithinValidRange": true
              }
            },
            {
              "Error": null,
              "LambdaName": "validateShiftStartTime",
              "TimeStamp": "2023-05-08T19:03:42.405Z",
              "Result": {
                "IsShiftStartTimeValid": true,
                "IsSaveSuccess": true
              }
            },
            {
              "Error": null,
              "LambdaName": "validateShiftEndTime",
              "TimeStamp": "2023-05-08T19:04:01.237Z",
              "Result": {
                "IsShiftEndTimeValid": true,
                "ShiftTimeNotValidReason": null,
                "IsSaveSuccess": false
              }
            },
            {
              "Error": null,
              "LambdaName": "validateShiftEndTime",
              "TimeStamp": "2023-05-08T19:04:04.715Z",
              "Result": {
                "IsShiftEndTimeValid": true,
                "ShiftTimeNotValidReason": null,
                "IsSaveSuccess": true
              }
            },
            {
              "Error": null,
              "LambdaName": "validateShiftEndTime",
              "TimeStamp": "2023-05-08T19:07:28.584Z",
              "Result": {
                "IsShiftEndTimeValid": true,
                "ShiftTimeNotValidReason": null,
                "IsSaveSuccess": true
              }
            }
          ],
          "TotalAbsenceDays": 3,
          "ClientCode": "GOODYEAR",
          "Absences": [
            {
              "ReasonConfig": {
                "ReportableDaysInFuture": 60,
                "ReportableDaysInPast": 2,
                "MaxDaysAllowed": 120
              },
              "AbsenceQuestionResponse": {
                "preQualifyingQuestions": [],
                "questions": [
                  {
                    "pageHeadingDescription": [],
                    "pageHeading": [],
                    "pageId": "1FA",
                    "sections": [
                      {
                        "questions": [],
                        "sectionId": "1FA",
                        "sectionHeading": [],
                        "sectionHeadingDescription": []
                      }
                    ]
                  },
                  {
                    "pageHeadingDescription": [],
                    "pageHeading": [],
                    "pageId": "1C",
                    "sections": [
                      {
                        "questions": [],
                        "sectionId": "1C",
                        "sectionHeading": [],
                        "sectionHeadingDescription": []
                      }
                    ]
                  },
                  {
                    "pageHeadingDescription": [],
                    "pageHeading": [],
                    "pageId": "1F",
                    "sections": [
                      {
                        "questions": [],
                        "sectionId": "1F",
                        "sectionHeading": [],
                        "sectionHeadingDescription": []
                      }
                    ]
                  }
                ],
                "reasonOfAbsenceAlerts": []
              },
              "SecondaryReason": "a60cc0b5-e69e-4909-8b72-3fbd4fdaf9a8",
              "ValidationErrors": [],
              "PrimaryReason": "d31f99ac-deb9-441b-be53-faf4c1ccba00",
              "EmployeeNumber": "80081254",
              "ClientCode": "GOODYEAR",
              "AbsenceIncident": {
                "AbsenceType": "continuous",
                "QuestionResponses": {},
                "SecondaryReason": "a60cc0b5-e69e-4909-8b72-3fbd4fdaf9a8",
                "FirstName": "Joshua",
                "NextScheduledShift": null,
                "EmployeeNumber": "80081254",
                "BestPhoneNumber": null,
                "AbsenceIncidentId": 22778,
                "RequestDate": "2023-05-08T15:34:54.2720798Z",
                "MaxReportableDays": 120,
                "LinkedIncident": null,
                "PrimaryReason": "d31f99ac-deb9-441b-be53-faf4c1ccba00",
                "ClaimStatus": "I",
                "MaxDaysAllowed": 120,
                "ClientCode": "GOODYEAR",
                "LastName": "Routzon",
                "ProvinceCode": "OH",
                "AbsenceDates": [
                  {
                    "StartDate": "2023-05-08 09:30:00",
                    "ShiftStartTime": "2023-05-08 09:30:00",
                    "ShiftDuration": null,
                    "IsUpdated": true,
                    "EndDate": "2023-05-08 12:30:00",
                    "ShiftEndTime": "2023-05-08 12:30:00",
                    "ScheduledShiftEndTime": null,
                    "ScheduledShiftStartTime": null,
                    "IncidentId": 22778,
                  },
                  {
                    "StartDate": "2023-05-09 13:30:00",
                    "ShiftStartTime": "2023-05-09 13:30:00",
                    "ShiftDuration": null,
                    "IsUpdated": true,
                    "EndDate": "2023-05-09 17:30:00",
                    "ShiftEndTime": "2023-05-09 17:30:00",
                    "ScheduledShiftEndTime": null,
                    "ScheduledShiftStartTime": null,
                    "IsUpdated": false,                    
                    "IncidentId": 22778,
                  },
                  {
                    "StartDate": "2023-05-08 14:30:00",
                    "ShiftStartTime": "2023-05-08 14:30:00",
                    "ShiftDuration": null,
                    "IsUpdated": true,
                    "EndDate": "2023-05-08 17:30:00",
                    "ShiftEndTime": "2023-05-08 17:30:00",
                    "ScheduledShiftEndTime": null,
                    "ScheduledShiftStartTime": null,
                    "IsUpdated": false,                    
                    "IncidentId": 22778,
                  }
                ],
                "ReturnToWorkDate": "2023-05-10 00:00:00",
                "ReportedBy": "Employee"
              },
              "AlertMessages": []
            }
          ],
          "Reasons": {
            "ReasonEntries": [
              {
                "IVROptionNumber": 2,
                "IsLeaf": false,
                "Id": "d31f99ac-deb9-441b-be53-faf4c1ccba00",
                "Description": "STD/Accident & Sickness or FMLA Absence",
                "Child": {
                  "IVROptionNumber": 2,
                  "IsLeaf": true,
                  "Id": "a60cc0b5-e69e-4909-8b72-3fbd4fdaf9a8",
                  "Description": "Associated with an existing intermittent claim",
                  "Child": null
                }
              }
            ],
            "Count": 1
          },
          "ContactId": "45a9d991-cbed-4b83-bab5-35d38cbf5156",
          "IsSaved": "true",
          "EmployeeInfo": {
            "workPostalCode": null,
            "jobTitle": null,
            "supervisorEmail": null,
            "language": "EN",
            "employeeNumberPadded": "000000080081254",
            "employeeNumber": "80081254",
            "organizationId": 142480,
            "homeProvinceCode": null,
            "workPhoneNumber": null,
            "supervisorFirstName": null,
            "businessEmail": null,
            "updatedBy": "g_migration",
            "organizationName": "SUP_40001012-FIN HRLY VRP Unistage Tir",
            "homeAddressLine2": null,
            "supervisorWCBEmail": null,
            "employeeAddresses": [
              {
                "addressTypeCode": "HOME",
                "addressTypeId": 2,
                "countryCode": "US",
                "postalCode": null,
                "addressLine1": null,
                "provinceStateCode": null,
                "addressLine2": null
              },
              {
                "addressTypeCode": "OFFICE",
                "addressTypeId": 1,
                "countryCode": "US",
                "postalCode": "45840",
                "addressLine1": "701 Lima Avenue",
                "provinceStateCode": "OH",
                "addressLine2": null
              }
            ],
            "homeAddressLine1": null,
            "supervisorId": 0,
            "workAddressLine1": null,
            "wageUnit": null,
            "workAddressLine2": null,
            "firstName": "Joshua",
            "homeEmail": null,
            "supervisorLastName": null,
            "companyId": 1576,
            "dateOfHire": "2014-06-03T00:00:00",
            "phoneNumber": "5672081231",
            "salutation": null,
            "updateDateTime": "2023-01-17T16:58:59.547",
            "homeCountry": null,
            "hash": null,
            "supervisorName": null,
            "lastName": "Routzon",
            "gender": "U",
            "workCountry": null,
            "supervisorAREmail": null,
            "middleInitial": null,
            "supervisorEmployeeNumber": null,
            "workAltPhoneNumber": null,
            "isSearchable": true,
            "recordFrom": "DynamicForms",
            "payRateFrequency": null,
            "employmentType": "FULLTIMEREG",
            "emP_Department": null,
            "provinceCode": "OH",
            "employmentTypeDescription": null,
            "employeeId": 1338208,
            "union": null,
            "birthDate": "1993-10-20T00:00:00",
            "homePostalCode": null,
            "createDateTime": "2023-01-17T09:19:34.247",
            "workProvinceCode": null,
            "altPhoneNumber": "9999999999",
            "createdBy": "g_migration",
            "clientCode": "GOODYEAR",
            "supervisorSTDEmail": null,
            "employeeCustomFields": [],
            "employeeWorkStatusId": 0
          },
          "IsSubmitted": "false"
        }
        
      ]
    },
    {
      TableName: 'ablarivr-data-cache',
      KeySchema: [{AttributeName: 'CacheKey', KeyType: 'HASH'}],
      AttributeDefinitions: [{AttributeName: 'CacheKey', AttributeType: 'S'}],
      ProvisionedThroughput: {ReadCapacityUnits: 1, WriteCapacityUnits: 1},
      data:[
        {
          "CacheKey": "claim_k6gkw3e3-a66b-4362-af33-65c752a6844d",
          "data":{
          "items": [
            {
              "lastDateOfAbsence": null,
              "claimDates": [
                {
                  "isPartialAbsence": null,
                  "unpaidTimeInMinutes": null,
                  "endDate": "2022-11-21T17:00:00-05:00",
                  "startDate": "2022-11-21T09:00:00-05:00",
                  "scheduledShiftStartTime": "09:00AM",
                  "scheduledShiftEndTime":"05:00PM"
                },
                {
                  "isPartialAbsence": null,
                  "unpaidTimeInMinutes": null,
                  "endDate": "2022-11-22T17:00:00-05:00",
                  "startDate": "2022-11-22T09:00:00-05:00",
                  "scheduledShiftStartTime": "09:00AM",
                  "scheduledShiftEndTime":"05:00PM"
                },
                {
                  "isPartialAbsence": null,
                  "unpaidTimeInMinutes": null,
                  "endDate": "2022-11-23T17:00:00-05:00",
                  "startDate": "2022-11-23T09:00:00-05:00",
                  "scheduledShiftStartTime": "09:00AM",
                  "scheduledShiftEndTime":"05:00PM"
                },
                {
                  "isPartialAbsence": null,
                  "unpaidTimeInMinutes": null,
                  "endDate": "2022-11-24T17:00:00-05:00",
                  "startDate": "2022-11-24T09:00:00-05:00",
                  "scheduledShiftStartTime": "09:00AM",
                  "scheduledShiftEndTime":"05:00PM"
                }
              ],
              "incidentId": 298711,
              "leaveEndDate": "2022-11-24T00:00:00",
              "incidentTypeCssClass": "INCIDENT_GENERIC_LEAVE"
            }
          ],
          "ContactId": "k6gkw3e3-a66b-4362-af33-65c752a6844d",
      }
    }
      ]
    },
    {
      TableName: 'ablarivr-client-config',
      KeySchema: [{AttributeName: 'ClientName', KeyType: 'HASH'}],
      AttributeDefinitions: [{AttributeName: 'ClientName', AttributeType: 'S'},{AttributeName: 'ClientCode', AttributeType: 'S'}],
      ProvisionedThroughput: {ReadCapacityUnits: 1, WriteCapacityUnits: 1},
      GlobalSecondaryIndexes: [
        {
          IndexName: "ClientCodeIdx",
          KeySchema: [
            {
              AttributeName: "ClientCode",
              KeyType: "HASH"
            }
          ],
          Projection: {
            ProjectionType: "ALL"
          },
          ProvisionedThroughput: {
            ReadCapacityUnits: 1,
            WriteCapacityUnits: 1
          }
        }
      ],
      data: [  {   
        ClientName: "GOODYEAR",
      ClientCode: "STARBUCKS",
      ClientSysId: "1",
      DefaultDateFormatType: "1",
      DefaultLanguage: "English",
      DefaultTimeFormatType: "1",
      IsEnterEmployeePhoneNumberRequired: "true",
      TransferNumberGeneralError: "+15012355529",
      PromptEnterEmployeeIdSSML:"<speak><amazon:domain name='conversational'> Please enter your employee I D using your keypad followed by pound key. If you do not know your employee I D, please disconnect and contact your employer to report your absence.</amazon:domain></speak>",
      PromptInvalidEmployeeIdSSML1:"<speak><amazon:domain name='conversational'>The Employee ID you entered is</amazon:domain></speak>",
      PromptInvalidEmployeeIdSSML2:"<speak><amazon:domain name='conversational'>This does not match with our records. Please try again</amazon:domain></speak>",
      PromptMaxInvalidEmployeeIdSSML:"<speak><amazon:domain name='conversational'>Sorry, your absence cannot be recorded without your correct Employee number Please call back later Goodbye.</amazon:domain></speak>",
      MaxAllowAbsenceDaysFuture: "60",
      MaxAllowAbsenceDaysPast: "14",
      MaxRetryLambda1: 3,
      MaxRetryLambda2: 3,
      MaxRetryMenu1: "3",
      MaxRetryMenu2: "",
      MaxTotalMissedAbsenceDays: 5,
      PreviousAbsenceWithinAA: "2",
      PromptTransferNumberGeneralErrorSSML: "",
      MaxValidAbsenceDays: 60,
      MaxBeforeAbsenceDays: 2,
      ClientDateFormat: "MMDDYYYY"
    },  ],
    },
    {
      TableName: 'ablarivr-reasonmenu',
      KeySchema: [{AttributeName: 'ClientName', KeyType: 'HASH'}],
      AttributeDefinitions: [{AttributeName: 'ClientName', AttributeType: 'S'}],
      ProvisionedThroughput: {ReadCapacityUnits: 1, WriteCapacityUnits: 1},
    }
  ],
  basePort: 8003
};