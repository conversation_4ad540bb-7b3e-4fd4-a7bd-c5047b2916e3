import { CanBeALinkedIncident, ClientConfigWrapper, LinkedAbsResult } from '@/models';
import {AbsenceApiService, CallLog, LocalConfiguration, ScopedHttpService} from '@/services'
import { PromptHelper, RefAbsenceType} from '@/util'
import moment from 'moment';
import { ConnectContactFlowEvent } from 'aws-lambda'


const callService = new CallLog();
const localConfig = new LocalConfiguration();
const absenceApi = new AbsenceApiService();

export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'checkCanBeLinked';
    callService.Logger.log('Input data', event);
    let contactId= event.Details.ContactData.ContactId;
    let result: LinkedAbsResult = {
        PromptLinkIncidentSSML: '',
        CanBeLinked: false,
        AA: 0,
        IsAbsenceIntermittent: true,
        IsContinuousFullDaySelected: false
    }
    const callData = await callService.getCallDataAsync(contactId);
    localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
    let configuration: ClientConfigWrapper = await localConfig.getClientConfigAsync(callData.ClientCode);
    let selectedLang = callData.SelectedLang ?? "EN";
    let currentAbsence = (callData.Absences)?.peekLast();
    
    if(currentAbsence.AbsenceIncident && callData.Reasons)
    {
        if(callData.CanBeLinkedResult == null || callData.CanBeLinkedResult == undefined)
        {
            if(currentAbsence.AbsenceIncident.AbsenceType === RefAbsenceType.Continuous)
            {
                //1. send absence dates to API 
                callService.Logger.log('Updating absence inside checkCanBeLinked');
                let updatedIncident = await absenceApi.updateIncidentAsync(callData.ClientCode, contactId, currentAbsence.AbsenceIncident );
                if(updatedIncident.AbsenceIncident != null && updatedIncident.ValidationErrors.length == 0){
                    currentAbsence = {...currentAbsence, ...updatedIncident};
                    callService.Logger.log('Update absence dates', JSON.stringify(currentAbsence.AbsenceIncident));
                    callService.Logger.log('Update absence', JSON.stringify(currentAbsence.QuestionReponse));
                    callData.Absences.pop();
                    callData.Absences.push(currentAbsence);
                    await callService.setCallDataAsync(contactId, {
                        IsSaved: "true",
                        Absences: callData.Absences
                    });
                }
                else callService.Logger.log('Update absence dates has failed', JSON.stringify(currentAbsence.AbsenceIncident));
            }
            
            
            let canBeLinkedResult = await absenceApi.canBeALinkedIncident(contactId, currentAbsence.AbsenceIncident);
            await callService.setCallDataAsync(contactId, {
                IsSaved: "true",
                CanBeLinkedResult: canBeLinkedResult
            });
            callService.Logger.log('getting canbelinked from ddb lambda invoked 1st time ');
            result = ConstructResultResponse(callData, configuration, selectedLang, canBeLinkedResult);
        }
        else if (callData.CanBeLinkedResult.CanBeLinked)
        {
            callService.Logger.log('getting canbelinked from ddb lambda invoked 2nd time ');
            result = ConstructResultResponse(callData, configuration, selectedLang);
        }
        callService.Logger.log('The absence type is - ', currentAbsence.AbsenceIncident.AbsenceType);
        if(currentAbsence.AbsenceIncident.AbsenceType == RefAbsenceType.Continuous)
        {
            result.IsAbsenceIntermittent = false;
            result.IsContinuousFullDaySelected = true;
        }
    }
    callService.Logger.log('Response', result);
    return result;
}

function ConstructResultResponse(callData: any, configuration: ClientConfigWrapper, selectedLang: string, canBeLinkedResult?: CanBeALinkedIncident): LinkedAbsResult
{
    let result: LinkedAbsResult = {
        PromptLinkIncidentSSML: '',
        CanBeLinked: false,
        IsAbsenceIntermittent: true,
        IsContinuousFullDaySelected: false,
        AA: 0 
    }
    callService.Logger.log('Can be linked - true');
//You recently used the {Primary and Secondary Absence Reason} reasons for absence. Have you returned to work since {absence date). If yes, press 1, If No, press 2
    let currentReason = callData.Reasons.ReasonEntries[0];

    let rName = currentReason.Description.concat('.   ', currentReason.Child?.Description);
    
    let linkedPrompText = callData.CanBeLinkedResult != undefined ? PromptHelper.getMergedPrompt(configuration.clientConfig.RtwText1 ?? [], selectedLang, "{LastAbsenceDate}",  
                moment.parseZone(callData.CanBeLinkedResult.LatestDate , ).startOf('day').format("YYYY-MM-DD"), 'date', true)
                :PromptHelper.getMergedPrompt(configuration.clientConfig.RtwText1 ?? [], selectedLang, "{LastAbsenceDate}",  
                moment.parseZone(canBeLinkedResult?.LatestDate, ).startOf('day').format("YYYY-MM-DD"), 'date', true);
    
    linkedPrompText = linkedPrompText.replace('{AbsenceReason}', rName);
    result.CanBeLinked = callData.CanBeLinkedResult?.CanBeLinked ?? canBeLinkedResult?.CanBeLinked;
    result.PromptLinkIncidentSSML = linkedPrompText;
    result.AA =  configuration.clientConfig.LinkedIncidentsLookbackThreshold ?? 7;
    return result;
}