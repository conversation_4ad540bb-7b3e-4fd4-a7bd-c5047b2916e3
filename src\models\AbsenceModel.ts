import moment, {Moment} from "moment";
import { ReasonText } from "./ReasonModel";
import { SimpleObject } from "@/types";
export interface AbsenceDates{
    IncidentId?: number | string,
    StartDate?: Moment | string,
    EndDate?:  Moment |string,
    ShiftStartTime?: Moment| string,
    ShiftEndTime?: Moment|string,
    ShiftDuration?: string | null,
    ScheduledShiftStartTime?: string | Moment ,
    ScheduledShiftEndTime?: string | Moment ,
    IsOverLap?: boolean,
    IsDuplicate?: boolean,
    IsPartialAbsence?: boolean,
    OverlapIncidentId?: number|string ,
    IsUpdated?: boolean | null,
    UnpaidTimeInMinutes?: number
}
// base model used to send/recieve absence data while creating, updating submitting absence
export interface AbsenceIncident {
    AbsenceIncidentId? : number | string,
    EmployeeNumber?: string ,
    ClientCode? : string ,
    FirstName?: string ,
    LastName?: string ,
    BestPhoneNumber?: string ,
    ProvinceCode?: string ,
    ReturnToWorkDate?: string ,
    NextScheduledShift?: string ,
    RequestDate?: string |null ,
    PrimaryReason?: string ,
    SecondaryReason?: string ,
    AbsenceType?: string | null ,
    ReportedBy?: string| null ,
    ReportingLocale?: string | null ,
    AbsenceDates?: AbsenceDates[] | null ,
    QuestionResponses?: SimpleObject ,
    LinkedIncident?: boolean | null ,
    ClaimStatus?: string | null,
    MaxDaysAllowed?: number | null,
    MaxReportableDays?: number | null
    ReasonConfig?: AbsenceConfig | null;
    AbsenceSource?: string;
}

export interface AbsenceConfig {
  ReportableDaysInFuture?: number;
  ReportableDaysInPast?: number;
  MaxDaysAllowed?:number;
}

export class AlertMessageResponse {
  AlertId: string = "";
  AlertText: ReasonText[] = [];
  AlertLevel?: string;
}

export interface SaveAbsenceIncidentReponse{
  AbsenceIncident: AbsenceIncident,
  //AbsenceQuestionResponse: Record<string, string>,
  ValidationErrors: ValidationErrors[]
  AlertMessages?: AlertMessageResponse[];
  QuestionReponse?: PageModel[]
}
export interface SubmitAbsenceResponse{
  AbsenceIncident: AbsenceIncident,
  AbsenceClosingScriptModel: AbsenceClosingScriptModel[],
  ValidationErrors: ValidationErrors[]
}

export interface CanBeALinkedIncident{
  CanBeLinked: boolean,
  LatestDate: string | Moment,
  IncidentToBeLinked: Number | string,
  TotalNoOfDaysReportedExcludingCurrent: Number | string
}
export class PageModel{
  PageId: string = '';
  PageHeading?: LocaleModel[] = [];
  PageHeadingDescription?: LocaleModel[] = [];
  Sections?: PageSectionModel[] =[];
  public static getTypeDef(propName: string): any {
    switch (propName) {
        case 'Sections':
            return new PageSectionModel();
        case 'Questions':
            return new QuestionModel();
        case 'Options':
            return new DropDownOption();
        case 'Alerts':
            return new AlertMessageResponse();
        case 'Locales':
        case 'locales':
        case 'label':
        case 'Label':
        case 'text':
        case 'Text':
        case 'longDescription':
        case 'LongDescription':
        case 'AlertText':
        case 'alertText':
            return {Locale: '', Description: ''}
    }
}

}
export class PageSectionModel{
  SectionId: string = '';
  SectionHeading?: LocaleModel[] =[];
  SectionHeadingDescription?: LocaleModel[] =[];
  Questions?: QuestionModel[] =[];
}
export class QuestionModel{
  Name: string = '';
  Label: LocaleModel[] = [];
  LongDescription?: LocaleModel[] =[];
  PlaceHolder?: LocaleModel[] =[];
  Source?: string = '';
  QuestionType?: string = '';
  IsVisible?: boolean = true;  
  Options?: DropDownOption[]= [];
  Alerts?: AlertMessageResponse[] = [];
}
export class DropDownOption{
  Text: LocaleModel[] =[];
  Value: string ='';
 // Metadata: Record<string, string>;
}
export interface CancelAbsenceModel
{
  CancelReason : string,
  CancelComment: string
}
export interface AbsenceClosingScriptModel{
  ClosingScriptId: string,
  Text: LocaleModel[],
  RedirectUrl?: string | null,
  DisplayOptionType?: string | null;
}
export interface LocaleModel{
  Locale?: string | null,
  Description?: string| null,
  ShortDescription?: string | null
}

export interface ReasonFlow {
  ReasonEntries: ReasonEntry[];
  Count: number;
}

export interface ReasonEntry {
  Id: string;
  Description: string; 
  IVROptionNumber: string | number;
  Child: ReasonEntry | null;
  IsLeaf: boolean;
}
export interface ValidationErrors{
  ErrorCode: string,
  ErrorDescription: string,
  ErrorSeverity: number,
  ErrorProvider: string,
  ErrorParameters?: ErrorParameter | null,
  ErrorFields?: string[],
  MetaData?: any
}

interface ErrorParameter{
  PropertyName: string | null,
  PropertyValue: any
}

export interface ValidateAbsenceShiftResponse{
  AbsenceDate: AbsenceDates,
  ValidationErrors: ValidationErrors[],
  Success: boolean
}
export interface LinkedAbsResult{
  PromptLinkIncidentSSML: string,
  CanBeLinked: boolean,
  AA: number,
  IsContinuousFullDaySelected: boolean,
  IsAbsenceIntermittent: boolean
}

function deepMap(obj:any, dest: any): any {
  let toReturn:any = {};
  let PageMapping = new Map();
  Object.keys(dest).forEach( x=> PageMapping.set(x.toLowerCase(), x));
  Object.entries(obj).forEach(el => {
      let prop = PageMapping.get(el[0].toLowerCase());
      if (prop) {
      if (Array.isArray(el[1])) {
          let newArr:any [] = [];
          el[1].forEach(x => {
              if (typeof(x) === 'object'){
                  //TODO for now
                  newArr.push(deepMap(x, PageModel.getTypeDef(prop))); //TODO -> typesafe, implement type specifier in this scenario
              }
              else {
                  newArr.push(x);
              }
          });
          toReturn[prop] = newArr;
      }
      else if (el[1] && typeof(el[1]) === 'object') {
          toReturn[prop] = deepMap(el[1], dest[prop]);
      }
      else {
          toReturn[prop] = el[1];
      }
  }
  });
  return toReturn;
}
