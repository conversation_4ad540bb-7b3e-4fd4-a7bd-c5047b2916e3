'use strict';

import moment from 'moment';
import {ConnectContactFlowEvent} from 'aws-lambda';
import {AbsenceApiService, AbsenceReportingService, CallLog, LocalConfiguration, ScopedHttpService} from '@/services';
import {DateHelper, ValidationErrors, PromptHelper, Constants} from '@/util';
import { AbsenceDates, AbsenceIncident } from '@/models';

const callService = new CallLog();
const reportingService = new AbsenceReportingService();
reportingService.CallLog = callService;
const dateHelperService = new DateHelper();
const localConfig = new LocalConfiguration();
const absenceApi = new AbsenceApiService();
interface IResult {
        IsShiftEndTimeValid: boolean,
        ShiftTimeNotValidReason: string,
        IsSaveSuccess: boolean,
        ShiftTimeValidation: boolean,
        PromptEndTimeValidationSSML: ''
}

export const handler = async (event: ConnectContactFlowEvent) => {
  const lambdaName = 'validateShiftEndTime';
  reportingService.Logger.log('Input data', event);
  let contactId;
  //let currentIncident: AbsenceIncident | undefined;
  let isValid: boolean;
  let result: IResult = {
    IsShiftEndTimeValid: false,
    ShiftTimeNotValidReason: '',
    IsSaveSuccess: false,
    ShiftTimeValidation: true,
    PromptEndTimeValidationSSML: ''
  };
  let ValidPath = (result: IResult) => result.ShiftTimeNotValidReason.length === 0;
  try {
    contactId = event.Details.ContactData.ContactId;
    const shiftEndTime = event.Details.Parameters.ShiftEndTime;

    const aMorPM = +event.Details.Parameters.AMorPM === 1 ? 'AM' : 'PM';
    if ((isValid = shiftEndTime.length === 4)) {
      const [hour, min] = [shiftEndTime.slice(0, 2), shiftEndTime.slice(2, 4)];

      isValid = isValid && dateHelperService.is12HClockCorrect(parseInt(hour), parseInt(min), aMorPM === 'AM');
      result.IsShiftEndTimeValid = isValid;
      result.ShiftTimeNotValidReason = !isValid
        ? ValidationErrors.InvalidShiftStartTime
        : '';

        reportingService.Logger.log(`${hour} hour, ${min} min is valid 12h clock time: ${isValid}`);

      const callData = await callService.getCallDataAsync(contactId);

      let currentIncident = (callData.Absences)?.peekLast();
      let selectedLang = callData.SelectedLang ?? 'en';
      const inputTime = `${shiftEndTime} ${aMorPM}`;
      reportingService.Logger.log(`Shift End Time: ${inputTime}`);

      const EndDate = dateHelperService.convertTo24HClock(inputTime);
      reportingService.Logger.log(`Converted to 24h format: ${EndDate}`);
      localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
      let config = await localConfig.getClientConfigAsync(callData.ClientCode);
      let maxShiftLengthThresholdInSec = (config.clientConfig?.MaxShiftLengthThresholdInMin ?? 1439) * 60;
      if (currentIncident && currentIncident.AbsenceIncident && ValidPath(result)) {
        const reportedDates: AbsenceDates[] =  currentIncident.AbsenceIncident?.AbsenceDates || [];       

        if (reportedDates && reportedDates.length > 0) {
          reportingService.Logger.log('Found reported absence date to add shift');
          let lastReport = reportedDates.peekLast();
          if (lastReport) {
            lastReport.ShiftEndTime = moment(lastReport.ShiftStartTime).add(maxShiftLengthThresholdInSec, 's').format('YYYY-MM-DD HH:mm:ss');
            lastReport.EndDate = moment(lastReport.ShiftStartTime).add(maxShiftLengthThresholdInSec, 's').format('YYYY-MM-DD HH:mm:ss');
            const shiftStartTime = moment(lastReport.ShiftStartTime);
            let endTime = moment(EndDate, "HH:mm");
            if (ValidPath(result)) {

              if(shiftStartTime.hours() < endTime.hours() || (shiftStartTime.hours() == endTime.hours() && shiftStartTime.minutes() < endTime.minutes()))
              {
                lastReport.ShiftEndTime = moment(lastReport.ShiftStartTime).format('YYYY-MM-DD');
                lastReport.EndDate = moment(lastReport.ShiftStartTime).format('YYYY-MM-DD');
                //await AbsenceReportingService.updateAbsenceDate(lastReport, {EndDate});
              }
              await AbsenceReportingService.updateAbsenceDate(lastReport, {EndDate});
              let shiftDurationInSec = moment(lastReport.ShiftEndTime).diff(moment(lastReport.ShiftStartTime), 'seconds',);    
              let shiftDurationInMin = moment(lastReport.ShiftEndTime).diff(moment(lastReport.ShiftStartTime), 'minutes',);    
              reportingService.Logger.log(lastReport);
              reportingService.Logger.log(`shift duration: ${shiftDurationInSec} seconds`);
              reportingService.Logger.log(`shift duration: ${shiftDurationInMin} minutes`);

              if (shiftDurationInMin % (config.clientConfig.AbsenceDurationMinutes ?? 15) != 0) {
                  (result.ShiftTimeNotValidReason =
                      ValidationErrors.ShiftTimeShouldBeIncrementalOfConfig),
                      reportingService.Logger.log('Result', result);
              };
              if (shiftDurationInSec <= 0) {
                (result.ShiftTimeNotValidReason =
                  ValidationErrors.ShiftEndTimeEarlierThanStartTime),
                  reportingService.Logger.log('Result', result);
              };
              if (shiftDurationInSec > maxShiftLengthThresholdInSec) {
                (result.ShiftTimeNotValidReason =
                  ValidationErrors.ShiftDurationTooLate),
                  reportingService.Logger.log('Result', result);
              }
            if((config.clientConfig.EnableMultipleShift ?? false))
            {
              callService.Logger.log('Multiple shift enabled');
              var validShift = await absenceApi.validateAbsenceShift(contactId, currentIncident.AbsenceIncident.AbsenceIncidentId,lastReport );
              callService.Logger.log('Conflict shift checked with result', JSON.stringify(validShift));
              if(!validShift.Success)
              {
                let conflictAbsenceDt = validShift.ValidationErrors?.filter(e => e.ErrorCode == Constants.Error_Code_Absence_Shift_End)?.peekLast()?.MetaData;
                if(conflictAbsenceDt == null || undefined )
                    conflictAbsenceDt = validShift.ValidationErrors?.filter(e => e.ErrorCode == Constants.Error_Code_Absence_WholeShift)?.peekLast()?.MetaData;
                let startTimeAMPM = moment.parseZone(conflictAbsenceDt.startDate).format('hh:mm A');
                callService.Logger.log('conflict shift start time as ', startTimeAMPM);
                let endTimeAMPM = moment.parseZone(conflictAbsenceDt.endDate).format('hh:mm A');
                callService.Logger.log('conflict shift end time as ', endTimeAMPM);
                let conflictPromt = PromptHelper.getMergedPrompt(config.clientConfig.ConflictingAbsenceShiftText ?? [], selectedLang,  "{AbsenceDate}", moment.parseZone(conflictAbsenceDt.startDate, ).startOf('day').format("YYYY-MM-DD"), 'date', true);
                conflictPromt = conflictPromt.replaceAll('{ShiftStartTime}', startTimeAMPM).replace('{ShiftEndTime}', endTimeAMPM);
                result.PromptEndTimeValidationSSML = conflictPromt;
                result.ShiftTimeValidation = false;
                result.IsSaveSuccess = false;
                reportingService.Logger.log('Result', result);
                return result;
                

              }
            }
              // if (ValidPath(result)) {
              //   lastReport.ShiftDuration = moment
              //     .utc(shiftDurationInSec * 1000)
              //     .format('HH:mm:ss');
              // }
            }
          }
          }
          reportingService.Logger.log(currentIncident.AbsenceIncident);
        //await callService.logStageAsync(contactId, lambdaName, null, result);
          if (result.ShiftTimeNotValidReason.length <= 0) {
              callData.Absences.pop();
              callData.Absences.push(currentIncident);
              reportingService.Logger.log('Call Data Absence', callData.Absences);
              await callService.setCallDataAsync(contactId, { Absences: callData.Absences }, true);
              result.IsSaveSuccess = true;
          }
          return result;
      }
    }
    return await handleValidationErrorAsync(result, contactId, lambdaName);
  } catch (error: any) {
    reportingService.Logger.log('Validate Shift End Time Error', error);
  }
};


async function handleValidationErrorAsync(result: IResult, contactId: string, lambdaName: string): Promise<IResult> {
    await callService.logStageAsync(contactId, lambdaName, result.ShiftTimeNotValidReason, result);
    result.IsShiftEndTimeValid = !((result.ShiftTimeNotValidReason !== null) && result.ShiftTimeNotValidReason.length > 0); 
    return result;
}

async function saveAndExit (contactId: string, result: IResult, incidentArray: AbsenceIncident[], currentIncident: AbsenceIncident, lambdaName: string): Promise<IResult> {
  if(result.ShiftTimeNotValidReason.length <= 0)  {
    incidentArray.push(currentIncident);
    await callService.setCallDataAsync(contactId, {Absences: incidentArray }, true);
    result.IsSaveSuccess = true;
  }
    return await handleValidationErrorAsync(result, contactId, lambdaName)
}

/*

  if (lastReport.IsOverLap) {
              result = await handleValidationErrorAsync(
                ValidationErrors.OverlappedEntry,
                contactId,
                lambdaName,
              );
              reportingService.Logger.log('Result', result);
              return result;
            }
            if (lastReport.IsDuplicate) {
              result = await handleValidationErrorAsync(
                ValidationErrors.DuplicateEntry,
                contactId,
                lambdaName,
              );
              reportingService.Logger.log('Result', result);
              return result;
            }
*/