import moment from 'moment';
import { LocaleModel } from './AbsenceModel';
import { LogService, PromptHelper } from '@/util'
import {  LocalConfiguration } from '@/services'



export interface AwsClientConfig {
    ClientCode: string;
    //CallFlowConfig: CallflowConfig;
    CallFlowGetOpeningMsgV1ARN?: string;
    CallFlowDynamicFlowV1ARN?: string;
    CallFlowValidateEmployeeARN?: string;
    CallFlowEnterTotalAbsenceDaysARN?: string;
    CallFlowEnterReasonARN?: string;
    CallFlowEnterAbsenceDateARN?: string;
    CallFlowEnterRTWARN?: string;
    CallFlowEnterEmployeePhoneNumberARN?: string;
    CallFlowSubmitAbsenceARN?: string;
    CallFlowSubmitAbsenceIfErrorARN?: string;
    CallFlowTransferToExternalARN?: string;
    CallFlowTransferToCSRARN?: string;
    CallFlowClosingARN?: string;
    CallFlowEnterNextAbsenceDateARN?: string;
    CallFlowGetOpenLeaveCaseARN?: string;
    CallFlowSTDLeaveARN?: string;
    CallFlowLinkAbsenceInAbilitiARN?: string; 
    CallFlowDynamicFlowV2ARN?: string; 
    CallFlowEnterAbsenceDateV2ARN?: string; 
    CallFlowEnterNextAbsenceDateV2ARN?: string;
    CallFlowEnterDateRange1ARN?: string;
    CallFlowEnterDateRange2ARN?: string;
    CallFlowVoicemailMessageARN?: string;
    LambdaGetOpeningMsgV1ARN?: string;
    LambdaGetClientConfigARN?: string;
    LambdaValidateEmployeeIdARN?: string;
    LambdaValidateDoBARN?: string;
    LambdaAuthenticateEmployeeARN?: string;
    LambdaSetReturnToWork?: string;
    LambdaSaveTotalAbsenceDaysARN?: string;
    LambdaGetPrimaryReasonMenuARN?: string;
    LambdaGetSecondaryReasonMenuARN?: string;
    LambdaSaveSecondaryReasonARN?: string;
    LambdaGetCarrierTransferNumberARN?: string;
    LambdaValidateAbsenceDateARN?: string;
    LambdaSaveAbsenceDateARN?: string;
    LambdaValidateShiftStartTimeARN?: string;
    LambdaValidateShiftEndTimeARN?: string;
    LambdaCheckAllAbsReportedARN?: string;
    LambdaValidateReturnToWorkDateARN?: string;
    LambdaValidateRTWShiftStartTimeARN?: string;
    LambdaValidateEmployeePhoneNumberARN?: string;
    LambdaSubmitAbsenceARN?: string;
    LambdaResolveConflictAbsenceARN?: string;
    LambdaSetReturnToWorkARN?: string;
    LambdaSaveReturnToWorkDateARN?: string;
    LambdaClearLastAddedAbsenceDateARN?: string;
    LambdaDroppedSessionMonitorARN?: string;
    LambdaCheckOpenCasesARN?: string;
    LambdaCheckEESTDARN?: string;
    LambdaSaveSTDResponseARN?: string;
    LambdaSaveThirdLevelResponseARN?: string;
    LambdaLinkAbsenceInAbilitiARN?: string;
    LambdaSaveLeaveResponseARN?: string;
    LambdaVMCallbackARN?: string;
    DefaultLanguage?: string;
    MaxRetryLambda1?: string;
    MaxRetryLambda2?: string;
    MaxRetryMenu1?: string;
    MaxRetryMenu2?: string;
    MaxTotalMissedAbsenceDays?: string;
    MaxAllowAbsenceDaysPast?: string;
    MaxAllowAbsenceDaysFuture?: string;
   // PreviousAbsenceWithinAA?: string;
    DefaultDateFormatType?: string;
    DefaultTimeFormatType?: string;
    IsEnterEmployeePhoneNumberRequired?: string | boolean;
    PromptTransferNumberGeneralErrorSSML?: string;
    PromptEnterEmployeeIdSSML?: string;
    PromptInvalidEmployeeIdSSML1?: string;
    PromptInvalidEmployeeIdSSML2?: string;
    PromptMaxInvalidEmployeeIdSSML?: string;
    PromptSpecialOpeningMsgSSML?: string;
    PromptOpeningSSML?: LocaleModel[];
    PromptSubOpeningSSML?: string;
    IsAllowInteruppted?: string;
    PromptLanugageSelectionS3URI?: string;
    PromptCarrierTransferMsgSSML?: string;
    PromptIntroPrimaryReasonMenuSSML?: string;
    PromptIntroSecondaryReasonMenuSSML?: string;
    ClientDateFormat?: string;
    CarrierTransferNumber?: string;
    MaxValidAbsenceDays?: number | string;
    MaxBeforeAbsenceDays?: number | string;
    PromptCompletedAbsenceSSML?: string;
    PromptOpeningSpecialMsgSSML?: string;
    PromptSubOpeningSpecialMsgSSML?: string;
    PromptMaxRetrySSML?: string;
    PromptUnexpectedErrorSSML?: string;
    PromptIsReturnToWorkSSML1?: string;
    PromptIsReturnToWorkSSML2?: string;
    PromptIsAMorPMSSML?: string;
    PromptInvalidEntryAndRetrySSML?: string;
    PromptInvalidAbsenceDateSSML?: string;
    PromptTransferToDisconnectSSML?: string;
    PromptEnterDobSSML?: string;
    PromptAuthEmployeeFailSSML1?: string;
    PromptAuthEmployeeFailSSML2?: string;
    PromptEnterTotalAbsenceDaysSSML1?: string;
    PromptEnterTotalAbsenceDaysSSML2?: string;
    PromptSecondaryReasonSpecialMsgSSML?: string;
    PromptPrimaryReasonMenuOptionsSSML?: string;
    PromptPrimaryReasonAlertMsgSSML?: string;
    PromptSecondaryReasonMenuOptionsSSML?: string;
    PromptSecondaryReasonAlertMsgSSML?: string;
    PromptIsRTWKnownSSML?: string;
    //CancelThresholdInMinutes?: number | null;
    PromptEnterAbsenceDateSSML?: string;
    PromptValidateIDandDobFailSSML?: string;
    PromptInvalidReturnToWorkSSML?: string;
    PromptFailTrasnferToCarrierSSML?: string;
    PromptConfirmReturnToWorkSSML1?: string;
    PromptConfirmAbsenceDateSSML1?: string;
    //PromptMaxInvalidEmployeeIdSSML?: string;
    //PromptOpeningSSML?: string;
    //PromptCompletedAbsenceSSML?: string;
    PromptEnterStartShiftTimeSSML?: string;
    PromptEnterEndShiftTimeSSML?: string;
    PromptEnterReturnToWorkSSML?: string;
    PromptProvideConfirmNumberSSML1?: string;
    PromptTransferToAgentSSML?: string;
    PromptTransferToCarrierSSML?: string;
    PromptProvideConfirmNumberSSML2?: string;
    PromptEnterEmployeePhoneNumberSSML?: string;
    IsRTWTimeRequiredForIVR?: string |boolean;
    PromptFullorPartialAbsenceSSML?: string;
    PromptEnterFirstAbsenceDateSSML?: string;
    PromptEnterLastAbsenceDateSSML?: string;
    EnableVoicemail?: string | boolean;
    PromptLeaveVoicemailSSML?: string;
    PromptRecordMessageSSML?: string;
    PromptBeep?: string;
}
export interface CallflowConfig {
    CallFlowGetOpeningMsgV1ARN?: string;
    CallFlowDynamicFlowV1ARN?: string;
    CallFlowValidateEmployeeARN?: string;
    CallFlowEnterTotalAbsenceDaysARN?: string;
    CallFlowEnterReasonARN?: string;
    CallFlowEnterAbsenceDateARN?: string;
    CallFlowEnterRTWARN?: string;
    CallFlowEnterEmployeePhoneNumberARN?: string;
    CallFlowSubmitAbsenceARN?: string;
    CallFlowSubmitAbsenceIfErrorARN?: string;
    CallFlowTransferToExternalARN?: string;
    CallFlowTransferToCSRARN?: string;
    CallFlowClosingARN?: string;
    CallFlowEnterNextAbsenceDateARN?: string;
    CallFlowGetOpenLeaveCaseARN?: string;
    CallFlowSTDLeaveARN?: string;
    CallFlowLinkAbsenceInAbilitiARN?: string; 
    CallFlowDynamicFlowV2ARN?: string; 
    CallFlowEnterAbsenceDateV2ARN?: string; 
    CallFlowEnterNextAbsenceDateV2ARN?: string;
    CallFlowEnterDateRange1ARN?: string;
    CallFlowEnterDateRange2ARN?: string;
    CallFlowVoicemailMessageARN?: string;
    LambdaGetOpeningMsgV1ARN?: string;
    LambdaGetClientConfigARN?: string;
    LambdaValidateEmployeeIdARN?: string;
    LambdaValidateDoBARN?: string;
    LambdaAuthenticateEmployeeARN?: string;
    LambdaSetReturnToWork?: string;
    LambdaSaveTotalAbsenceDaysARN?: string;
    LambdaGetPrimaryReasonMenuARN?: string;
    LambdaGetSecondaryReasonMenuARN?: string;
    LambdaSaveSecondaryReasonARN?: string;
    LambdaGetCarrierTransferNumberARN?: string;
    LambdaValidateAbsenceDateARN?: string;
    LambdaSaveAbsenceDateARN?: string;
    LambdaValidateShiftStartTimeARN?: string;
    LambdaValidateShiftEndTimeARN?: string;
    LambdaCheckAllAbsReportedARN?: string;
    LambdaValidateReturnToWorkDateARN?: string;
    LambdaValidateRTWShiftStartTimeARN?: string;
    LambdaValidateEmployeePhoneNumberARN?: string;
    LambdaSubmitAbsenceARN?: string;
    LambdaResolveConflictAbsenceARN?: string;
    LambdaSetReturnToWorkARN?: string;
    LambdaSaveReturnToWorkDateARN?: string;
    LambdaClearLastAddedAbsenceDateARN?: string;
    LambdaDroppedSessionMonitorARN?: string;
    LambdaCheckOpenCasesARN?: string;
    LambdaCheckEESTDARN?: string;
    LambdaSaveSTDResponseARN?: string;
    LambdaSaveThirdLevelResponseARN?: string;
    LambdaLinkAbsenceInAbilitiARN?: string;
    LambdaSaveLeaveResponseARN?: string;
    LambdaVMCallbackARN?: string;
}

export class ClientConfigWrapper {     
    clientConfig: ClientConfigModel;
    constructor(config: ClientConfigModel) {
        this.clientConfig = config;
    }
    public getPromptText(prompt?: LocaleModel[], locale: string = 'en', convertToSSML: boolean = true, mergeDict: any = null) {
        if (Array.isArray(prompt) && prompt.length > 0) {
            var returnMessage = convertToSSML ? PromptHelper.filterAndWrapSSML(prompt, locale) : prompt.find(x => x.Locale?.toLowerCase() === locale.toLowerCase())?.Description ?? "";            
            if (mergeDict?.length > 0) {
                for (let index in mergeDict) {
                    if (mergeDict[index] != null)                         
                        returnMessage = PromptHelper.getFormattedMessage(returnMessage, mergeDict[index].key, mergeDict[index].value);                    
                }
            }
            return returnMessage;
        }

        return '';
    }
}
export class ClientConfigModel {
    ClientCode: string = "";
    WelcomeMessage: string = "";
    UpdateTimeStamp: string = moment(Date.now()).toString();
    DefaultLanguage: string = "EN";
    MultiLanguageId: string = "0"; //EN=0, EN+ES=1, EN+FR=2
    OpeningStatement?: LocaleModel[] = [];
    SpecialOpeningStatement?: LocaleModel[] = [];
    SubOpeningStatement?: LocaleModel[] = [];
    SubOpeningSpecialStatement?: LocaleModel[] = [];
    //MaxDaysAllowed?: number;
    MaxAllowAbsenceDaysFuture?: number | null;
    MaxAllowAbsenceDaysPast?: number | null;
    MaxTotalMissedAbsenceDays?: number | null;
    PreviousAbsenceWithinAA?: number | null;
    FailedAttemptValue?: number | null;
    MaxRetryValue?: number | null;
    MaxRetryMenu1?: number | null;
    MaxRetryMenu2?: number | null;
    //CancelThresholdInMinutes?: number | null;
    RepeatMenuValue?: number | null;
    CarrierTransferNumber?: string;
    MaxRetryText?: LocaleModel[] = [];
    UnexpectedErrorText?: LocaleModel[] = [];
    LinkedIncidentsLookbackThreshold?: number  |null;
    RtwText1?: LocaleModel[] = [];
    RtwText2?: LocaleModel[] = [];

    InvalidEntryText?: LocaleModel[] = [];
    IsRtwShiftTimeEnabled?: boolean;
    IsEnterEmployeePhoneNumberRequired?: boolean;
    ApiDownText?: LocaleModel[] = [];
    InvalidAbsenceDateText?: LocaleModel[] = [];
    TransferToDisconnectText?: LocaleModel[] = [];
    EnterEmployeeIdText?: LocaleModel[] = [];
    EnterDobText?: LocaleModel[] = [];
    InvalidAuthenticationText?: LocaleModel[] = [];

    PrimaryReasonIntroText?: LocaleModel[] = [];
    PrimaryReasonSpecialText?: LocaleModel[] = [];
    PrimaryReasonMenuOptionText?: LocaleModel[] = [];
    PrOptionRepeatText?: LocaleModel[] = [];
    PRRepeatDigit?: string;
    SecondaryReasonIntroText?: LocaleModel[] = [];
    SrOptionRepeatText?: LocaleModel[] = [];
    SecondaryReasonMenuOptionText?: LocaleModel[] = [];
    SecondaryReasonSpecialText?: LocaleModel[] = [];
    SRRepeatDigit?: string;
    EnterAbsenceDateText?: LocaleModel[] = [];
    ConfirmAbseneDate1Text?: LocaleModel[] = [];
    ConfirmAbseneDate2Text?: LocaleModel[] = [];
    EnterShiftStartText?: LocaleModel[] = [];
    EnterShiftEndText?: LocaleModel[] = [];
    EnterRtwDateText?: LocaleModel[] = [];
    InvalidRtwDateText?: LocaleModel[] = [];
    ConfirmRtwDateText?: LocaleModel[] = [];
    IsRtwKnownText?: LocaleModel[] = [];
    CarrierTransferText?: LocaleModel[] = [];
    IntermittentContinuousText?: LocaleModel[] = [];
    //InvalidOutOfRangeAbsenceDateText?: LocaleModel[] = [];
    FdaText?: LocaleModel[] = [];
    LdaText?: LocaleModel[] = [];
    IsAMorPMText?: LocaleModel[] = [];
    InvalidEntryAndRetryText?: LocaleModel[] = [];

    InvalidEmployeeIdText1?: LocaleModel[] = [];
    InvalidEmployeeIdText2?: LocaleModel[] = [];
    AuthEmployeeFailText1?: LocaleModel[] = [];
    AuthEmployeeFailText2?: LocaleModel[] = [];
    EnterTotalAbsenceDaysText?: LocaleModel[] = [];
    


    ClientDateFormat?: string;
    TimeFormatMask?: string;
    ConflictAbsenceDateText?: LocaleModel[] = [];
    TransferToCarrierText?: LocaleModel[] = [];
    FailTransferToCarrierText?: LocaleModel[] = [];
    TransferToAgentText?: LocaleModel[] = [];
    ConfirmationNumber1Text?: LocaleModel[] = [];
    ConfirmationNumber2Text?: LocaleModel[] = [];
    EmployeePhoneNumberText?: LocaleModel[] = [];
    DefaultWorkShift?: WorkShift[] = [];
    FixedQuestions?: FixedQuestion[] = [];
   SpecialMessageEnabled?: boolean | null;
   WarningMsgOptionSelectionText?: LocaleModel[] =[];
   SpecialMessageAlertEnabled?: boolean | null;

    EnableFmlaBalance?: boolean | null;
    FmlaBalanceText1?: LocaleModel[] = [];
    FmlaBalanceText2?: LocaleModel[] = [];



   EnableLeaveCreation?: boolean;
   LeaveCaseAssociationText?: LocaleModel[] = [];   
   LeaveOptionText?: LocaleModel[] = [];
   NewLeaveOptionText?: LocaleModel[] = [];
   NonAssociationDigit?: string;
   MaxShiftLengthThresholdInMin?: number |null;   

   EnableStdCreation?: boolean;
   StdOptionText?: LocaleModel[] = [];   
   AbsenceDurationMinutes?: number | null;

    EnableMultipleShift?: boolean;
    ConflictingAbsenceShiftText?: LocaleModel[] = [];
    AbsenceClassificationOptText?: LocaleModel[] = [];

    EnableVoicemail?: boolean;
    VoiceMailOptionText?: LocaleModel[] = []; ///PromptLeaveVoicemailSSML
    VoiceMailRecordingText?: LocaleModel[] = [];///PromptRecordMessageSSML
    ////PromptBeep
}

export interface WorkShift {
    ShiftDescription: LocaleModel[];
    ShiftHours?: number;
    Order: number;
    StartTimeHours: number;
    StartTimeMinutes: number;
    EndTimeHours: number;
    EndTimeMinutes: number;
    IsDefault: boolean;
}

export interface FixedQuestion {
    QuestionName: string;
    QuestionDescription?: LocaleModel[];
}

