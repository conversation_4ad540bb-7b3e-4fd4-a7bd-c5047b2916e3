import {CallLog} from '@/services'
import {LogService} from '@/util'
import { ConnectContactFlowEvent } from 'aws-lambda';

const log = new LogService({ logToConsole: process.env.Debug });
const callService = new CallLog();

export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'setReturnToWork';
   
    const contactId = event.Details.ContactData.ContactId;
    const callData = await callService.getCallDataAsync(contactId);
    const selectedAnswer: number = parseInt(event.Details.Parameters.IsReturnToWork,);
    callService.Logger.log(`Selected STD response - ${selectedAnswer}`);
    let currentAbsence = (callData.Absences)?.peekLast();
    if(currentAbsence.AbsenceIncident){            
        currentAbsence.AbsenceIncident.LinkedIncident = selectedAnswer === 1? false: true;
        callData.Absences.pop();
        callData.Absences.push(currentAbsence);            
        await callService.setCallDataAsync(contactId, {
            IsSaved: "true",
            Absences: callData.Absences
            }); 
    }     
    return { IsSaveSuccess: true };
        
}
              
   
