import { flowEvent } from "../mocks/mock-all";
import { validateDob<PERSON>and<PERSON> } from '@/functions'
import { startDb, stopDb, createTables, deleteTables } from "jest-dynalite";
import {DynamoDBClient, ListTablesCommand } from '@aws-sdk/client-dynamodb'
import {MockAll, TearDown} from '../mocks/mock-all'
import { AuthResponse } from "@/models";
import Sinon from "sinon";
jest.setTimeout(60000)

describe('Unit test for app handler',  () => {

    beforeAll(async () => {});
    beforeEach( async() => { 
        MockAll();  
        ////&console.log("In create");
    })

    afterEach ( async () => {
        ////&console.log("In delete");
        TearDown();
        await deleteTables();
        await createTables();
    })
    afterAll(async () => {
        ////&console.log("In stopdb");
        await stopDb();
    })
    it('verifies successful response', async () => {
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'e6gkw3e3-a66b-4362-af33-65c752a6321',
              },
              Parameters : {
                DoBFormatType: 'MMDDYYYY',
                DateOfBirth: '02122023'
              }
          }
            };
        //flowObject = {...flowObject, ...flowEvent}
        const result = await validateDobHandler(flowObject)
        console.log(result);
        expect(result).toBeDefined();
        
        expect((result).IsDoBValid).toBe(true);
    });

    it('check for failing result', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'e6gkw3e3-a66b-4362-af33-65c752a6321',
              },
              Parameters : {
                DoBFormatType: 'MMDDYYYY',
                DateOfBirth: '20122023'
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await validateDobHandler(flowObject)

        expect(result).toBeDefined();      
        expect((result).IsDoBValid).toBe(false);
       
    });
})
