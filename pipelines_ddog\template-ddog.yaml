AWSTemplateFormatVersion : '2010-09-09'
Transform: 
  - AWS::Serverless-2016-10-31 
  # - Name: DatadogServerless
  #   Parameters:
  #     stackName: !Ref DdStackName
  #     nodeLayerVersion: "91"
  #     extensionLayerVersion: 43
  #     site: !Ref DdSite
  #     apiKeySecretArn: !Ref DdApiKey
        
Description: DEV Abiliti IVR Lambda Stack.
Parameters:
  # DdApiKey: 
  #   Type: String
    
  # DdSite: 
  #   Type: String    
  # DdStackName: 
  #   Type: String    
  AbsenceApiEndpoint:
    Type: String   
  EeApiEndpoint:
    Type: String   
  ConfigApiEndpoint:
    Type: String   
  AuthApiEndpoint:
    Type: String   
  ScopeConfig:
    Type: String
  GrantType:
    Type: String
  Scope:
    Type: String
  ClientId:
    Type: String
  ClientSecret:
    Type: String   
  AwsDynamodbEndpoint:
    Type: String
  IvrCallsTableName:
    Type: String
  CacheTableName:
    Type: String
  ClientConfigTableName:
    Type: String
  ReasonMenuTableName:
    Type: String
  DefaultLanguage:
    Type: String
   

Globals:
  Function:
    VpcConfig:
      SecurityGroupIds:
        -  sg-066853a8514fbe24e
      SubnetIds:
         -  subnet-01d38b015e855905e
    Layers:
      - !Ref RuntimeDependenciesLayer
    Environment:
      Variables:
          absenceApiEndpoint: !Ref 'AbsenceApiEndpoint' 
          eeApiEndpoint: !Ref 'EeApiEndpoint' 
          configApiEndpoint: !Ref 'ConfigApiEndpoint' 
          authApiUrl: !Ref 'AuthApiEndpoint' 
          grantType: !Ref 'GrantType' 
          scopeConfig: !Ref 'ScopeConfig' 
          scope: !Ref 'Scope' 
          clientid: !Ref 'ClientId' 
          clientsecret: !Ref 'ClientSecret' 
          AWSDYNAMODBENDPOINT: !Ref 'AwsDynamodbEndpoint' 
          defaultLanguage: !Ref 'DefaultLanguage' 
          ivrCallsTableName: !Ref 'IvrCallsTableName' 
          cacheTableName: !Ref 'CacheTableName' 
          clientConfigTableName: !Ref 'ClientConfigTableName' 
          reasonMenuTableName: !Ref 'ReasonMenuTableName' 
          environment: "qa"
          # DD_API_KEY: !Ref 'DdApiKey'
          # DD_SITE: !Ref 'DdSite'
    Runtime: nodejs18.x
    MemorySize: 128
    Timeout: 100

Resources:
  RuntimeDependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Metadata:
      BuildMethod: makefile
    Properties:
      Description: Runtime dependencies for Lambdas
      ContentUri: ./
      CompatibleRuntimes:
        - nodejs18.x
      RetentionPolicy: Retain

  PermissionForEventsToInvokeauthenticateEmployee:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "authenticateEmployee"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"
      
  authenticateEmployee:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-AuthenticateEmployee
      Handler: src/functions/authenticateEmployee.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokecheckAllAbsenceReported:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "checkAllAbsenceReported"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  checkAllAbsenceReported:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-CheckAllAbsReported
      Handler: src/functions/checkAllAbsenceReported.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  #checkUSHoliday:
  #  Type: AWS::Serverless::Function
  #  Metadata:
  #    BuildMethod: makefile
  #  Properties:
  #    FunctionName: 
  #    Handler: src/functions/checkUSHoliday.handler
  #    Runtime: nodejs18.x
  #    Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokeresolvegetOpeningMsgV1:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "getOpeningMsgV1"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  getOpeningMsgV1:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-GetOpeningMsgV1
      Handler: src/functions/getOpeningMsgV1.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokeresolveConflictAbsence:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "resolveConflictAbsence"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  resolveConflictAbsence:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ResolveConflictAbsence
      Handler: src/functions/resolveConflictAbsence.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokesaveAbsenceDate:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "saveAbsenceDate"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  saveAbsenceDate:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SaveAbsenceDate
      Handler: src/functions/saveAbsenceDate.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokesaveTotalAbsenceDays:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "saveTotalAbsenceDays"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  saveTotalAbsenceDays:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SaveTotalAbsenceDays
      Handler: src/functions/saveTotalAbsenceDays.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokesetReturnToWork:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "setReturnToWork"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  setReturnToWork:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SetReturnToWork
      Handler: src/functions/setReturnToWork.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role
  
  
  # submitAbsence:
  #   Type: AWS::Serverless::Function
  #   Metadata:
  #     BuildMethod: makefile
  #   Properties:
  #     Handler: src/functions/submitAbsence.handler
  #     Runtime: nodejs18.x
  #     Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role


  PermissionForEventsToInvokevalidateAbsenceDate:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateAbsenceDate"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  validateAbsenceDate:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateAbsenceDate
      Handler: src/functions/validateAbsenceDate.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokevalidatevalidateDOB:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateDOB"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  validateDOB:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateDoB
      Handler: src/functions/validateDOB.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokevalidateEmployeeId:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateEmployeeId"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  validateEmployeeId:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateEmployeeId
      Handler: src/functions/validateEmployeeId.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  # validateRTWShiftTime:
  #   Type: AWS::Serverless::Function
  #   Metadata:
  #     BuildMethod: makefile
  #   Properties:
  #     FunctionName:
  #     Handler: src/functions/validateRTWShiftTime.handler
  #     Runtime: nodejs18.x
  #     Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-rol
  
  #validateReturnToWorkDate:
  #  Type: AWS::Serverless::Function
  #  Metadata:
  #    BuildMethod: makefile
  #  Properties:
  #    FunctionName:
  #    Handler: src/functions/validateReturnToWorkDate.handler
  #    Runtime: nodejs18.x
  #    Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-rol

  PermissionForEventsToInvokevalidatevalidateShiftEndTime:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateShiftEndTime"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  validateShiftEndTime:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateShiftEndTime
      Handler: src/functions/validateShiftEndTime.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokevalidatevalidateShiftStartTime:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateShiftStartTime"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  validateShiftStartTime:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateShiftStartTime
      Handler: src/functions/validateShiftStartTime.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokevalidategetPrimaryReasonMenu:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "getPrimaryReasonMenu"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  getPrimaryReasonMenu:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-GetPrimaryReasonMenu
      Handler: src/functions/getPrimaryReasonMenu.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokevalidategetSecondaryReasonMenu:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "getSecondaryReasonMenu"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  getSecondaryReasonMenu:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-GetSecondaryReasonMenu
      Handler: src/functions/getSecondaryReasonMenu.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokevalidatesaveSecondaryReason:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "saveSecondaryReason"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  saveSecondaryReason:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SaveSecondaryReason
      Handler: src/functions/saveSecondaryReason.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokevalidategetClientConfig:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "getClientConfig"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  getClientConfig:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-GetClientConfig
      Handler: src/functions/getClientConfig.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokevalidateReturnToWorkDate:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateReturnToWorkDate"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  validateReturnToWorkDate:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateReturnToWorkDate
      Handler: src/functions/validateReturnToWorkDate.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role
  PermissionForEventsToInvokesaveReturnToWorkDate:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "saveReturnToWorkDate"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  saveReturnToWorkDate:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SaveReturnToWorkDate
      Handler: src/functions/saveReturnToWorkDate.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokevalidateEmployeePhoneNumber:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateEmployeePhoneNumber"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  validateEmployeePhoneNumber:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateEmployeePhoneNumber
      Handler: src/functions/validateEmployeePhoneNumber.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role

  PermissionForEventsToInvokesubmitAbsence:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "submitAbsence"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  submitAbsence:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SubmitAbsence
      Handler: src/functions/submitAbsence.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role
  PermissionForEventsToInvokegetCarrierTransferNumber:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "getCarrierTransferNumber"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  getCarrierTransferNumber:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-GetCarrierTransferNumber
      Handler: src/functions/getCarrierTransferNumber.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role
  PermissionForEventsToInvokedroppedSessionMonitor:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "droppedSessionMonitor"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"
  clearLastAddedAbsenceDate:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ClearLastAddedAbsenceDate
      Handler: src/functions/clearLastAddedAbsenceDate.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role
  PermissionForEventsToInvokeclearLastAddedAbsenceDate:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "clearLastAddedAbsenceDate"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"
  droppedSessionMonitor:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-DroppedSessionMonitor
      Handler: src/functions/droppedSessionMonitor.handler
      Runtime: nodejs18.x
      Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-role