{"compilerOptions": {"resolveJsonModule": true, "experimentalDecorators": true, "target": "esnext", "module": "commonjs", "lib": ["es2022"], "allowJs": true, "outDir": "build", "baseUrl": ".", "paths": {"@/*": ["src/*"], "Mixins": ["src/services/mixins"]}, "strict": true, "esModuleInterop": true, "types": ["node", "jest"], "skipLibCheck": true}, "include": ["src/**/*", "config/**/*"], "exclude": ["src/**/*.spec.ts", "tests/**/*"]}