AWSTemplateFormatVersion : '2010-09-09'
Description: DEV Abiliti IVR dynamoDB.

Resources:  
  ablarivrcallsession:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: ablarivr-call-session
      AttributeDefinitions:
        - AttributeName: ContactId
          AttributeType: S
        - AttributeName: IsSubmitted
          AttributeType: S
      KeySchema:
        - AttributeName: ContactId
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: true
      GlobalSecondaryIndexes:
        - IndexName: IsSubmittedIdx
          KeySchema:
            - AttributeName: IsSubmitted
              KeyType: HASH
          Projection:
              ProjectionType: ALL
      Tags:
        - Key: "LOB"
          Value: "adm"
        - Key: "Project"
          Value: "ablarivr"

  ablarivrDataCache:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: ablarivr-data-cache
      AttributeDefinitions:
        - AttributeName: CacheKey
          AttributeType: S
      KeySchema:
        - AttributeName: CacheKey
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: true
      TimeToLiveSpecification:
          AttributeName: TimeToLive
          Enabled: TRUE
      Tags:
        - Key: "LOB"
          Value: "adm"
        - Key: "Project"
          Value: "ablarivr"
  ablarivrReasonMenu:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: ablarivr-reasonmenu
      AttributeDefinitions:
        - AttributeName: ClientName
          AttributeType: S
      KeySchema:
        - AttributeName: ClientName
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: true
      TimeToLiveSpecification:
          AttributeName: TimeToLive
          Enabled: TRUE
      Tags:
        - Key: "LOB"
          Value: "adm"
        - Key: "Project"
          Value: "ablarivr"
  ablarivrCallFlowConfig:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: ablarivr-callflow-config
      AttributeDefinitions:
        - AttributeName: Instance
          AttributeType: S
      KeySchema:
        - AttributeName: Instance
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: true
      Tags:
        - Key: "LOB"
          Value: "adm"
        - Key: "Project"
          Value: "ablarivr"