//import { setup } from "jest-dynalite";


let aws = require('@aws-sdk/client-dynamodb')
//&console.log("SETTING UP DYNAMODB")

require('jest-dynalite').setup(__dirname)
//sleep(5000);
////&console.log(`From DynamoDb: ${out}`);

//const _ = async () => await require('jest-dynalite').createTables();
//&console.log("FINISHED SETTING UP DYNAMODB")
// You must give it a config directory
//setup(__dirname);
function sleep(miliseconds) {
    var currentTime = new Date().getTime();
 
    while (currentTime + miliseconds >= new Date().getTime()) {
    }
 }
