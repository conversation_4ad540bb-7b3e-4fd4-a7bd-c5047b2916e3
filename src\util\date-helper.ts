import moment, { Moment } from 'moment-timezone'

export const DateFormats = (opt: any) => {
    switch(opt) {
        case "1":
            return "MMDDYYYY";
        case "2":
            return "YYYYMMDD";
        default:
            return "MMDDYYYY"
    }
}

export class DateHelper {
  // FUTURE: Should come from config
  private defaultTimeZone: string = 'America/Toronto';

  get DefaultTimeZone(): string {
    return this.defaultTimeZone;
  }

  public getDateFromMMDDFormat(
    inputDate: string,
    inDecemberMoveToNextYear = true,
  ): Moment | null {
    const janIndex = 0;
    const decIndex = 11;
    const today = moment(new Date()).startOf('day');
    const inputDateInCurrentYear = `${today.year()}${inputDate}`;

    if (!moment(inputDateInCurrentYear, 'YYYYMMDD', true).isValid()) {
      return null;
    }

    let absenceDateInCurrentYear = moment(inputDateInCurrentYear, 'YYYYMMDD');

    if (
      inDecemberMoveToNextYear &&
      absenceDateInCurrentYear.month() === janIndex &&
      today.month() === decIndex
    ) {
      absenceDateInCurrentYear = absenceDateInCurrentYear.add(1, 'years');
    }

    return absenceDateInCurrentYear;
  }

  is12HClockCorrect(h: number, m: number, isAM: any) {
    const is12h = h > 0 && h <= 12 && (isAM ? h <= 12 : h > 0); //  12:00 => PM
    const dayPart = isAM ? 'AM' : 'PM';
    return is12h && moment(`${h}:${m} ${dayPart}`, ['h:mm A']).isValid();
  }

  // None of the following are currently being used
  getDateFromYYYYMMDDFormat(inputDate: string) {
    return moment(inputDate, 'YYYYMMDD', true).isValid()
      ? moment(inputDate, 'YYYYMMDD')
      : moment(inputDate, 'YYYY-MM-DD', true).isValid()
      ? moment(inputDate, 'YYYY-MM-DD')
      : null;
  }

  convertTo24HClock(timeIn12HClock: string) {
    return moment(timeIn12HClock, ['h:mm A']).format('HH:mm');
  }

  getDuration(startTimeStamp: string, endTimeStamp: string) {
    if (endTimeStamp === null) endTimeStamp = startTimeStamp; //make call duretion to zero if nothing is specified
    const startTime = moment(startTimeStamp);
    const ms = moment(endTimeStamp).diff(moment(startTime));
    const duration = moment.duration(ms);
    const formattedDuration =
      Math.floor(duration.asHours()) + moment.utc(ms).format(':mm:ss');
    return formattedDuration;
  }

  static getReportedShiftTime(
    absenceDate?: string | moment.Moment,
    shiftTime?: string
  ): string {

    
    const fmtAbsenceDate = moment(absenceDate);
    
    if (shiftTime) {
    const shiftTimeParsed = moment(shiftTime, 'HH:mm');
    
    fmtAbsenceDate.set({
      hour: shiftTimeParsed.get('hour'),
      minute: shiftTimeParsed.get('minute'),
      second: shiftTimeParsed.get('second'),
    });
    }
    
    return fmtAbsenceDate.format('YYYY-MM-DD HH:mm:ss');
  }
}   
