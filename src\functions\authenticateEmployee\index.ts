import {AbsenceReportingService, CallLog, LocalConfiguration} from '@/services'
import {DateFormats} from '@/util'
import {AuthResponse} from '@/models'
import { ConnectContactFlowEvent } from 'aws-lambda'


const callService = new CallLog();
const reportingService = new AbsenceReportingService();
reportingService.CallLog = callService;
const localConfig = new LocalConfiguration();

export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'authenticateEmployee';
    reportingService.Logger.log('Input data', event);
    let contactId = event.Details.ContactData.ContactId;;
    try {
        
        const clientCode = event.Details.Parameters.ClientCode;
        const clientName = event.Details.Parameters.ClientName;
        const employeeNumber = event.Details.Parameters.EmployeeId;
        const employeeBirthDate = event.Details.Parameters.DateOfBirth;        
        const dtFormat = DateFormats(event.Details.Parameters.DoBFormatType);
        const empData = await reportingService.getEmployeeDataAsync(
            contactId,
            clientCode,
            employeeNumber,
            employeeBirthDate,
            dtFormat
        );
        
        let result : AuthResponse = {
            IsEmployeeValid: empData.IsEmployeeValid,
            IsDoBValid: empData.IsValidDob ,
           
        };
        //&console.log(empData);    
        await callService.logStageAsync(
            contactId,
            lambdaName,
            result.IsEmployeeValid ? null : { Validation: result.ValidationMessage },
            result
        );
        reportingService.Logger.log('Result ', result);
        return  result;
        
    } catch (error: any) {
        reportingService.Logger.error('Error ', error);
        return {
            error:error.message, 
            result: null};
    }
};

