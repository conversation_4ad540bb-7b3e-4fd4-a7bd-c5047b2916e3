import { ConnectContactFlowEvent } from 'aws-lambda'
import { CallLog, LocalConfiguration } from '@/services'
import { DateFormats} from '@/util'
import moment from 'moment'

const callService = new CallLog(); // FUTURE - don't instantiate like this

export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'saveReturnToWorkDate';
    callService.Logger.log('Input data', event);
    
    let contactId;
    let res= {
        IsSaveSuccess: false,
       
    };
    try {
        const enteredDate = event.Details.Parameters.ReturnDayToWork;
        contactId = event.Details.ContactData.ContactId;        
        const dtFormat = DateFormats(event.Details.Parameters?.RTWDateFormatType) || 'MMDDYYYY';                        
        const returnToWorkDate = moment(enteredDate, dtFormat).startOf('day');
        callService.Logger.log(`Entered return to work date formatted: ${returnToWorkDate}`);        
        //Check whether entered date format is correct.
        const isValidDate = moment(enteredDate, dtFormat, true).isValid();
        //Set RTW date in call data if its valid
        if(isValidDate){
            const callData = await callService.getCallDataAsync(contactId);
            
            let currentIncident = (callData.Absences)?.peekLast();
            callService.Logger.log('currentIncident', currentIncident); 
            if(currentIncident.AbsenceIncident)
            {
                currentIncident.AbsenceIncident.ReturnToWorkDate = returnToWorkDate.format('YYYY-MM-DD HH:mm:ss')
                
            callData.Absences.pop();
            callData.Absences.push(currentIncident);  
            callService.Logger.log('call data', callData.Absences ); 
            await callService.setCallDataAsync(contactId, {
                IsSaved: "true",
                Absences: callData.Absences
                });
            res.IsSaveSuccess = true;
            
        }
            
        };
        
        callService.Logger.log('Result', res);

        //Return result
        return res;

    } catch (err: any) {
        callService.Logger.log('Error occured:', err);     
        return res;
    }
};
