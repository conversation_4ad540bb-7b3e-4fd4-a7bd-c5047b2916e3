import { BaseService, IHttpService } from "./service";
import {DynamoClient} from '@/services/mixins'
import { ClientConfigModel, ClientConfigWrapper} from '@/models'
import moment from "moment";
import { Constants } from "@/util";
//FUTRURE: DECIDE IMPLEMENTATION (EXTEND SYNTAX ALLOWS INTRODUCTION OF PROPERTIES, DECORATOR DOES NOT)
/**
 * Services any lambda with 
 * *clientconfig - from AWS Connect dynamoDB
 * 
 */

export class LocalConfiguration extends DynamoClient(BaseService) {
  AuthenticatedHttpClient?: IHttpService; //TODO
  private ConfigEndpoint(clientCode: string): string {
    return (
      `${process.env.configApiEndpoint}AbsenceConfig/${clientCode}`
    );
  }

  get TableName() {
    return process.env.clientConfigTableName || 'ablarivr-client-config';
  }

  public async getClientConfigAsync(clientCode: string , clientName?:string): Promise<ClientConfigWrapper> {
    let params: any = {
      TableName: this.TableName,
      IndexName: `ClientCodeIdx`,
      KeyConditionExpression: 'ClientCode = :clientCode',
      ExpressionAttributeValues: {
        ':clientCode': `${clientCode?.toUpperCase()}`,
      },
    };
    
    if (!clientCode) {
      params = {
        TableName: this.TableName,
        Key: {
          ClientName: clientName,
        },
        KeyConditionExpression: 'ClientName = :clientName',
        ExpressionAttributeValues: {
          ':clientName': `${clientName?.toUpperCase()}`,
        },
      };
    }
    let configModel : any = {};
    try {
      this.Logger.log('PERFORMANCE: in getClientConfigAsync GET from DDB')
      configModel =  await this.query(params);
      this.Logger.log('PERFORMANCE: in getClientConfigAsync RETURNED from DDB')
      try{
          if (configModel && Array.isArray(configModel?.Items) && configModel?.Items[0]) {
              if (process.env.refreshLocalDBInMinutes !== undefined) {
                  this.log.log(`the refresh time is ${parseInt(process.env.refreshLocalDBInMinutes)}`);
               }                                          
                           
               if (moment(Date.now()).diff(moment(configModel.Items[0].UpdateTimeStamp), 'minutes') > +(process.env.refreshLocalDBInMinutes??10))
               {
                let config = await this.fetchConfig(clientCode);
                await this.updateClientConfigAsync( config, clientCode || clientName);
                return new ClientConfigWrapper(config);
               }   
              
            return  new ClientConfigWrapper(<ClientConfigModel>configModel?.Items[0]); //TODO
        
      }
      }catch(error: any){
        this.log.error(JSON.stringify(error)); //TODO
      }      
      let config = await this.fetchConfig(clientCode);
      var result = await this.put({
        TableName: this.TableName,
        Item: { ClientName: `${clientCode}`, ...config },
      });
      //this.Logger.log('Loaded configuration data', config);
      return new ClientConfigWrapper(config);
    } catch (err: any) {
      this.log.error(JSON.stringify(err)); //TODO
      throw err; //TODO
    }
    
  
}

  public async updateClientConfigAsync(model: ClientConfigModel, clientCode?: string) {    
    try {      
      var result = await this.delete({
        TableName: this.TableName,
        Key: { ClientName: `${clientCode}`},
      });
      await this.put({
        TableName: this.TableName,
        Item: { ClientName: `${clientCode}`, ...model },
      });      
    } catch (err: any) {
      this.log.error(JSON.stringify(err));
    }
  } 

  public async fetchConfig(clientCode: string): Promise<ClientConfigModel> {
    let toReturn :ClientConfigModel;
    //if(clientCode){
    this.Logger.log("PERFORMANCE: getconfig BEFORE GET", clientCode);
    let res = await this.AuthenticatedHttpClient?.getAsync(`${this.ConfigEndpoint(clientCode)}`,); 
    this.Logger.log("PERFORMANCE: getconfig AFTER GET", clientCode);
    toReturn = this.MapConfigResponse(res.data);
   // }
    return toReturn;
  }
  
  public MapConfigResponse(data: any): ClientConfigModel {
    let ivrResponse : any = {};
    let toReturn: ClientConfigModel = new ClientConfigModel();
   
    if( ivrResponse = data.aR3IvrConfiguration)
    {
      
      toReturn.UpdateTimeStamp = moment(Date.now()).toString();
      toReturn.ClientCode = data.clientCode;      
      toReturn.MaxTotalMissedAbsenceDays = ivrResponse.maxReportableDays;
      toReturn.MaxAllowAbsenceDaysFuture = ivrResponse.maxReportableDaysInFuture;
      toReturn.MaxAllowAbsenceDaysPast = ivrResponse.maxReportableDaysInPast;
      toReturn.CarrierTransferNumber = ivrResponse.carrierTransferNumber;
      toReturn.NonAssociationDigit = ivrResponse.nonAssociationDigit;
      toReturn.MaxRetryValue = ivrResponse.maxRetry1Value != null && ivrResponse.maxRetry1Value > 0  ? ivrResponse.maxRetry1Value - 1 : 0;
      toReturn.ClientDateFormat = ivrResponse.dateFormatMask;
      toReturn.MaxRetryMenu1 = ivrResponse.maxRetry1Value != null && ivrResponse.maxRetry1Value > 0 ? ivrResponse.maxRetry1Value - 1 : 0;
      toReturn.MaxRetryMenu2 = ivrResponse.maxRetry1Value != null && ivrResponse.maxRetry1Value > 0 ? ivrResponse.maxRetry1Value - 1 : 0;
      toReturn.TimeFormatMask = ivrResponse.timeFormatMask;
      toReturn.PRRepeatDigit = ivrResponse.prRepeatDigit;
      toReturn.SRRepeatDigit = ivrResponse.srRepeatDigit;
      toReturn.SpecialMessageEnabled = ivrResponse.specialMessageEnabled;
      toReturn.SpecialMessageAlertEnabled = ivrResponse.specialMessageAlertEnabled;
      toReturn.EnableStdCreation = ivrResponse.enableStdCreation;
      toReturn.EnableLeaveCreation = ivrResponse.enableLeaveCreation;
      toReturn.IsRtwShiftTimeEnabled = ivrResponse.isRtwShiftTimeEnabled;
      toReturn.IsEnterEmployeePhoneNumberRequired = ivrResponse.isEnterEmployeePhoneNumberRequired;
      if (Array.isArray(data.enabledLocales) && data.enabledLocales.length > 1) {
        switch(data.enabledLocales[1])
        {
            case Constants.LocaleString_Spanish.toUpperCase():
              toReturn.MultiLanguageId =  Constants.MultiLanguage_EN_ES;
              break;
            case Constants.LocaleString_French.toUpperCase():
              toReturn.MultiLanguageId = Constants.MultiLanguage_EN_FR;
              break;
            default:
              toReturn.MultiLanguageId = Constants.MultiLanguage_EN;
              break;
        }
      } else {
        toReturn.MultiLanguageId = Constants.MultiLanguage_EN; //PLACE HOLDER VALUES
      }
      toReturn.EnableVoicemail = data.isAr3VMEnabled;
      toReturn.MaxShiftLengthThresholdInMin = ivrResponse.maxShiftLengthThresholdInMinutes;
      toReturn.AbsenceDurationMinutes = data.absenceDurationMinutes;
      toReturn.LinkedIncidentsLookbackThreshold = data.linkedIncidentsLookbackThreshold;
      toReturn.EnableMultipleShift = ivrResponse.multipleShiftEnabled;
      toReturn.WelcomeMessage = ivrResponse.welcomeMessage;
      this.MapLocaleText(ivrResponse.openingStatement, toReturn.OpeningStatement ?? []);
      this.MapLocaleText(ivrResponse.specialOpeningStatement, toReturn.SpecialOpeningStatement??[]);
      this.MapLocaleText(ivrResponse.subOpeningStatement, toReturn.SubOpeningStatement ?? []);
      this.MapLocaleText(ivrResponse.subOpeningSpecialStatement, toReturn.SubOpeningSpecialStatement??[]);
      this.MapLocaleText(ivrResponse.maxRetryText, toReturn.MaxRetryText ?? []);
      this.MapLocaleText(ivrResponse.rtwText1, toReturn.RtwText1 ?? []);
      this.MapLocaleText(ivrResponse.unexpectedErrorText, toReturn.UnexpectedErrorText??[]);
      this.MapLocaleText(ivrResponse.invalidEntryAndRetryText, toReturn.InvalidEntryAndRetryText??[]);
      this.MapLocaleText(ivrResponse.isAMorPMText, toReturn.IsAMorPMText??[]);
      this.MapLocaleText(ivrResponse.invalidAbsenceDateText, toReturn.InvalidAbsenceDateText??[]);
      this.MapLocaleText(ivrResponse.transferToDisconnectText, toReturn.TransferToDisconnectText??[]);
      
      this.MapLocaleText(ivrResponse.enterEmployeeIdText, toReturn.EnterEmployeeIdText??[]);
      this.MapLocaleText(ivrResponse.enterDobText, toReturn.EnterDobText??[]);
      this.MapLocaleText(ivrResponse.invalidAuthenticationText, toReturn.InvalidAuthenticationText??[]);
      
      this.MapLocaleText(ivrResponse.enterTotalAbsenceDaysText, toReturn.EnterTotalAbsenceDaysText ?? []);
      this.MapLocaleText(ivrResponse.enterAbsenceDateText, toReturn.EnterAbsenceDateText ??[]);
      this.MapLocaleText(ivrResponse.confirmAbseneDate1Text, toReturn.ConfirmAbseneDate1Text ?? []);
      this.MapLocaleText(ivrResponse.confirmAbseneDate2Text, toReturn.ConfirmAbseneDate2Text ?? []);
      this.MapLocaleText(ivrResponse.intermittentContinuousText, toReturn.IntermittentContinuousText ?? []);
      this.MapLocaleText(ivrResponse.enterShiftStartText, toReturn.EnterShiftStartText ??[]);
      this.MapLocaleText(ivrResponse.enterShiftEndText, toReturn.EnterShiftEndText ?? []);
      this.MapLocaleText(ivrResponse.conflictAbsenceDateText, toReturn.ConflictAbsenceDateText ??[]);
      this.MapLocaleText(ivrResponse.enterRtwDateText, toReturn.EnterRtwDateText ?? []);
      this.MapLocaleText(ivrResponse.confirmRtwDateText, toReturn.ConfirmRtwDateText ?? []);
      this.MapLocaleText(ivrResponse.invalidRtwDateText, toReturn.InvalidRtwDateText ?? []);
      this.MapLocaleText(ivrResponse.isRtwKnownText, toReturn.IsRtwKnownText ?? []);
      this.MapLocaleText(ivrResponse.secondaryReasonSpecialText, toReturn.SecondaryReasonSpecialText ?? []);
      this.MapLocaleText(ivrResponse.primaryReasonIntroText, toReturn.PrimaryReasonIntroText ?? []);
      this.MapLocaleText(ivrResponse.secondaryReasonIntroText, toReturn.SecondaryReasonIntroText ?? []);
      this.MapLocaleText(ivrResponse.primaryReasonMenuOptionText, toReturn.PrimaryReasonMenuOptionText ?? []);
      this.MapLocaleText(ivrResponse.primaryReasonSpecialText, toReturn.PrimaryReasonSpecialText ?? []);
      this.MapLocaleText(ivrResponse.prOptionRepeatText, toReturn.PrOptionRepeatText ?? []);
      this.MapLocaleText(ivrResponse.secondaryReasonMenuOptionText, toReturn.SecondaryReasonMenuOptionText ?? []);
      this.MapLocaleText(ivrResponse.srOptionRepeatText, toReturn.SrOptionRepeatText ?? []);
      this.MapLocaleText(ivrResponse.transferToCarrierText, toReturn.TransferToCarrierText ?? []);
      this.MapLocaleText(ivrResponse.failTransferToCarrierText, toReturn.FailTransferToCarrierText ?? []);
      this.MapLocaleText(ivrResponse.transferToAgentText, toReturn.TransferToAgentText ?? []);
      this.MapLocaleText(ivrResponse.confirmationNumber1Text, toReturn.ConfirmationNumber1Text ?? []);
      this.MapLocaleText(ivrResponse.confirmationNumber2Text, toReturn.ConfirmationNumber2Text ?? []);
      this.MapLocaleText(ivrResponse.employeePhoneNumberText, toReturn.EmployeePhoneNumberText ?? []);
      this.MapLocaleText(ivrResponse.specialMessageOption, toReturn.WarningMsgOptionSelectionText ?? []);
      this.MapLocaleText(ivrResponse.leaveCaseAssociationText, toReturn.LeaveCaseAssociationText ?? []);
      this.MapLocaleText(ivrResponse.leaveOptionText, toReturn.LeaveOptionText ?? []);
      this.MapLocaleText(ivrResponse.newLeaveOptionText, toReturn.NewLeaveOptionText ?? []);
      this.MapLocaleText(ivrResponse.stdOptionText, toReturn.StdOptionText ?? []);
      this.MapLocaleText(ivrResponse.conflictingAbsenceShiftText, toReturn.ConflictingAbsenceShiftText ?? []);
      this.MapLocaleText(ivrResponse.absenceClassificationOptText, toReturn.AbsenceClassificationOptText ?? []);
      this.MapLocaleText(ivrResponse.fdaText, toReturn.FdaText ?? []);
      this.MapLocaleText(ivrResponse.ldaText, toReturn.LdaText ?? []);
      this.MapLocaleText(ivrResponse.voiceMailOptionText, toReturn.VoiceMailOptionText ?? []);
      this.MapLocaleText(ivrResponse.voiceMailRecordingText, toReturn.VoiceMailRecordingText ?? []);
    }
    let elCheck: any = {};
    
    let wsResponse: any = {} ;
    if(wsResponse = data.workShifts)
    {
      let ws: any[] = data.workShifts as Array<any>;
      toReturn.DefaultWorkShift = [];
      ws.forEach((item:any) =>{
        toReturn.DefaultWorkShift?.push({
          ShiftDescription: item.shiftDescription.map((x: any) => ({
            Locale: x?.locale,
            Description: x?.description
          })),
          IsDefault: item.isDefault,
          ShiftHours: item.shiftHours,
          Order: item.order,
          StartTimeHours: item.startTimeHours,
          StartTimeMinutes: item.startTimeMinutes,
          EndTimeHours: item.endTimeHours,
          EndTimeMinutes: item.endTimeMinutes
        })
      })


   
    }
    return toReturn;
  }
  
  // private MapLocaleText(from: any[], to: LocaleModel[]) {  
  //   if(Array.isArray(from) && from.length>0)  {
  //   from.forEach ((x: any) => {
  //     console.log(JSON.stringify(x));
  //       to.push({
  //           Locale: x.locale || x.Locale,
  //           Description :x.description || x.Description
  //       })
  //      })
  //     }
  //   }
  //TODO
  // public async getConfigurationAsync(
  //   clientCode: string | null,
  //   clientName?: string,
  //   langSelected?: string
  // ): Promise<AwsClientConfig> {
  //   let language: string = 'EN';
    
  //   try{
  //      let config: AwsClientConfig = {
  //       ClientCode : ""
  //      };//await this.getClientConfigAsync(clientCode, clientName);
  //     // if(langSelected!= undefined)     
  //     //   language = langSelected;            
  //     // toReturn = this.ConfigMapper(config, language);
  //     // this.Logger.log('Loaded configuration data', config);
  //     // return toReturn;
  //     return config
      
  //   } catch (err: any) {
  //     this.Logger.error(err);
  //     throw new Error(err);
  //   }
  // }


  public async saveConfigurationAsync(clientCode: string, configuration: any) {
    const params = {
      TableName: this.TableName,
      Item: { ClientCode: clientCode, ...configuration },
    };
    try {
      const result = await this.put(params);
      this.log.log('Saved configuration data', params);
      return result;
    } catch (err: any) {
      this.Logger.error(err);
      throw new Error(err);
    }
  }
}