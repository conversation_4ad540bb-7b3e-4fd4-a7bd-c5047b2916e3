'use strict';


import moment, {Moment} from 'moment';
import {ConnectContactFlowEvent} from 'aws-lambda';
import {CallLog, AbsenceReportingService, LocalConfiguration, ScopedHttpService} from '@/services';
import {LogService, DateFormats, ValidationErrors, PromptHelper, RefAbsenceType} from '@/util';

const callService = new CallLog();
const log = new LogService({ logToConsole: process.env.Debug });
const reportingService = new AbsenceReportingService();
reportingService.CallLog = callService;
const localConfig = new LocalConfiguration();

interface IValidResponse {
    IsAbsDateValid: boolean,
    AbsDateNotValidReason: string | null,
    ReportAbsenceDatePlayable: string,
    PromptConfirmAbsenceDateSSML1: string  
}

export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'validateAbsenceDate';
    reportingService.Logger.log('Input data', event);
    let contactId =  event.Details.ContactData.ContactId;
    localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
    try {
         
        let inputDate = event.Details.Parameters.ReportAbsenceDate;
        let isContinuousOrIntermittent = event.Details.Parameters.IsContinuousFullDaySelected;        
        let dateFormat = DateFormats(event.Details.Parameters.AbsenceDateFormatType);
        
        let dateIsValid = moment(inputDate, dateFormat, true).isValid();
        let absenceDate: Moment = moment(inputDate, dateFormat).startOf('day');
        const result: IValidResponse = {
            IsAbsDateValid: false,
            AbsDateNotValidReason: null,
            ReportAbsenceDatePlayable: absenceDate.format('YYYYMMDD'),
            PromptConfirmAbsenceDateSSML1: ''
        }

        if (!dateIsValid) {
            result.AbsDateNotValidReason = ValidationErrors.InvalidDateFormat;
            result.ReportAbsenceDatePlayable = '';
            return await handleValidationErrorAsync(contactId, lambdaName, result);
        }
        
        /*let overlapIncidents = await reportingService.checkOverlappingAbsences(contactId, absenceDate);
        // Check if there are any overlapping Absences, either saved, or in progress
        if (overlapIncidents) {
            let overlapIncidentIds: string[] = overlapIncidents.map( (x) => `${x?.IncidentId}`);
            result.AbsDateNotValidReason = await reportingService.markSessionDuplicateAbsences(contactId, overlapIncidentIds, absenceDate);
        }*/
        reportingService.Logger.log('Reported Absence date ', absenceDate);
        let session = await callService.getCallDataAsync(contactId);
        let config = await localConfig.getClientConfigAsync(session.ClientCode);
        let selectedLang = session.SelectedLang ?? 'en';        
        let currentAbsence = (session.Absences)?.peekLast();
        const localMaxValidAbsenceDays = +(config?.clientConfig?.MaxAllowAbsenceDaysFuture || currentAbsence?.ReasonConfig?.MaxDaysAllowed || 0);
        const localMaxBeforeAbsenceDays = +(config?.clientConfig?.MaxAllowAbsenceDaysPast || currentAbsence?.ReasonConfig?.ReportableDaysInPast || 0);
        const clientDateFormat = DateFormats(config?.clientConfig?.ClientDateFormat ?? dateFormat);
        callService.Logger.log(`MaxValidAbsenceDays: ${localMaxValidAbsenceDays}; MaxBeforeAbsenceDays: ${localMaxBeforeAbsenceDays};`);
        let absenceDateParsed = moment(inputDate, clientDateFormat);
        //check date in valid range
        const isAbsDateWithinValidRange = await reportingService.isDateWithinValidRange(
            localMaxBeforeAbsenceDays,
            localMaxValidAbsenceDays,
            absenceDateParsed
        );

        if (!isAbsDateWithinValidRange) {
            result.AbsDateNotValidReason = ValidationErrors.AbsenceDateNotWithinValidRange;
            result.ReportAbsenceDatePlayable = '';           
            return await handleValidationErrorAsync(contactId, lambdaName, result);
        }
        const maxAbsenceDays = +(config.clientConfig.MaxTotalMissedAbsenceDays || 0);
        
        let absType = isContinuousOrIntermittent == "true"? RefAbsenceType.Continuous : RefAbsenceType.Intermittent;
        
        let ldaValid = false;
        let dateRange: string[] = [];
        //Added to check duplicate for FDA or intermittent absence dates
        dateRange.push(absenceDate.format('YYYY-MM-DD'));
        //if absencetype is already set then do not do following
        if((currentAbsence.AbsenceIncident.AbsenceType === undefined || currentAbsence.AbsenceIncident.AbsenceType === null) && (session.IsDateRange === undefined || session.IsDateRange === null))
        {
            
           currentAbsence.AbsenceIncident.AbsenceType = absType;
           session.Absences.pop();
           session.Absences.push(currentAbsence);

            await callService.setCallDataAsync(contactId, {
                IsSaved: "true",
                IsDateRange: (session.TotalAbsenceDays > 1 && absType == RefAbsenceType.Continuous)? true: false,
                Absences: session.Absences
            });
            
                
        }
        else  if(session.IsDateRange && Array.isArray(currentAbsence.AbsenceIncident.AbsenceDates) && currentAbsence.AbsenceIncident.AbsenceDates.length ==1 )
        {
            //check if lda >fda and add to 
            let fda = currentAbsence.AbsenceIncident.AbsenceDates[0];
            
            if(fda != null || undefined)
            {                
                ldaValid = reportingService.isLdaGreaterThanFda(absenceDate, fda);
                if(!ldaValid)
                {
                    result.AbsDateNotValidReason = ValidationErrors.InvalidDateFdaGreateThanLda;
                    result.ReportAbsenceDatePlayable = '';                    
                    return await handleValidationErrorAsync(contactId, lambdaName, result);
                }

                //Check for duplicate absence with in the range of FDA nd LDA
                //date range overrides line 84 daterange (already added one) if its range FDA and LDA is known.                
                dateRange = await reportingService.GetAbsenceDateRange(moment(fda.StartDate).startOf('day'), absenceDate);
                let isTotalDaysGreaterThanMax = reportingService.isTotalDaysGreaterThanMax(maxAbsenceDays, dateRange.length + 1);
                if (isTotalDaysGreaterThanMax) {
                    result.AbsDateNotValidReason = ValidationErrors.IsTotalDaysGreaterThanMax;
                    result.ReportAbsenceDatePlayable = '';
                    return await handleValidationErrorAsync(contactId, lambdaName, result);
                }               
            }
        }       
        let isDuplicate = ldaValid ? await reportingService.checkDuplicateAbsencesInBulk(contactId, dateRange, isContinuousOrIntermittent) 
                                : await reportingService.checkDuplicateAbsences(contactId, absenceDate, isContinuousOrIntermittent);
        if (isDuplicate) {
            result.AbsDateNotValidReason = ValidationErrors.DuplicateEntry;
            result.ReportAbsenceDatePlayable = '';            
            return await handleValidationErrorAsync(contactId, lambdaName, result);
        }

        result.IsAbsDateValid = result.AbsDateNotValidReason == null;
        result.PromptConfirmAbsenceDateSSML1 = PromptHelper.getMergedPrompt(config.clientConfig.ConfirmAbseneDate1Text??[], selectedLang, '{ReportedAbsenceDateString}', result.ReportAbsenceDatePlayable, "date");
        reportingService.Logger.log('Result', result);
        //await callService.logStageAsync(contactId, lambdaName, null, result);
        return result;
        //return await handleValidationErrorAsync(contactId, lambdaName, result);
    } catch (error: any) {
        reportingService.Logger.error('Error ', error);
        return {
            message: error.message, 
            result: null};
    }
};


async function handleValidationErrorAsync(contactId: string, lambdaName: string, result: any) : Promise<any>{
    await callService.logStageAsync(contactId, lambdaName, { Validation: result.AbsDateNotValidReason }, result);
    return result;
}



