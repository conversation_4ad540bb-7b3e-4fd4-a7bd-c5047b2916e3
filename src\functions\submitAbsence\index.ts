'use strict';

import { ConnectContactFlowEvent } from 'aws-lambda';
import { CallLog, LocalConfiguration, AbsenceApiService, ScopedHttpService } from '@/services';
import { ClaimStatus, PromptHelper } from '@/util';
import { SubmitAbsenceResponse, ClientConfigWrapper } from '@/models';
import he from 'he';

const callService = new CallLog();
const localConfig = new LocalConfiguration();
const absenceApi = new AbsenceApiService();

export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'submitAbsence';
    callService.Logger.log('Input data', event);
    let contactId = event.Details.ContactData.ContactId;
    const confirmAbsence = event.Details.Parameters.ConfirmAbsence;
    let selectedAbsenceTreatmentOption = event.Details.Parameters.SelectedAbsenceTreatmentOption === 'null'? 0: +(event.Details.Parameters.SelectedAbsenceTreatmentOption);
    const callData = await callService.getCallDataAsync(contactId);
    let promptClosingScript = '';
    let currentAbsence = (callData.Absences)?.pop();
    let saved: boolean = false;
    let confirmNum: any = 0;
    localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
    let config: ClientConfigWrapper = await localConfig.getClientConfigAsync(callData.ClientCode);
    let confirmNumberText: string = '';
    let selectedLang = callData.SelectedLang ?? 'en';
    if (currentAbsence.AbsenceIncident && confirmAbsence === 'true') {
        if (currentAbsence.AbsenceIncident && callData.IsSubmitted === ClaimStatus.Submitted) {
            callService.Logger.log(`The absence has already been submitted with Confirnumber ${currentAbsence.AbsenceIncident.AbsenceIncidentId}`);
            saved = true;
            confirmNum = currentAbsence.AbsenceIncident.AbsenceIncidentId;
            promptClosingScript = he.decode(currentAbsence.ClosingScripts?.peekLast()?.peekLast().Text.find((x:any) => x?.Locale.toLowerCase() == selectedLang.toLowerCase())?.Description  ?? '');

            confirmNumberText = PromptHelper.getMergedPrompt(config.clientConfig.ConfirmationNumber1Text ?? [], selectedLang, '{ConfirmationNumber}', `${currentAbsence.AbsenceIncident.AbsenceIncidentId}`, "telephone");
            callService.Logger.log(`Closing script when submitted already: ${PromptHelper.wrapSSML([promptClosingScript])}`);
            return {
                IsSaveSuccess: saved,
                ConfirmationNumber: confirmNum,
                PromptClosingScript: PromptHelper.wrapSSML([promptClosingScript]),
                PromptProvideConfirmNumberSSML1: confirmNumberText
            };
        }
        else if(currentAbsence.AbsenceIncident && callData.IsSubmitted === ClaimStatus.InProgress){
            //******
            await new Promise(resolve => setTimeout(resolve, 3000));
            return {
                    SubmitStatus:ClaimStatus.InProgress
                }
        }
        else 
        {
            //*********the question responses are hard-coded intentionally. otherwise while sending api post request it send gibberish in the request */
            
            if(selectedAbsenceTreatmentOption > 0)
            {
                let absClassificationResponse = {"CQ_ASK_FOR_CLASSIFY_ABSENCE_QUESTION": selectedAbsenceTreatmentOption === 1 ? "Flare-up": "Treatment"};
                currentAbsence.AbsenceIncident.QuestionResponses = currentAbsence.AbsenceIncident.QuestionResponses? {...currentAbsence.AbsenceIncident.QuestionResponses, ...absClassificationResponse} : {absClassificationResponse};
            }
            else if(selectedAbsenceTreatmentOption == 0)
            {
                let absClassificationResponse = {"CQ_ASK_FOR_CLASSIFY_ABSENCE_QUESTION": "Flare-up"};
                currentAbsence.AbsenceIncident.QuestionResponses = currentAbsence.AbsenceIncident.QuestionResponses? {...currentAbsence.AbsenceIncident.QuestionResponses, ...absClassificationResponse} : {absClassificationResponse};
            }
            callService.Logger.log("Updating absence and saving with fixed/company question responses");
            callService.Logger.log(currentAbsence.AbsenceIncident);
            //isSubmitted is set to "InProgress" and saved to DDB 
            await callService.setCallDataAsync(contactId, {
                IsSaved: "true",
                IsSubmitted: ClaimStatus.InProgress
            });
            //submit request is sent to API
            let submittedIncident: SubmitAbsenceResponse = await absenceApi.submitIncidentAsync(callData.ClientCode, contactId, currentAbsence.AbsenceIncident);
            currentAbsence.ValidationErrors = [];
            if (submittedIncident.ValidationErrors.length > 0) {
                currentAbsence.ValidationErrors = submittedIncident.ValidationErrors;
                callService.Logger.error(JSON.stringify(submittedIncident.ValidationErrors));
                await callService.logStageAsync(contactId, lambdaName, submittedIncident.ValidationErrors);
            }
            else 
            {
                currentAbsence.AbsenceIncident = submittedIncident.AbsenceIncident;
                currentAbsence.ClosingScripts ? currentAbsence.ClosingScripts?.push(submittedIncident.AbsenceClosingScriptModel) : currentAbsence.ClosingScripts = [submittedIncident.AbsenceClosingScriptModel]

                callService.Logger.log(`The confirmation number is ${submittedIncident.AbsenceIncident.AbsenceIncidentId}`);
                saved = true;
                confirmNum = submittedIncident.AbsenceIncident.AbsenceIncidentId;

                promptClosingScript = he.decode(submittedIncident.AbsenceClosingScriptModel?.peekLast()?.Text.find((x:any) => x?.Locale.toLowerCase() == selectedLang.toLowerCase())?.Description  ?? '');
                confirmNumberText = PromptHelper.getMergedPrompt(config.clientConfig.ConfirmationNumber1Text ?? [], selectedLang, '{ConfirmationNumber}', currentAbsence.AbsenceIncident.AbsenceIncidentId, "telephone");
                
            }
            callService.Logger.log(`Closing script firsrt time : ${PromptHelper.wrapSSML([promptClosingScript])}`);
            callData.Absences.push(currentAbsence);
            await callService.setCallDataAsync(contactId, {
                IsSaved: "true",
                Absences: callData.Absences,
                IsSubmitted: ClaimStatus.Submitted
            });
        }
    }

    return {
        IsSaveSuccess: saved,
        ConfirmationNumber: confirmNum,
        PromptClosingScript: PromptHelper.wrapSSML([promptClosingScript]),
        PromptProvideConfirmNumberSSML1: confirmNumberText,
        SubmitStatus: ClaimStatus.Success
    };
}

//1st invoke of submit lambda - request sent tp API
    //- Lambda response takes more than 8 sec to return to CallFlow - Timeout error seen in callflow logs
//2nd invoke of submit lambda - the session's absence has iSubmitted = 'inProgress'
    //- we do nothing and wait 3 sec and return SubmitStatus= 'inprogress'
//3rd invoke of submit lambda happens because the SubmitStatus = 'inProgress'
    // - Till then the API has returned with response and isSubmitted='submitted' and accordingly response sent back to call Flow


