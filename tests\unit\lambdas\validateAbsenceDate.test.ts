import { flowEvent, generateDate } from "../mocks/mock-all";
import { validateAbsenceDateHandler } from '../../../src/functions'
import { startDb, stopDb, createTables, deleteTables } from "jest-dynalite";
import {MockAll, TearDown} from '../mocks/mock-all'
import Sinon from "sinon";
jest.setTimeout(60000)

describe('Unit test for app handler',  () => {

    beforeAll(async () => {});
    beforeEach( async() => { 
        MockAll();  
        ////&console.log("In create");
    })

    afterEach ( async () => {
        ////&console.log("In delete");
        TearDown();
        await deleteTables();
        await createTables();
    })
    afterAll(async () => {
        ////&console.log("In stopdb");
        await stopDb();
    })
    it('verifies successful response', async () => {
        let flowObject: any =  {
            Details : {
             ContactData: {
                ...flowEvent.Details.ContactData
              },
              Parameters : {
                AbsenceDateFormatType: 'MMDDYYYY',
                ReportAbsenceDate: generateDate(2)
              }
          }
            };
        
        flowObject.Details.Parameters = {...flowObject.Details.Parameters, ...flowEvent.Details.Parameters};
        const result = await validateAbsenceDateHandler(flowObject)
        console.log(result);
        expect(result).toBeDefined();        
        expect((result).IsAbsDateValid).toBe(true);
        expect((result).ReportAbsenceDatePlayable).toBeDefined();
        
        // expect(result.message).toEqual(null);
    });

    it('check for failing result', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'e6gkw3e3-a66b-4362-af33-65c752a6321',
              },
              Parameters : {
                AbsenceDateFormatType: '1',
                ReportAbsenceDate: '13302022'
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await validateAbsenceDateHandler(flowObject)

        expect(result).toBeDefined();
        expect((result).AbsDateNotValidReason).toBeDefined();
        expect ((result).AbsDateNotValidReason).toContain('date format')
        expect((result).IsAbsDateValid).toBe(false);
        expect((result).ReportAbsenceDatePlayable).toBeDefined();
    });
})
