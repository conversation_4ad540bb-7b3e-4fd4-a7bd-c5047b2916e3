{"ClientName": "LOCALDEV", "Environment": "DEV", "ConfigurationData": {"CallflowARN": {"CallFlowEnterReason": "arn:aws:connect:us-east-1:xxxxxxxxxxxx:instance/gggggggg-gggg-gggg-gggg-gggggggggg/contact-flow/gggggggg-gggg-gggg-gggg-gggggggggg", "CallFlowValidateEmployee": "arn:aws:connect:us-east-1:xxxxxxxxxxxx:instance/gggggggg-gggg-gggg-gggg-gggggggggg/contact-flow/gggggggg-gggg-gggg-gggg-gggggggggg", "CallFlowTransferToCSR": "arn:aws:connect:us-east-1:xxxxxxxxxxxx:instance/gggggggg-gggg-gggg-gggg-gggggggggg/contact-flow/gggggggg-gggg-gggg-gggg-gggggggggg", "CallFlowEnterRTW": "arn:aws:connect:us-east-1:xxxxxxxxxxxx:instance/gggggggg-gggg-gggg-gggg-gggggggggg/contact-flow/gggggggg-gggg-gggg-gggg-gggggggggg", "CallFlowEnterAbsDate1": "arn:aws:connect:us-east-1:xxxxxxxxxxxx:instance/gggggggg-gggg-gggg-gggg-gggggggggg/contact-flow/gggggggg-gggg-gggg-gggg-gggggggggg", "CallFlowTransferToEmployer": "arn:aws:connect:us-east-1:xxxxxxxxxxxx:instance/gggggggg-gggg-gggg-gggg-gggggggggg/contact-flow/gggggggg-gggg-gggg-gggg-gggggggggg", "CallFlowEnterAbsDate2": "arn:aws:connect:us-east-1:xxxxxxxxxxxx:instance/gggggggg-gggg-gggg-gggg-gggggggggg/contact-flow/gggggggg-gggg-gggg-gggg-gggggggggg"}, "LambdaARN": {"LambdaValidateShiftStartTime": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-validateShiftStartTime", "LambdaSaveAbsence": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-saveAbsence", "LambdaSaveAbsenceDate": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-saveAbsenceDate", "LambdaInitDatabase": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-initDatabase", "LambdaServicePingAsync": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-servicePingAsync", "LambdaValidateShiftEndTime": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-validateShiftEndTime", "LambdaValidateDoB": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-validateDoB", "LambdaSaveSubReason": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-saveSubReason", "LambdaServicePing": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-servicePing", "LambdaGetAbsenceReportingConfigLocal": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-getAbsenceReportingConfigLocal", "LambdaValidateEmployeePhoneNumber": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-validateEmployeePhoneNumber", "LambdaValidateEmployer": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-validateEmployer", "LambdaValidateReturnToWorkDate": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-validateReturnToWorkDate", "LambdaValidateRTWShiftStartTime": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-validateRTWShiftStartTime", "LambdaGetPrequalifyQuestions": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-getPrequalifyQuestions", "LambdaClearAddedAbsences": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-clearAddedAbsences", "LambdaGetAbsenceReportingConfig": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-getAbsenceReportingConfig", "LambdaGetMainReasonMenu": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-getMainReasonMenu", "LambdaDroppedSessionMonitor": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-droppedSessionMonitor", "LambdaSaveAbsenceAsync": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-saveAbsenceAsync", "LambdaSaveTotalAbsenceDay": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-saveTotalAbsenceDay", "LambdaGetSubReasonMenu": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-getSubReasonMenu", "LambdaGetSaveAbsenceStatus": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-getSaveAbsenceStatus", "LambdaGetEmployerPhoneNumbers": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-getEmployerPhoneNumbers", "LambdaMinus": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-minus", "LambdaValidateAbsenceDate": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-LOCAL-validateAbsenceDate", "LambdaCheckUSHoliday": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-checkUSHoliday", "LambdaSetContinuousAbsence": "arn:aws:lambda:us-east-1:xxxxxxxxxxxx:function:cicivrus-setContinuousAbsence"}, "MaxRetryLambda2": "20", "MaxRetryLambda": "3"}}