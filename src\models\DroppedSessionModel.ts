import { AbsenceIncident } from "./AbsenceModel";

export class DroppedSessionModel{
    ClientCode: string = "";
    ContactId: string = "";
    AbsenceIncident: AbsenceIncident = {};
    CallStageLog: CallStageLogEntry []= [];
    IsSaved: string = "true";
    IsSubmitted: string ="";

}

 export class CallStageLogEntry {
    public LambdaName?: string;
    public TimeStamp?: Date|string;
    public Result?: any;
    public Error?: any;
    [item: string]: any;

    constructor(error: any, result: any, lambdaName: string) {
        (this.LambdaName = lambdaName), (this.TimeStamp = new Date().toISOString()), (this.Result = result);
        this.Error = error;
    }
    
}
