import { ConnectContactFlowEvent } from "aws-lambda";
import { LocalConfiguration, ScopedHttpService } from "@/services";
const localConfig = new LocalConfiguration();
export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'checkFmlaBalanceEnabled';
    localConfig.Logger.debug('Input data', event);
    
    let clientCode = event.Details.Parameters.ClientCode;
    let contactId = event.Details.ContactData.ContactId;
    localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
    let config = await localConfig.getClientConfigAsync(clientCode);
    let enabled: boolean = false;
    if(config)
    {
        enabled =  config.clientConfig.EnableFmlaBalance ?? false;
    }

    return {
        IsEnabled: enabled
    }
}