import { ParsedMail } from 'mailparser';
import {CallLog,  AmazonS3Service, DocumentApiService} from '@/services'
import { SNSEvent } from 'aws-lambda';
import { ParsedVoiceMail } from '@/models';

const s3service = new AmazonS3Service();
const callService = new CallLog();
const docApiService = new DocumentApiService();
export const handler = async (event:SNSEvent) =>{
    callService.Logger.log('Input data', JSON.stringify(event));   
  
    callService.Logger.log(JSON.stringify(event.Records[0].Sns?.Message));
    const message = event.Records[0].Sns.Message;
    const parsedMessage = typeof message === "string" ? JSON.parse(message) : message;

    const snsMessage = parsedMessage;//JSON.parse(event.Records[0].Sns.Message);

    // Extract the S3 bucket and object key from the SNS message
    const s3Bucket = snsMessage.receipt.action.bucketName;
    const s3Key = snsMessage.receipt.action.objectKey;
    var parsedEmail: ParsedVoiceMail = await s3service.GetEmailFromS3(s3Bucket, s3Key);
    callService.Logger.log('Parsed Email in vmcallback', JSON.stringify(parsedEmail));
    let contactId = parsedEmail.Body;
    const callData = await callService.getCallDataAsync(contactId);
    if(callData != null || callData != undefined && callData.Absences){
        let currentAbsence = callData.Absences?.peekLast();
        if(currentAbsence.AbsenceIncident)
        {
            callService.Logger.log("Attachment content", parsedEmail.Attachment[0]);
            var response = await docApiService.AddDocument(parsedEmail, currentAbsence.AbsenceIncident, contactId);
             await callService.setCallDataAsync(contactId, {
                    IsSaved: "true",
                    VMFileIdFromAPI: response.DocumentId
                    });
        }
    }

  };