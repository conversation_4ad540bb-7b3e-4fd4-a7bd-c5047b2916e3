import { Constants } from "@/util/constants";
import { LogService } from "@/util";
import moment, {Moment} from "moment";
import { LocaleModel } from "./AbsenceModel";

let log = new LogService();
export class EmployeeData {
    EmployeeData?: EmployeeDataDetails;
    IsEmployeeValid: boolean = false;
    IsValidDob: boolean = false; 
    ValidationError?: any;      
}
export class EmployeeDataDetails {    
    EmployeeInfo: Record<string, any> = {};
    EmpClaims?: EmployeeClaim;
    EmployeeAddress?: Record<string, any>;
    EmployeeCustomField?: Record<string, any>;    
}
export interface CaseDetails{
    ClaimId: string;
    ClaimStartDate?: string |Moment;
    LeaveStartDate?: string |Moment;
    LeaveEndDate?: string |Moment;
    PrimaryReasonId: string;
    PrimaryReason?: LocaleModel[];
    SecondaryReasonId: string;
    SecondaryReason?: LocaleModel[];
    IVROptionNumber?: string |number;
}
export function GetCountry(employeeAddress: Record<string, any>[]) : string{
    log = new LogService();
    let address :Record<string,any> |undefined  = GetWorkAddress(employeeAddress);    
    address = (address.length == 0 || address?.peekLast().postalCode == '' || undefined || null) ? GetHomeAddress(employeeAddress) : address;    
    address = ((address?.peekLast().postalCode == '' || undefined || null) ? employeeAddress[0] : address) ?? {}; 
    log.log(address);
    return address?.peekLast()?.countryCode ?? "US" ;
}
export function GetWorkAddress(employeeAddress: Record<string, any>[]): Record<string,any>{
    return employeeAddress.filter((add: any) => add.addressTypeId == Constants.OfficeAddressType);
}
export function  GetHomeAddress(employeeAddress: Record<string, any>[]): Record<string,any>{
    return employeeAddress.filter((add: any) => add.addressTypeId == Constants.HomeAddressType);
}
export function  GetProvinceCode(employeeAddress: Record<string,any>[]): string{
    let address :Record<string,any> |undefined  = GetWorkAddress(employeeAddress);
    address = (address != undefined && (address.length == 0 || address?.peekLast().postalCode == ''|| undefined|| null)) ? GetHomeAddress(employeeAddress) : address;
    address = ((address != undefined && (address.length == 0 || address?.peekLast().postalCode == ''|| undefined|| null)) ? employeeAddress[0] : address)?? {};
    return address?.peekLast()?.provinceStateCode ?? "";
}
export interface EmployeeCustomField{
    Name: string,
    Value: string
}
export interface EmployeeClaim
{
   items: EmployeeClaimItem[];
}

export interface EmployeeClaimItem {
    incidentId: string | number,
    incidentTypeCssClass?: string; 
    lastDateOfAbsence?: string | Moment;
    claimStartDate?: string | Moment;
    leaveStartDate?: string | Moment;
    leaveEndDate?: string | Moment;
    claimDates: EmployeeClaimDate[];
    claimStatus: string;
    primaryReasonOfAbsenceId: string;
    primaryReasonOfAbsenceLocale: LocaleModel[];
    secondaryReasonOfAbsenceId: string;
    secondaryReasonOfAbsenceLocale: LocaleModel[];
    policies?: PolicyModel[];
}
export interface PolicyModel{
    policyCode: string;
    locale?: LocaleModel[];
}
export interface EmployeeClaimDate {

        startDate?: string | Moment
        isPartialAbsence?: boolean
        unpaidTimeInMinutes?: number
        endDate?: string | Moment
        shiftDuration?: string |null
        scheduledShiftStartTime?: string | Moment | null
        scheduledShiftEndTime?: string | Moment | null
}