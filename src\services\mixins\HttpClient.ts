import {BaseService, IHttpService} from '@/services'
import axios, {AxiosInstance, AxiosRequestConfig, AxiosStatic} from 'axios'
import { AnyConstructor } from 'Mixins'

export const instance = axios.create()

//TODO setup scoped
export const HttpClient  = <T extends AnyConstructor<BaseService>>(target: T)=> { //FUTURE
    return class extends target implements IHttpService {
        AxiosClient: AxiosInstance | AxiosStatic = instance; //TODO
        BaseUrl: string = '';
        constructor(...args: any[]){
          super(...arguments);  
        }

        async getAsync(url: string): Promise<any> {
          return await this.AxiosClient?.get(url)
        }

        async postAsync(url: string, payload: any): Promise<any> {
          return await this.AxiosClient?.post(url, payload)
        }

        async putAsync(url: string, payload: any): Promise<any> {
          return await this.AxiosClient?.put(url, payload)
        }
    };
  };

  