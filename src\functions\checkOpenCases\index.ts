
import moment from 'moment';
import {ConnectContactFlowEvent} from 'aws-lambda';
import {AbsenceApiService, AbsenceReportingService, CallLog, LocalConfiguration, ScopedHttpService} from '@/services';
import { Constants, PromptHelper} from '@/util';
import { DropDownOption, PageModel, PageSectionModel, CaseDetails, ClientConfigWrapper, LocaleModel } from '@/models';

const callService = new CallLog();
const localConfig = new LocalConfiguration();
const apiService = new AbsenceApiService();
const reportingService = new AbsenceReportingService();
export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'checkOpenCases';
    callService.Logger.log('Input data', event);
    let res= {
        IsLeaveCaseAvailable: false,
        PromptAssociateLeaveSSML: '',        
        IsPromptNewLeaveSSMLBlank: true,
        PromptNewLeaveSSML:'',
        NumberOfOptions: 0,
        PromptClaimCaseSSML: '',
        OptionToRepeat: '',//*,
        OptionToNotAssociateClaim: ''

    };
    let  contactId = event.Details.ContactData.ContactId; 
    const callData = await callService.getCallDataAsync(contactId);
    let selectedLang = callData.SelectedLang ?? 'en';
    let currentIncident = (callData.Absences)?.peekLast();
    localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
    if(currentIncident.AbsenceIncident)
    {
        const configuration = await localConfig.getClientConfigAsync(callData.ClientCode);
        //if referral creation is enabled then send update to API 
        let leaveAssociationQText = '';
        let newLeaveQtext = '';
        let isLeaveEnabled = configuration?.clientConfig.EnableLeaveCreation ?? false;
        if(isLeaveEnabled){//***check this value type */
            callService.Logger.log('Leave creation enabled');
            callService.Logger.log('Update absence', JSON.stringify(currentIncident.QuestionReponse));
            if(currentIncident){
                callService.Logger.log("checkopencases-requested absence to API", JSON.stringify(currentIncident.AbsenceIncident));
            //1. send absence dates to API 
            let updatedIncident = await apiService.updateIncidentAsync(callData.ClientCode, contactId, currentIncident.AbsenceIncident );
            if(updatedIncident.AbsenceIncident != null){
            currentIncident = {...currentIncident, ...updatedIncident};            
            }
            callData.Absences.pop();
            callData.Absences.push(currentIncident);
            await callService.setCallDataAsync(contactId, {
                IsSaved: "true",
                Absences: callData.Absences
            });
            }
           
            
            //2. check if association question is returned - this will come only when there are open cases for EE
            let leaveQuestions: any = currentIncident.QuestionReponse?.filter((page: PageModel) => page.PageId === Constants.FIXED_TRIGGER_QUESTION_PAGE_ID).peekLast()  ;
            let section: PageSectionModel = leaveQuestions?.Sections?.filter((sec: PageSectionModel) => sec.SectionId === Constants.FIXED_TRIGGER_QUESTION_PAGE_ID).peekLast();

            //Leave association question
            let leaveAssociationQ = section?.Questions?.filter((q) => q.Name === `${Constants.CompanySpecificQuestions_Prefix}${Constants.AskForExistingLeaveCase}`).peekLast();
             leaveAssociationQText = leaveAssociationQ != undefined? (leaveAssociationQ.Label.length > 0 ? configuration.getPromptText(leaveAssociationQ.Label, selectedLang, false):''): '';
             let lvOptionTxt = configuration.getPromptText(configuration?.clientConfig.NewLeaveOptionText, selectedLang, false); 
                
            //3. check case ids returned and if yes get the details
            if(leaveAssociationQ != undefined && Array.isArray(leaveAssociationQ.Options)){
                callService.Logger.log('Api returned open leave cases and assocuition Q');
                //get the caseids in an array from the options.values
                // let resultCases: CaseDetails[] = [];
                let optionValues = leaveAssociationQ.Options?.map((o: DropDownOption) => (o.Value) );
                callService.Logger.log('Open cases found:', JSON.stringify(optionValues) );
                let claimDetails: CaseDetails[] = await reportingService.getLeaveClaimDetails(contactId, callData.ClientCode, callData.EmployeeInfo.employeeNumber, optionValues, Constants.IncidentTypeLeaveCss);
                callService.Logger.log('Claim Details', claimDetails);
                if(claimDetails && claimDetails.length > 0){
                    callService.Logger.log('Obtained open leave case details');
                    //4. filter out the cases which meets the condition specified                    
                    //Build option menu
                    res.NumberOfOptions = claimDetails.length;
                    currentIncident.OpenCases = claimDetails;
                    //var x = buildOpenCaseMenu(claimDetails, configuration, selectedLang);
                    res.PromptClaimCaseSSML = PromptHelper.wrapSSML(buildOpenCaseMenu(claimDetails, configuration, selectedLang));               
                    res.IsLeaveCaseAvailable = true;
                    //let lvOptionTxt = configuration.getPromptText(configuration?.clientConfig.NewLeaveOptionText, selectedLang, false); 
                    res.PromptAssociateLeaveSSML = PromptHelper.getPromptWithSpeakTag("conversational", leaveAssociationQText.concat(' ', lvOptionTxt));
                    
                }
            }
            //4. if leave association Q is not there then check/map/return new leave creation Q
            let newLeaveQ = section.Questions?.filter((q) => q.Name === `${Constants.CompanySpecificQuestions_Prefix}${Constants.ApplyForLeaveReferral}`).peekLast();
            newLeaveQtext = newLeaveQ != undefined? (newLeaveQ.Label.length > 0 ? configuration.getPromptText(newLeaveQ.Label, selectedLang, false):''): '';
            
            if(newLeaveQ != undefined && newLeaveQtext != '' ){
                //get option text
                callService.Logger.log('API returned with new leave referal question', JSON.stringify(newLeaveQ));
                res.IsPromptNewLeaveSSMLBlank = false;
                res.PromptNewLeaveSSML = PromptHelper.getPromptWithSpeakTag("conversational", newLeaveQtext.concat(' ', lvOptionTxt));
            }
         }
        res.OptionToRepeat = configuration.clientConfig.PRRepeatDigit ?? "*";
        res.OptionToNotAssociateClaim = configuration.clientConfig.NonAssociationDigit ?? "#";
        callData.Absences.pop();
        callData.Absences.push(currentIncident);            
        await callService.setCallDataAsync(contactId, {
            IsSaved: "true",
            Absences: callData.Absences
            }); 
        }      
        callService.Logger.log('Result', res); 
        return res;
    }


function buildOpenCaseMenu(openCases: CaseDetails[], configuration: ClientConfigWrapper, selectedLang: string): string[]{
    const res: string[] = [];
   //LeaveCaseAssociationText = For claim started on {LeaveStartDate} for {AbsenceReason}, press {Number}
   //LeaveOptionText = If you do not want to associate this absence with any of your open leave case, press the hash key. To Rpeat, press the star key
    openCases.forEach((x, idx) => {
        //rname = "illness or injury illness or injury at work"
        callService.Logger.log(x.SecondaryReason);
       
        let rName = configuration.getPromptText(x.PrimaryReason, selectedLang, false).concat('.  ').concat(configuration.getPromptText(x.SecondaryReason, selectedLang, false));
        
        if(configuration.getPromptText(x.PrimaryReason, selectedLang, false) == '' && configuration.getPromptText(x.SecondaryReason, selectedLang, false) == '')
            rName = configuration.getPromptText(x.PrimaryReason, 'EN', false).concat('.  ').concat(configuration.getPromptText(x.SecondaryReason, 'EN', false));
        
        callService.Logger.log('Primary & Secondary Reasons', rName); 
        //levestartdate = <say as><interpret =date>x.claimstartdate</sayas>
        let promptText =  PromptHelper.getMergedPrompt(configuration.clientConfig.LeaveCaseAssociationText?.filter((x) => x.Locale?.toLowerCase() == selectedLang.toLocaleLowerCase()) ?? [], selectedLang, x.LeaveStartDate != null ? "{LeaveStartDate}" : "started on {LeaveStartDate}", x.LeaveStartDate != null ? moment(x.LeaveStartDate).format("YYYY-MM-DD") : '', 'date', false);
        promptText[0].Description = promptText[0].Description.replaceAll('{AbsenceReason}', rName).replaceAll('{Number}', PromptHelper.mapToNumLocale(idx, selectedLang).toString());
        
        res.push(promptText[0].Description);
    })
 let optionText = configuration.getPromptText(configuration.clientConfig.LeaveOptionText, selectedLang, false);
 res.push(optionText);

 return res;

}
