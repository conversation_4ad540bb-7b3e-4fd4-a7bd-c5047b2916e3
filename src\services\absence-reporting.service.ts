import { EmployeeData, AbsenceDates, EmployeeClaim } from '@/models';
import { AbsenceIncident, ReasonEntry } from '@/models/AbsenceModel';
import { CaseDetails } from '@/models/EmployeeData';
import { EmployeeApiService, CallLog } from '@/services'
import { DateHelper, ValidationErrors } from '@/util'
import moment, {Moment} from 'moment'

export default class AbsenceReportingService extends EmployeeApiService {
  
  private _callLog: CallLog | any = null;

  set CallLog(callLogService: CallLog) {
    this._callLog = callLogService;
  }

  get CallLog(): CallLog {
    return this._callLog;
  }

  public async validateEmployeeIdAsync(
    contactId: string,
    clientCode: string,
    employeeNumber: string,
  ): Promise<boolean> {
    try {
      let empInfo = await this.getEmployeeAsync(
        clientCode,
        employeeNumber,
        contactId,
      );
      let isValidEmpId = false;
      if (empInfo === undefined) {
        this.Logger.log('employeeId is not valid', empInfo);
        return isValidEmpId;
      }
      isValidEmpId = empInfo.employeeId != null;

      return isValidEmpId;
    } catch (error: any) {
      this.Logger.error('ReportingService error', error);
      return false;
    }
  }

  private async tryGetEmployeeFromCache( contactId: string,clientCode: string, employeeNumber: string): Promise<any> {
    const cacheKey = `empData_${contactId}`;
    const empData = await this.getCachedAsync(cacheKey, async () => {
      return await this.getEmployeeAsync(clientCode, employeeNumber, contactId);
    });
    return empData;
  }
  private async tryGetClaimsFromCache(contactId: string,clientCode: string, employeeNumber: string): Promise<EmployeeClaim>{
    const claimCacheKey = `claim_${contactId}`;
    const empClaims: EmployeeClaim = await this.getCachedAsync(
      claimCacheKey,
      async () => {
        return await this.getEmployeeClaims(
          clientCode,
          contactId,
          employeeNumber,
          "createdDate",
        );
      },
    );
    return empClaims;
  }
  public async getEmployeeDataAsync(contactId: string, clientCode: string, employeeNumber: string, employeeBirthDate: string, doBFormat: string,): Promise<EmployeeData |any>  {
    const toReturn: EmployeeData = {
      IsEmployeeValid: false,
      IsValidDob: false,
    };

    try {
      const isDobFormatValid = moment(
        employeeBirthDate,
        doBFormat,
        true,
      ).isValid();
      this.Logger.log(
        `Date format validation for date: ${employeeBirthDate} format: ${doBFormat} valid:${isDobFormatValid}`,
      );
      if (!isDobFormatValid) {
        throw new Error(ValidationErrors.InvalidDoBFormat);
      }
      employeeBirthDate = moment(employeeBirthDate, doBFormat).format(
        'YYYYMMDD',
      );

      const empCacheKey = `empData_${contactId}`;
      this.Logger.log(`CacheKey:${empCacheKey}`);
      
      const empClaims: EmployeeClaim = await this.tryGetClaimsFromCache(contactId, clientCode, employeeNumber)

      const empData = await this.tryGetEmployeeFromCache(contactId, clientCode, employeeNumber);
     
      if (isDobFormatValid) {
        const empDob = moment(empData.birthDate);
        toReturn.IsValidDob = moment(empDob).isSame(employeeBirthDate);
        this.Logger.log(
          `Employee birth Date validation for date: ${employeeBirthDate} valid:${isDobFormatValid}`,
        );
        if (!toReturn.IsValidDob) {
          throw new Error(ValidationErrors.InvalidDoB);
        }
      }
      toReturn.IsEmployeeValid = empData.employeeId != null ? true : false;

        this.Logger.log('Emp Claim ', empClaims);
      if (!toReturn.IsEmployeeValid) {
        this.Logger.log('employee is not valid', empData);
        throw new Error(ValidationErrors.InvalidEmployee);
      }

      toReturn.EmployeeData = { EmployeeInfo: empData, EmpClaims: empClaims };
      await this.CallLog.setCallDataAsync(contactId, {
        ClientCode: clientCode,
        EmployeeInfo: empData,
        EmployeeClaims: empClaims
      });
      return toReturn;
    } catch (error: any) {
      this.Logger.error('ReportingService error', error);
      if (
        error.message.includes(ValidationErrors.InvalidEmployee) ||
        error.message === ValidationErrors.InvalidDoB ||
        error.message === ValidationErrors.InvalidDateFormat
      ) {
        let result = {
          IsEmployeeValid: false,
          ValidationError:
            error.message ===
            (ValidationErrors.InvalidDoBFormat
              ? ValidationErrors.InvalidDoBFormat
              : error.message === ValidationErrors.InvalidDoB
              ? ValidationErrors.InvalidDoB
              : ValidationErrors.InvalidEmployee),
              
        };
        return {...toReturn, ...result} ;
      }
      throw error;
    }
  }
  public async getCachedClaimsAsync(contactId: string): Promise<EmployeeClaim> {
    return await this.getCachedAsync(`claim_${contactId}`, async () => {
      throw Error(
        'Employee absence data not loaded, and not enough information to load it!',
      );
    });
  }

  public async generateAbsenceIncident(
    contactId: string,
    clientCode: string,
    reason: ReasonEntry,
    locale: string
  ): Promise<AbsenceIncident> {
    try {
      const empInfo = await this.tryGetEmployeeFromCache(
        contactId,
        clientCode,
        '',
      );

      const sessionAbsence: AbsenceIncident = {
        PrimaryReason: reason.Id,
        SecondaryReason: reason.Child?.IsLeaf ? reason.Child.Id : '', //TODO
        EmployeeNumber: empInfo.employeeNumber,
        ClientCode: clientCode,
        AbsenceSource: "IVR",
        ReportedBy: "Employee",
        ReportingLocale: locale
      };
      
      return sessionAbsence;
    } catch (err) {
      this.Logger.error(err);
      throw 'Employee not in cache';
    }
  }

  public async getAbsenceDatesAsync(
    contactId: string,
    localSessionOnly: boolean = false,
  ): Promise<AbsenceDates[]> {
    try {
      //get cahced claims

      const empClaims: EmployeeClaim = await this.getCachedClaimsAsync(
        contactId,
      );
      this.log.log('Cached Employee Info', empClaims);

      const previouslyReportedDates: AbsenceDates[]  = localSessionOnly
        ? []
        : (empClaims?.items).flatMap(({ incidentId, claimDates }) =>          
            claimDates.length?claimDates.map(
              ({
                startDate,
                endDate,
                shiftDuration,
                scheduledShiftStartTime,
                scheduledShiftEndTime,                
              }) => ({
                IncidentId: incidentId,
                StartDate: startDate, //startDate,
                EndDate: endDate,
                ShiftStartTime: startDate,
                ShiftEndTime: endDate,
                ShiftDuration: shiftDuration,
                ScheduledShiftStartTime: scheduledShiftStartTime ?? startDate,
                ScheduledShiftEndTime: scheduledShiftEndTime ?? endDate,
                IsOverLap: false,
                IsDuplicate: false,
              }),
            ):[],
          );
      this.Logger.log('Previously reported absences', previouslyReportedDates);
      const callService = new CallLog();
      let sessionData = await callService.getCallDataAsync(contactId);
      this.Logger.log('Session stored Absence object', sessionData.Absences);

      let newReportedDates: AbsenceDates[] = [];
      let currentIncident = sessionData.Absences.peekLast();
      if (currentIncident && Array.isArray(currentIncident.AbsenceIncident.AbsenceDates)) {         
            newReportedDates = [...newReportedDates, ...currentIncident.AbsenceIncident.AbsenceDates]
      }
      this.Logger.log('Session stored Absences', newReportedDates);
      const result = [...previouslyReportedDates, ...newReportedDates];

      this.Logger.log('GetAbsenceDatesAsync Result', result);

      return result;
    } catch (error: any) {
      this.Logger.error('ReportingService: getAbsenceDatesAsync error', error);
      throw error;
    }
  }

  public async getAbsenceIncidents(contactId: string): Promise<AbsenceIncident[]> {
      let sessionAbsences:AbsenceIncident[] | null = (await this.CallLog.getCallDataAsync(contactId))?.Absences;
      return sessionAbsences || [];
  }

  public async checkOverlappingAbsences(contactId: string, reportingAbsence: AbsenceDates): Promise<AbsenceDates[]> {
    const absenceDates = await this.getAbsenceDatesAsync(contactId, true);
    let overlapIncidents: AbsenceDates[] = [];
    let reportedStartDate = moment(reportingAbsence.StartDate).startOf('day');
    
    if (reportedStartDate) {
      const absenceDatesList = Array.from(absenceDates);
      overlapIncidents = absenceDatesList.filter((x: AbsenceDates) => (
      moment(x.StartDate).startOf('day').isSame(reportedStartDate) 
      //(moment(x.StartDate).isBefore(reportedEndDate) && moment(x.EndDate).isAfter(reportedStartDate))
      ));
      //let startDt = absenceDates.map(t => moment(t.StartDate).startOf('day'));
      
      if(overlapIncidents.length > 0){
        overlapIncidents.forEach(x => x.IsOverLap = true)
        
      }
    }   
    return overlapIncidents;
  }
  
    public async checkDuplicateAbsences(contactId: string, enteredDate: Moment, isContinuousOrIntermittent: string): Promise<boolean>{
    this.Logger.log('CheckDuplicateAbsences', enteredDate);
    let isDuplicate: boolean = false;
    let sessionAbsenceDates = await this.getAbsenceDatesAsync(contactId, isContinuousOrIntermittent.toLowerCase() === "true" ? false : true); //TODO: Return AbsenceIncidents instead    

    isDuplicate = sessionAbsenceDates.some(d => moment(d.ShiftStartTime, 'YYYY-MM-DD').isSame(moment(enteredDate, 'YYYY-MM-DD'), 'day'));
    this.Logger.log('checkDuplicateAbsences Result', isDuplicate);
    return isDuplicate;
  }
  public async markSessionDuplicateAbsences(contactId: string, overlappingIds: string[], enteredDate: Moment): Promise<string>  {
    let result: string = '';
    let sessionAbsences = await this.getAbsenceDatesAsync(contactId, true); //TODO: Return AbsenceIncidents instead
    let sessionIncidents = await this.getAbsenceIncidents(contactId);
    if (overlappingIds && overlappingIds.length) { 
      // The entered date overlaps with at least one absence, somewhere
      result = ValidationErrors.OverlappedEntry;
      
      // At this point, it is known the the entered date does overlap.
      // To determine if it is a duplicate, the IncidentId of the in-progress 
      // absence claim must be contained in the call sessions' AbsenceIds
      if (sessionAbsences && sessionAbsences.length) {
          //The current absence is the latest entry in the session absences
          let currentAbsence = sessionIncidents.peekLast();
          
          // Find if any absences that overlap with entered date are part of the 
          // current session
          // START TODO: Use Reduce for this block.
          const sessionAbsenceList = Array.from(sessionAbsences);
          let overlapSessionAbsences = sessionAbsenceList.filter( (x) => x.IncidentId && 
                                              overlappingIds.find( entry => entry == `${x.IncidentId}`)
                                            );
          sessionAbsences.forEach( x => {
            let overLappedEntry:AbsenceDates | undefined;
            if (overLappedEntry = overlapSessionAbsences.find(z => z.IncidentId == x.IncidentId)) {
            x.IsOverLap = true;
            x.OverlapIncidentId = overLappedEntry?.IncidentId; 
            x.IsDuplicate = ((currentAbsence?.AbsenceIncidentId && currentAbsence.AbsenceIncidentId) == x.IncidentId) && x.StartDate == enteredDate
            }
          })
          // END TODO
          if (currentAbsence && overlapSessionAbsences.some(x => x.IsDuplicate)) {
              result = ValidationErrors.DuplicateEntry
          }
      }
      await this.CallLog.setCallDataAsync(contactId, {AbsenceDates: sessionAbsences});
  }
  return result;
  }
  public async getLeaveClaimDetails(contactId: string, clientCode: string, employeeNumber: string, claimNumbers?: string[], claimType?: string):Promise<CaseDetails[]>{
    claimNumbers = claimNumbers?.filter((c) => c != "0").sort((a,b)=> b.localeCompare(a, undefined, {numeric: true}));
    let claims = await this.getEmployeeClaims(clientCode, contactId,employeeNumber, "leaveStartDate", claimNumbers);
    let claimDetails: CaseDetails[] = [];
    if(claims.items != undefined && Array.isArray(claims.items) && claims.items.length > 0){
       claims.items.forEach((item) => { 
      let cl :CaseDetails = {
        ClaimId: item.incidentId.toString(),
        ClaimStartDate: item.claimStartDate,
        LeaveStartDate: item.leaveStartDate,
        LeaveEndDate: item.leaveEndDate,
        PrimaryReasonId: item.primaryReasonOfAbsenceId,
        PrimaryReason: item.primaryReasonOfAbsenceLocale,
        SecondaryReasonId: item.secondaryReasonOfAbsenceId,
        SecondaryReason: item.secondaryReasonOfAbsenceLocale
         }
      claimDetails.push(cl);
      })
  }
  
  return claimDetails;//.sort((a,b) => moment(a.LeaveStartDate).startOf('day').diff(moment(b.LeaveStartDate).startOf('day')));

  }
  public static updateAbsenceDate(absence: AbsenceDates,{StartDate, EndDate, ScheduledStartTime, ScheduledEndTime, OverWrite}: any ): any {
    absence.StartDate = DateHelper.getReportedShiftTime(absence.StartDate, StartDate);
    absence.ShiftStartTime = DateHelper.getReportedShiftTime(absence.ShiftStartTime, StartDate);
    absence.EndDate = DateHelper.getReportedShiftTime(absence.EndDate, EndDate);
    absence.ShiftEndTime = DateHelper.getReportedShiftTime(absence.ShiftEndTime, EndDate);
    absence.ScheduledShiftEndTime = DateHelper.getReportedShiftTime(absence.ScheduledShiftEndTime, ScheduledEndTime);
    absence.ScheduledShiftStartTime = DateHelper.getReportedShiftTime(absence.ScheduledShiftStartTime, ScheduledStartTime);
  }

  public static updateAbsDatesInRange(startAbsence: AbsenceDates, lastAbsence: AbsenceDates, ScheduledStartTime?: any, ScheduledEndTime?: any): AbsenceDates[]
  {
    let toReturn : AbsenceDates[] = [];
    let lastDateMoment = moment(lastAbsence.ShiftStartTime);
    let startDateMoment =  moment(startAbsence.ShiftStartTime).startOf('day');
    let duration = moment(startAbsence.ShiftEndTime).diff(moment(startAbsence.ShiftStartTime), 'hours',);
    //lda -fda
    const numberOfDays = lastDateMoment.diff(startDateMoment, 'days');
    const startTime = moment(startAbsence.ShiftStartTime).format("HH:mm");
    const endTime = moment(startAbsence.ShiftEndTime).format("HH:mm");
    if(numberOfDays > 0)
    {
      for (let i = 0; i <= numberOfDays; i++) {
        let currentDate = startDateMoment.clone().add(i, 'days');

        // Format the date to match your requirement
        let formattedStartDate = currentDate.startOf('day').add(moment.duration(startTime));
        let formattedEndDate = formattedStartDate.clone().add(duration, "hours");
        let scheduledShiftStartTime = DateHelper.getReportedShiftTime(currentDate, ScheduledStartTime);
        let scheduledShiftEndTime = DateHelper.getReportedShiftTime(currentDate, ScheduledEndTime);
        // Push the formatted date with shift start and end time of startDate to the result array
        toReturn.push({
            StartDate: formattedStartDate.format('YYYY-MM-DD HH:mm:ss'),
            EndDate:formattedEndDate.format('YYYY-MM-DD HH:mm:ss'),
            ShiftStartTime: formattedStartDate.format('YYYY-MM-DD HH:mm:ss'),
            ShiftEndTime: formattedEndDate.format('YYYY-MM-DD HH:mm:ss'),
            ScheduledShiftStartTime: scheduledShiftStartTime,
            ScheduledShiftEndTime: scheduledShiftEndTime,
            ShiftDuration: startAbsence.ShiftDuration,
            IsOverLap: startAbsence.IsOverLap,
            IsDuplicate: startAbsence.IsDuplicate

        });
      }
    }
    return toReturn;
   }

    public async GetAbsenceDateRange(startDate: Moment, endDate: Moment): Promise<string[]>{      

        this.Logger.log('Range Start Date', startDate);
        this.Logger.log('Range End Date', endDate);

        // Calculate the difference in days between the start date and end date
        const numberOfDays = endDate.diff(startDate, 'days');
        this.Logger.log('Number Of Days in Range', numberOfDays);
        // Generate the array of dates between the start date and end date
        let dateRange = [];
        if (numberOfDays > 0) {           
            for (let i = 1; i <= numberOfDays; i++) {
                let date = startDate.clone().add(i, 'days');
                dateRange.push(date.format('YYYY-MM-DD')); // Format the date as desired
            }
        }
      
     
        return dateRange;
      
    }

    public async checkDuplicateAbsencesInBulk(contactId: string, enteredDateList: string[], isContinuousOrIntermittent: string): Promise<boolean> {
        let isDuplicate: boolean = false; 
        this.Logger.log('Entered Absence Dates', enteredDateList);   
        let sessionAbsenceDates = await this.getAbsenceDatesAsync(contactId, isContinuousOrIntermittent.toLowerCase() === "true" ? false : true);
        let sessionDatesInString = sessionAbsenceDates.map(x => this.extractDate(x.ShiftStartTime));
      //  let sessionabsdatesinString = sessionAbsenceDates.map(x => moment(x.ShiftStartTime).format('YYYY-MM-DD'));
      //  this.Logger.log('session Absence Dates', sessionabsdatesinString); 
        this.Logger.log('session Absence Dates 2', sessionDatesInString);
        let enteredDatelistinSet = new Set(enteredDateList);
        isDuplicate = sessionDatesInString.some(d => enteredDatelistinSet.has(d));
        //isDuplicate = sessionAbsenceDates.some((d) => enteredDateList.some(x => moment(d.ShiftStartTime).startOf('day').isSame(moment(x).startOf('day'))));      
        this.Logger.log('IsDuplicate', isDuplicate);
        return isDuplicate;
    }
    public extractDate (input: Moment | string |undefined): string  {
      if (!input) {
        // Handle undefined
        return '';
      }    
      if (moment.isMoment(input)) {
          // If it's a Moment object, format it to 'YYYY-MM-DD'
          return input.format('YYYY-MM-DD');
      } else if (typeof input === 'string') {
          // If it's a string, extract the date part
          return input.split('T')[0];
      }
      throw new Error('Invalid input type'); // Handle unexpected cases
  };
  

    public async isDateWithinValidRange(maxBeforeDays: number | null, max: number | null, absenceMomentDate: moment.Moment): Promise<boolean> {
        if (maxBeforeDays == null || max == null) {
            return false;
        }
        this.Logger.log(`Date NOW: ${JSON.stringify(moment(new Date(), "YYYY-MM-DD").startOf('day'))}`);
        const daysFromNow = moment(absenceMomentDate, "YYYY-MM-DD").diff(moment(new Date(), "YYYY-MM-DD").startOf('day'), 'days') + 1;
        this.Logger.log(`${maxBeforeDays * -1}<= ${daysFromNow} <= ${max}`);
        return daysFromNow > maxBeforeDays * -1 && (daysFromNow) <= max+1;
    }

  public isLdaGreaterThanFda(lda: moment.Moment, fda: AbsenceDates): boolean {
    return moment(lda, "YYYY-MM-DD").diff(moment(fda.ShiftStartTime, "YYYY-MM-DD").startOf('day'), 'days') > 0;
 }
    public isTotalDaysGreaterThanMax(maxAbsenceDays: number, totalAbsenceDays: number): boolean{
        this.Logger.log('Max Absence days', maxAbsenceDays);    
        this.Logger.log('total absence days', totalAbsenceDays);  
        return totalAbsenceDays > maxAbsenceDays ;
 }

}