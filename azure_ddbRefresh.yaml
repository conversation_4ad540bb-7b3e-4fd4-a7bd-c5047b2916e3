
#this is a client config cache clearing pipeline. It calls the main CF_ddbtable_refresh template where it deletes and created the ablarivr-client-config tables.
trigger: none
pool:
    vmImage: 'ubuntu-latest'

variables:
  # - group: ablarivr_QA
  - name: Region
    value: "us-east-1"
  - name: ClientConfigTableName
    value: "ablarivr-client-config"
  - name: ClientConfigStack
    value: "ABLARIVR-Lambda-DDB-Config"
  - name: AwsCredentialsQA
    value: "lifeworks-connect-lab"
  - name: AwsCredentialsUAT
    value: "lifeworks-connect-uat"
  - name: AwsCredentialsPROD
    value: "lifeworks-connect-PROD"

  
  

stages:
  - stage: DeployQA
    displayName: Deploy to QA
    variables: 
    - group: ablarivr_QA
    jobs:
      - deployment: BuildAndDeploy
        displayName: Build and Deploy
        environment: 'AbilitiApiIVRLambda-QA'
        strategy:
            runOnce:
              deploy:
                  steps:
                    - checkout: self
                    - task: S3Upload@1
                      displayName: Create Deploy Bucket
                      inputs:
                        awsCredentials: $(AwsCredentialsQA)
                        bucketName: $(AwsBucket)
                        regionName: $(Region)
                        globExpressions: "undefined"
                        createBucket: true                                    
                    - task: AWSShellScript@1
                      displayName: Run delete script
                      env:
                        workingDir: "$(System.DefaultWorkingDirectory)/QA"
                      inputs:
                        awsCredentials: $(AwsCredentialsQA)
                        regionName: $(Region)
                        scriptType: 'inline'
                        inlineScript: |
                          echo $(ClientConfigStack) &&
                          sam delete --stack-name $(ClientConfigStack) --region $(Region) --s3-bucket $(AwsBucket) --no-prompts

                    - task: AWSShellScript@1
                      displayName: Package client config table
                      env:
                        workingDir: "$(System.DefaultWorkingDirectory)/QA"
                      inputs:
                        awsCredentials: $(AwsCredentialsQA)
                        regionName: $(Region)
                        scriptType: 'inline'
                        inlineScript: |
                          echo $workingDir &&
                          sam build --debug \
                          -s . \
                          --template-file ./CFTemplates/CF_ddbtable_refresh.yaml

                    - task: AWSShellScript@1
                      displayName: Deploy DynamoDB tables
                      inputs:
                        awsCredentials: $(AwsCredentialsQA)
                        regionName: $(Region)
                        scriptType: "inline"
                        inlineScript: |                
                          sam deploy \
                          --template-file ./CFTemplates/CF_ddbtable_refresh.yaml \
                          --no-confirm-changeset \
                          --no-fail-on-empty-changeset \
                          --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
                          --stack-name $(ClientConfigStack) \
                          --s3-bucket $(AwsBucket) \
                          --s3-prefix $(ClientConfigStack) \

  - stage: DeployUAT
    displayName: Deploy to UAT
    variables: 
    - group: ablarivr_UAT
    jobs:
      - deployment: BuildAndDeploy
        displayName: Build and Deploy
        environment: 'AbilitiApiIVRLambda-UAT'
        strategy:
            runOnce:
              deploy:
                  steps:
                    - checkout: self
                    - task: S3Upload@1
                      displayName: Create Deploy Bucket
                      inputs:
                        awsCredentials: $(AwsCredentialsUAT)
                        bucketName: $(AwsBucket)
                        regionName: $(Region)
                        globExpressions: "undefined"
                        createBucket: true 
                                  
                    - task: AWSShellScript@1
                      displayName: Run delete script
                      env:
                        workingDir: "$(System.DefaultWorkingDirectory)/UAT"
                      inputs:
                        awsCredentials: $(AwsCredentialsUAT)
                        regionName: $(Region)
                        scriptType: 'inline'
                        inlineScript: |
                          echo $(ClientConfigStack) &&
                          sam delete --stack-name $(ClientConfigStack) --region $(Region) --s3-bucket $(AwsBucket) --no-prompts

                    - task: AWSShellScript@1
                      displayName: Package config table
                      env:
                        workingDir: "$(System.DefaultWorkingDirectory)/UAT"
                      inputs:
                        awsCredentials: $(AwsCredentialsUAT)
                        regionName: $(Region)
                        scriptType: 'inline'
                        inlineScript: |
                          echo $workingDir &&
                          sam build --debug \
                          -s . \
                          --template-file ./CFTemplates/CF_ddbtable_refresh.yaml

                    - task: AWSShellScript@1
                      displayName: Deploy DynamoDB tables
                      inputs:
                        awsCredentials: $(AwsCredentialsUAT)
                        regionName: $(Region)
                        scriptType: "inline"
                        inlineScript: |                
                          sam deploy \
                          --template-file ./CFTemplates/CF_ddbtable_refresh.yaml \
                          --no-confirm-changeset \
                          --no-fail-on-empty-changeset \
                          --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
                          --stack-name $(ClientConfigStack) \
                          --s3-bucket $(AwsBucket) \
                          --s3-prefix $(ClientConfigStack) \

  - stage: DeployPROD
    displayName: Deploy to PROD
    variables: 
    - group: ablarivr_PROD
    jobs:
      - deployment: BuildAndDeploy
        displayName: Build and Deploy
        environment: 'AbilitiApiIVRLambda-PROD'
        strategy:
            runOnce:
              deploy:
                  steps:
                    - checkout: self
                    - task: S3Upload@1
                      displayName: Create Deploy Bucket
                      inputs:
                        awsCredentials: $(AwsCredentialsPROD)
                        bucketName: $(AwsBucket)
                        regionName: $(Region)
                        globExpressions: "undefined"
                        createBucket: true 
                                  
                    - task: AWSShellScript@1
                      displayName: Run delete script
                      env:
                        workingDir: "$(System.DefaultWorkingDirectory)/PROD"
                      inputs:
                        awsCredentials: $(AwsCredentialsPROD)
                        regionName: $(Region)
                        scriptType: 'inline'
                        inlineScript: |
                          echo $(ClientConfigStack) &&
                          sam delete --stack-name $(ClientConfigStack) --region $(Region) --s3-bucket $(AwsBucket) --no-prompts
                        

                    - task: AWSShellScript@1
                      displayName: Package config table
                      env:
                        workingDir: "$(System.DefaultWorkingDirectory)/PROD"
                      inputs:
                        awsCredentials: $(AwsCredentialsPROD)
                        regionName: $(Region)
                        scriptType: 'inline'
                        inlineScript: |
                          echo $workingDir &&
                          sam build --debug \
                          -s . \
                          --template-file ./CFTemplates/CF_ddbtable_refresh.yaml

                    - task: AWSShellScript@1
                      displayName: Deploy DynamoDB tables
                      inputs:
                        awsCredentials: $(AwsCredentialsPROD)
                        regionName: $(Region)
                        scriptType: "inline"
                        inlineScript: |                
                          sam deploy \
                          --template-file ./CFTemplates/CF_ddbtable_refresh.yaml \
                          --no-confirm-changeset \
                          --no-fail-on-empty-changeset \
                          --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
                          --stack-name $(ClientConfigStack) \
                          --s3-bucket $(AwsBucket) \
                          --s3-prefix $(ClientConfigStack) \
