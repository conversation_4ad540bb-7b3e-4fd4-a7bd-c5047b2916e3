### Intial setup and install for local development
<b>Prerequisites</b>
 - Docker desktop with Docker compose
 - Node (Version 18+, currently developed on 18, updated to 22.x)
 - Visual Studio Code (to take advantage of the launch profiles and tooling)

 **Setup**
 
Before setting up make sure system is restarted after docker installation and docker desktop is running in your local.
The IVR Lambdas and services communicate with the Abiliti APIs and DynamoDB. No setup is necessarily required for development against the APIs*. As for Dynamo DB:
  -  In the ./dynamodb folder there is a docker-compose.yaml
  - Navigate to this folder and run `docker-compose up` 
  - This will launch 2 containers 
	  - One that runs as AWS CLI (for interacting with DynamoDb...it's just nice for investigative work and it does run some initial creation scripts)
	  - The other container is the local dynamodb instance, set by default to run at port 8000. 


With Node and npm installed, and from the root directory you will need to run `npm install` 
 - This will install all dependencies listed in the package.json file at the project root

That's it for setup!

**Running the project**

The scripts are currently under development, currently in order to build and run the application, run th following from the root:
`npm run start` 

This will build and transpile the typescript and place it into a folder called `build`. It will then start the default server for local testing (/src/devserver -> /build/src/devserver).

To make the process of running/debugging functions meant to run in a serverless environment easier...we run them in a server! There is a lightweight server where you can actually invoke lambdas in a..."with-server" manner.

The local dynamo-db in docker needs dummy credentials to connect the local code. As such we have added those credentials in dynamodb/aws-cli/Dockerfile. This will help in creating the tables in our local container in docker. 
We also need to add the credentials when doing GET/UPDATE/PUT in dynamo-db local. The code is in the src/services/DynamiDbClient.ts file. Please uncomment the credentials part when debugging locally.

If using VSCode, there are 3 launch profiles included:
 - One for running/debugging the devserver `Debug TypeScript in Node.js`
 - One for running/debugging lambdas serverless `debug`
 - One for debugging Mock logic (since this application communicates with 3 separate APIs as well as a database, sometimes it is worth running thorugh/debugging the mock setup logic) `Debug Mocks in Node.js`

**Testing**

This project uses Jest for unit and integration testing. Optionally, and if using VSCode you may want to add a Jest plugin (`Orta.vscode-jest` is one that works well, but there are a few good ones like `firsttris.vscode-jest-runner`) for continuous testing, and for a real top-notch debugging experience. Otherwise, you can run:
`npm run test`

Due to number of different API requests, it does not make sense to mock request/responses within each test suite. Instead, there is a config file located at `tests/unit/mocks/responses.json` and a helper `tests/unit/mocks/mock-all.ts`. For Dynamo-db mocking, we are using Jest dynalite. This avoid any dependency on an actual dynamodb instance, any sdk, runtime, aws-provided software etc. 

**Pipelines**

There are 4 pipelines in this project
  - azure-pipeline.yml is the MAIN pipeline which deploys all resources mentioned in the template.yaml Cloude formation template. It has a trigger to deploy when changes are merged to Development branch.
  - azure_ddbRefresh.yaml deletes and creates the cache table inside AWS dynamodb using the CFTemplates/CF_ddbtable_refresh.yaml Cloud formation template. This is used whenever, we change configuration in OHS Config and needs to reflect in AWS.
  - azure-dynamodb-pipeline.yml creates all the dynamo db tables using the CFTemplates/CF_ddbtemplate.yaml cloud formation template. This is a 1 time pipeline created to set up new AWS environments. NOT NEEDED TO RUN AT ALL as the setup has already been completed.
  - azure_VM_pipeline.yml create the S3 bucket for voicemail flow using the CFTemplates/CF_VoiceMail_Resource.yaml Cloud Formation template. THIS IS A 1 TIME PIPELINE RUN FOR SETTING UP S3 in AWS. DO NOT RUN IT AGAIN.

  All pipelines use the Azure Library sets - ablarivr_QA, ablarivr_UAT and ablarivr_PROD
  There are 3 environments in Azure - 
  - AbilitiApiIVRLambda-QA
  - AbilitiApiIVRLambda-UAT
  - AbilitiApiIVRLambda-PROD

TODO:
 - Add hot-reloading & true TS debugging while developing (currently, the project is built after s change is made in order to debug)
 - Do we want a seeding script on deploy?
 - Fix Dynamo Client
   - Multiple table names in a single call chain?
 - Make HttpMixin scoped
 - Write other services
 - Decide on interfacing for base service(s)
 - Review "FUTURE" and "TODO" comments
 - Fix naming of service files (decide on a convention)
 - Architecture discussions