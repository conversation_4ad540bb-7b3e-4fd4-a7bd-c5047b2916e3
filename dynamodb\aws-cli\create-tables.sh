#!/bin/bash
echo "########### Creating profile ###########"
aws configure set aws_access_key_id default_access_key --profile=dynamo-db-local
aws configure set aws_secret_access_key default_secret_key --profile=dynamo-db-local
aws configure set region us-east-1 --profile=dynamo-db-local

echo "########### Listing profile ###########"
aws configure list

echo "Attempt delete"

aws $AWS_DYNAMODB_ENDPOINT dynamodb delete-table --table-name ablarivr-data-cache
aws $AWS_DYNAMODB_ENDPOINT dynamodb delete-table --table-name ablarivr-call-session
aws $AWS_DYNAMODB_ENDPOINT dynamodb delete-table --table-name ablarivr-client-config
aws $AWS_DYNAMODB_ENDPOINT dynamodb delete-table --table-name ablarivr-reasonmenu

echo "Create database"

aws $AWS_DYNAMODB_ENDPOINT dynamodb create-table \
    --table-name ablarivr-data-cache \
    --attribute-definitions \
        AttributeName=CacheKey,AttributeType=S \
    --key-schema \
        AttributeName=CacheKey,KeyType=HASH \
    --provisioned-throughput \
        ReadCapacityUnits=1,WriteCapacityUnits=1 \

aws $AWS_DYNAMODB_ENDPOINT dynamodb create-table \
    --table-name ablarivr-call-session \
    --attribute-definitions \
        AttributeName=ContactId,AttributeType=S \
    --key-schema \
        AttributeName=ContactId,KeyType=HASH \
    --provisioned-throughput \
        ReadCapacityUnits=1,WriteCapacityUnits=1 \


aws $AWS_DYNAMODB_ENDPOINT dynamodb create-table \
    --table-name ablarivr-client-config \
    --attribute-definitions \
        AttributeName=ClientName,AttributeType=S \
    --key-schema \
        AttributeName=ClientName,KeyType=HASH \
    --provisioned-throughput \
        ReadCapacityUnits=1,WriteCapacityUnits=1 \

aws $AWS_DYNAMODB_ENDPOINT dynamodb create-table \
    --table-name ablarivr-reasonmenu \
    --attribute-definitions \
        AttributeName=ClientName,AttributeType=S \
    --key-schema \
        AttributeName=ClientName,KeyType=HASH \
    --provisioned-throughput \
        ReadCapacityUnits=1,WriteCapacityUnits=1 \

aws $AWS_DYNAMODB_ENDPOINT dynamodb update-time-to-live --table-name ablarivr-reasonmenu \
                      --time-to-live-specification Enabled=true,AttributeName=ablarivr-reasonmenu


aws $AWS_DYNAMODB_ENDPOINT dynamodb create-table \
    --table-name ablarivr-callflow-config \
    --attribute-definitions \
        AttributeName=Instance,AttributeType=S \
    --key-schema \
        AttributeName=Instance,KeyType=HASH \
    --provisioned-throughput \
        ReadCapacityUnits=1,WriteCapacityUnits=1 \

echo "Update TTL"
aws $AWS_DYNAMODB_ENDPOINT dynamodb update-time-to-live --table-name ablarivr-data-cache \
                      --time-to-live-specification Enabled=true,AttributeName=ablarivr-data-cache
 echo "Update GSI"
 aws $AWS_DYNAMODB_ENDPOINT dynamodb update-table \
     --table-name ablarivr-client-config \
     --attribute-definitions \
            AttributeName=ClientCode,AttributeType=S \
     --global-secondary-index-updates \
         "[{\"Create\":{\"IndexName\": \"ClientCodeIdx\",\"KeySchema\":[{\"AttributeName\":\"ClientCode\",\"KeyType\":\"HASH\"}], \
         \"ProvisionedThroughput\": {\"ReadCapacityUnits\": 1, \"WriteCapacityUnits\": 1      },\"Projection\":{\"ProjectionType\":\"ALL\"}}}]"

aws $AWS_DYNAMODB_ENDPOINT dynamodb update-table \
     --table-name ablarivr-call-session \
     --attribute-definitions \
            AttributeName=IsSubmitted,AttributeType=S \
     --global-secondary-index-updates \
         "[{\"Create\":{\"IndexName\": \"IsSubmittedIdx\",\"KeySchema\":[{\"AttributeName\":\"IsSubmitted\",\"KeyType\":\"HASH\"}], \
         \"ProvisionedThroughput\": {\"ReadCapacityUnits\": 1, \"WriteCapacityUnits\": 1      },\"Projection\":{\"ProjectionType\":\"ALL\"}}}]"


echo "Show Tables"
aws $AWS_DYNAMODB_ENDPOINT dynamodb list-tables

echo "Show all items"
aws $AWS_DYNAMODB_ENDPOINT dynamodb scan --table-name ablarivr-data-cache

if [ ! -f firstrun.lock ]; then
    echo "Creating lock file to enable container to run indefinitely"
    touch firstrun.lock
    tail -f /dev/null
fi