/*function MapField() {
    return function(target: any)
}*/
import { CallbackFunction, SimpleMapFunc, IPrototype } from '@/types'
import { AbsenceConfig, LocaleModel, ReasonEntry, ReasonFlow } from './AbsenceModel';
import {Constants, PromptHelper} from '@/util'
import { ClientConfigWrapper } from './ClientConfigModel';
import moment from 'moment';

//TODO: GETTYPE FOR DEEPCOPY HELPER?

//TODO move locale setting into wrappers

export interface ReasonMenuSSML { //TODO: incorporate this
    PromptPrimaryReasonMenuOptionsSSML: string;
    NumberOfOptions: number | string;
    OptionToRepeat: string,
    PromptSpecialMsgSSML: string;
    PromptIntroPrimaryReasonMenuSSML: string;  
    IntroPrimaryMenuBypassOption: string |number;  
}

export class AnswerWrapper {
    get isBereavement(): boolean {
        return  !!(this?.answerModel?.IsBereavement || this.ResponseText()?.includes('Bereavement'))
    }

    AbsenceConfig: AbsenceConfig;

    answerModel: AnswerModel;
    wrappedSubAnswers: AnswerWrapper[] = [];
    constructor(answer: AnswerModel, config?: AbsenceConfig) {
        this.answerModel = answer;

        this.AbsenceConfig = {
            MaxDaysAllowed: this.answerModel?.MaxDaysAllowed ??  config?.MaxDaysAllowed,
            ReportableDaysInFuture: this.answerModel?.ReportableDaysInFuture ?? config?.ReportableDaysInFuture,
            ReportableDaysInPast: this.answerModel?.ReportableDaysInPast ?? config?.ReportableDaysInPast
        }
        if(this.answerModel.SubAnswers ){        
        for (let i of this.answerModel.SubAnswers) {
            this.wrappedSubAnswers.push(new AnswerWrapper(i, this.AbsenceConfig))
        }
    }
    }

     //TODO: Typescript offers reflection for this
     public static getTypeDef(propName: string): any {
        switch (propName) {
            case 'SubAnswers':
                return new AnswerModel();
            case 'Locales':
            case 'locales':
                return {Locale: '', Description: ''}
        }
    }

    public ResponseText(locale: string = 'EN'): string | undefined {
        return this.AnswerText.find( x => x.Locale == locale)?.Text;
    }

    get AnswerText(): ReasonText[] {
        if (!this.answerModel.AnswerText) {
            this.answerModel.AnswerText = [];
            MapReasonText(this.answerModel.Locales, this.answerModel.AnswerText);
        }
        return this.answerModel.AnswerText;
    }
}

//TODO: Could also use prototypical inheritance
export class ReasonWrapper {
    reasonModel: ReasonModel;
    WrappedAnswers: AnswerWrapper[];

    get Answers(): AnswerModel[] {
        return this.WrappedAnswers.map( x=> x.answerModel);
    }

    constructor(reason: ReasonModel) {
        this.reasonModel = reason;
        this.WrappedAnswers = reason.Answers.map( (x) => new AnswerWrapper(x));
    }

    get ReasonTextMap (): Record<string, ReasonText[]> {
        // In JS properties can be accessed with array index as well as
        // dot operator, but in order to keep things as strictly-typed
        // as possible, this Map is introduced. There is no overhead involved.
        // TypeScript reflection can also be used to achieve this.
        return {
            'Manager': this.reasonModel.Manager,
            'Employee': this.reasonModel.Employee,
            'FamilyMember': this.reasonModel.FamilyMember,
            'MainText': this.reasonModel.MainText
        }
    }

    public getMainText(position: string = 'Employee', locale: string = 'EN'): string {
        return this.ReasonTextMap[position].find( x=> x.Locale == locale)?.Text || '';
    }

    // public getPromptText(transformFunc?: SimpleMapFunc<string>, role:string = 'Employee', locale: string = 'EN'): string [] {
    //     if (!!transformFunc) {
    //         return [transformFunc(this)];
    //     }
    //     return [this.getMainText(role, locale), ...this.getReasonOptionsText(locale, ''), this.getRepeatText()];
    // }

    public getRepeatText(locale: string = 'EN', text: string, repeatDigit: string, transformFunc?: SimpleMapFunc<string>): string{

        if((repeatDigit != '') && (text != ''))
        {
            switch(repeatDigit)
            {
                case '*':
                    return text.replaceAll("{RepeatDigit}", 
                        locale == Constants.LocaleString_English.toUpperCase() ? Constants.StarKey_EN :
                            locale == Constants.LocaleString_Spanish.toUpperCase() ? Constants.StarKey_ES :
                        Constants.StarKey_FR);
                case '#':
                    return text.replaceAll("{RepeatDigit}", 
                        locale == Constants.LocaleString_English.toUpperCase() ? Constants.PoundKey_EN :
                            locale == Constants.LocaleString_Spanish.toUpperCase() ? Constants.PoundKey_ES :
                        Constants.PoundKey_FR);
                case '$':
                    return text.replaceAll("{RepeatDigit}", 
                        locale == Constants.LocaleString_English.toUpperCase() ? Constants.DollarKey_EN :
                            locale == Constants.LocaleString_Spanish.toUpperCase() ? Constants.DollarKey_ES :
                        Constants.DollarKey_FR);  
            }
            return text.replaceAll("{RepeatDigit}", repeatDigit);
        } 
        
        return `To repeat the menu options, press ${this.reasonModel.RepeatDigit.replaceAll('*', 'star')}.`;
    }

    public getReasonOptionsText(locale: string = 'EN', text: string, resonLevel?: string, transformFunc?: SimpleMapFunc<string>): string [] {
       
        if(text != '')
        {
           const res: string[] = [];
            this.WrappedAnswers.forEach((answer, idx) => {
                let t : string =  text;
                let rName: string = answer.ResponseText(locale)?? '';
                let cntLocale: string = PromptHelper.mapToNumLocale(idx, locale);
                 t = t.replace("{ReasonName}", rName);
                 t = t.replace("{Number}", cntLocale);      
                res.push(t);               
            });
            
            return res;

        }
        return this.WrappedAnswers.map((answer, idx) =>`${answer.ResponseText(locale)}, press ${idx + 1}. `);
    
    
    }
    public getSecondaryReasonOptionsText(locale: string = 'EN', text: string, selectedItem: AnswerWrapper,transformFunc?: SimpleMapFunc<string>): string [] {
       
        if(text != '')
        {
           const res: string[] = [];
          
            selectedItem.wrappedSubAnswers.forEach((answer, idx) => {
                let t : string =  text;
                let rName: string = answer.ResponseText(locale)?? '';
                let cntLocale: string = PromptHelper.mapToNumLocale(idx, locale);
                 t = t.replace("{ReasonName}", rName);
                 t = t.replace("{Number}", cntLocale);      
                res.push(t);               
            });
            
            return res;

        }
        return this.WrappedAnswers.map((answer, idx) =>`${answer.ResponseText(locale)}, press ${idx + 1}. `);
    
    
    }
    
    public getSpecialSSML(transformFunc?: SimpleMapFunc<string>): string {
        if (!!transformFunc) {
            return transformFunc(this);
        }
        throw 'Not Implemented';
    }

    public getMenuForConnect(config: ClientConfigWrapper, locale: string = "EN"): ReasonMenuSSML {
        let result: ReasonMenuSSML = {
            PromptPrimaryReasonMenuOptionsSSML: '',
            NumberOfOptions: '',
            OptionToRepeat: '',
            PromptSpecialMsgSSML: '',
            PromptIntroPrimaryReasonMenuSSML: '',
            IntroPrimaryMenuBypassOption: 'nobypass'
        };
        if(config)
        {
            result.PromptIntroPrimaryReasonMenuSSML = config.getPromptText(config.clientConfig.PrimaryReasonIntroText, locale) ?? '';
            result.PromptSpecialMsgSSML = config.getPromptText(config.clientConfig.PrimaryReasonSpecialText, locale) ?? '';
            
            var configOptionText = config.getPromptText(config.clientConfig.PrimaryReasonMenuOptionText, locale, false) ?? '';

            var optionText = this.getReasonOptionsText(locale, configOptionText);
            var configRepeatTxt = config.getPromptText(config.clientConfig.PrOptionRepeatText, locale, false) ?? '';
            var repeatTxt = this.getRepeatText(locale, configRepeatTxt, config.clientConfig.PRRepeatDigit ?? '');
            optionText.push(repeatTxt);
            result.PromptPrimaryReasonMenuOptionsSSML = PromptHelper.wrapSSML(optionText);            
            result.OptionToRepeat = config.clientConfig.PRRepeatDigit ?? "*";
            let isSpecialMenuOption = this.WrappedAnswers.find((x) => x.answerModel.Ar3IvrReasonSettings?.IsSpecialMenu);
            let isSpecialMenuOptionNum = this.WrappedAnswers.findIndex((x) => x.answerModel.Ar3IvrReasonSettings?.IsSpecialMenu);
            result.IntroPrimaryMenuBypassOption = isSpecialMenuOption != undefined ? isSpecialMenuOptionNum+1  : 'nobypass';
        }
       return result;
    }

  public traverseReasons(reasonFlow: ReasonFlow, latestSelection: number): AnswerWrapper {
        // First, get the last entered reason 
        let currentReason: ReasonEntry | null = reasonFlow.ReasonEntries[reasonFlow.Count-1];
        let root:AnswerWrapper = this.WrappedAnswers[parseInt(`${currentReason.IVROptionNumber}`) - 1];
        while(currentReason = currentReason.Child) {
            let nextLvl = root.wrappedSubAnswers[parseInt(`${currentReason.IVROptionNumber}`) - 1];
            
            if (nextLvl.wrappedSubAnswers.length === 0) {
                // last entry in flow needs to be overwritten
                reasonFlow.ReasonEntries[reasonFlow.Count-1].Child = null;;
                break;
            }
            root = nextLvl;
        }

        return root.wrappedSubAnswers[latestSelection-1];
    }
}

// Using class instead of interface for flexibility/reflection
export class ReasonModel {
    
    RepeatDigit: string = '*';
    QuestionId: string = '';
    QuestionName: string = '';
    QuestionDescription: string = '';
    // Country: string = '';
    // Province: string = '';
    MainText: ReasonText[] = [];
    Manager: ReasonText[] = [];
    Employee: ReasonText[] = [];
    FamilyMember: ReasonText[] = [];
    Answers: AnswerModel[] = [];
    UpdateTimeStamp: string = moment(Date.now()).toString();
    EmployeeNumber: string = '';
    //TODO: another opportunity for Reflection
}
export class Ar3IvrReasonSettings{
    IsTransfer?: boolean = false;
    IsSpecialMenu?: boolean = false;
    Locales?: LocaleModel[] = [];
}
export interface ReasonText {
    Locale: string;
    Text: string;
}

// Using class instead of interface for flexibility/reflection
export class AnswerModel {
    AnswerId: string = '';
    Order?: number;
    AnswerCode?: string;
    IsBereavement?: boolean;
    IsIllness?: boolean;
    IsNoneOfTheAbove?: boolean;
    Locales: any[] = [];
    AnswerText?: ReasonText[];
    ParentAnswerId?: string;
    MaxDaysAllowed?: number;
    NoOfAbsence?: number;
    NoOfSTDAbsence?: number;
    ReportableDaysInFuture?: number;
    ReportableDaysInPast?: number;
    SubAnswers: AnswerModel[] = [];
    AlertText: string | null = null;
    Ar3IvrReasonSettings: Ar3IvrReasonSettings = new Ar3IvrReasonSettings();
    //TODO: IVR AUDIO IDS
    //TODO Related leave types
}

export function MapReasonResponse(ar3ReasonConfig: any/*, country: string, province: string*/, employeeNumber: string): ReasonModel {
    let primaryRoot: any = {};
    let toReturn:ReasonModel = new ReasonModel();
    
    if (primaryRoot = ar3ReasonConfig.reasonOfAbsence) {
        //TODO - this entire block may be replaceable with the generic deep-copy mapper
        toReturn.QuestionId = primaryRoot.questionId;
        toReturn.QuestionName = primaryRoot.questionName;
        toReturn.QuestionDescription = primaryRoot.questionDescription;
        // toReturn.Country = country;
        // toReturn.Province = province;
        toReturn.EmployeeNumber = employeeNumber;
        toReturn.UpdateTimeStamp = moment(Date.now()).toString();
        MapReasonText(primaryRoot.employee, toReturn.Employee);
        MapReasonText(primaryRoot.familyMember, toReturn.FamilyMember);
        MapReasonText(primaryRoot.manager, toReturn.Manager);
        primaryRoot.answers.forEach((x:any) => {
            let newModel: AnswerModel = new AnswerModel();
            newModel = deepMap(x, newModel);
            newModel.AnswerText = [];
            MapReasonText(newModel.Locales, newModel.AnswerText);
            toReturn.Answers.push(newModel)
        }
        );
    }
    return toReturn;
}
/*
Suggested:

export function mapReasonText(from: any[], to: ReasonText[]) {
  from.forEach((item: any) => {
    const { locale = item.Locale, description = item.Description } = item;
    to.push({ Locale: locale, Text: description });
  });
}

*/
//TODO
export function MapReasonText(from: any[], to: ReasonText[]) {    
from.forEach ((x: any) => {
    to.push({
        Locale: x.locale || x.Locale,
        Text :x.description || x.Description
    })
   })
}


//TODO: This doesn;t belong here. Should be in Utils
//TODO: obj = <T>, dest = <Mappable>
function deepMap(obj:any, dest: any): any {
    let toReturn:any = {};
    let AnswerMapping = new Map();
    Object.keys(dest).forEach( x=> AnswerMapping.set(x.toLowerCase(), x));
    Object.entries(obj).forEach(el => {
        let prop = AnswerMapping.get(el[0].toLowerCase());
        if (prop) {
        if (Array.isArray(el[1])) {
            let newArr:any [] = [];
            el[1].forEach(x => {
                if (typeof(x) === 'object'){
                    //TODO for now
                    newArr.push(deepMap(x, AnswerWrapper.getTypeDef(prop))); //TODO -> typesafe, implement type specifier in this scenario
                }
                else {
                    newArr.push(x);
                }
            });
            toReturn[prop] = newArr;
        }
        else if (el[1] && typeof(el[1]) === 'object') {
            toReturn[prop] = deepMap(el[1], dest[prop]);
        }
        else {
            toReturn[prop] = el[1];
        }
    }
    });
    return toReturn;
}
