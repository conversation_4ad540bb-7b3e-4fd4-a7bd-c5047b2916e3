import {ConnectContactFlowEvent} from 'aws-lambda';
import {AbsenceApiService, CallLog, LocalConfiguration, ScopedHttpService} from '@/services';
import { Constants, PromptHelper} from '@/util';
import { PageModel, PageSectionModel } from '@/models';

const callService = new CallLog();
const localConfig = new LocalConfiguration();
const apiService = new AbsenceApiService();

export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'checkEESTD';
    callService.Logger.log('Input data', event);
    let res= {
        IsEESTDEligible: false,
        PromptSTDOptionSSML: '',
    };
    let  contactId = event.Details.ContactData.ContactId; 
    const callData = await callService.getCallDataAsync(contactId);
    let selectedLang = callData.SelectedLang ?? 'en';
    let currentIncident = (callData.Absences)?.peekLast();
    localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
    const configuration = await localConfig.getClientConfigAsync(callData.ClientCode);
    let newStdQtext = '';
    if(currentIncident.AbsenceIncident)
    {
        let updatedIncident = await apiService.updateIncidentAsync(callData.ClientCode, contactId, currentIncident.AbsenceIncident );
        if(updatedIncident.AbsenceIncident != null){
        currentIncident = {...currentIncident, ...updatedIncident};
        callData.Absences.pop();
        callData.Absences.push(currentIncident);
        await callService.setCallDataAsync(contactId, {
            IsSaved: "true",
            Absences: callData.Absences
        });
        let stdQuestions: any = currentIncident.QuestionReponse?.filter((page: PageModel) => page.PageId === Constants.FIXED_TRIGGER_QUESTION_PAGE_ID).peekLast()  ;
        let section: PageSectionModel = stdQuestions?.Sections?.filter((sec: PageSectionModel) => sec.SectionId === Constants.FIXED_TRIGGER_QUESTION_PAGE_ID).peekLast();
        
        let newStdQ = section.Questions?.filter((q) => q.Name === `${Constants.CompanySpecificQuestions_Prefix}${Constants.ApplyForStdReferral}`).peekLast();
        newStdQtext = newStdQ != undefined? (newStdQ.Label.length > 0 ? configuration.getPromptText(newStdQ.Label, selectedLang, false):''): '';
        
        if(newStdQ != undefined && newStdQtext.length >0 )
        {
            callService.Logger.log('Api returned STd referral Q');
            //get option text
            let stdOptionTxt = configuration.getPromptText(configuration?.clientConfig.StdOptionText, selectedLang, false);
            
            res.PromptSTDOptionSSML = PromptHelper.getPromptWithSpeakTag("conversational", newStdQtext.concat(' ', stdOptionTxt));
            res.IsEESTDEligible = true;
        }
      
        }
    }
    callService.Logger.log("result", res);
    return res;
}