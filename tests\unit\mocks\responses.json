{"absenceApiEndpoint": {"POST": [], "GET": [], "PUT": []}, "eeApiEndpoint": {"POST": [{"endpoint": "ElasticSearch/SearchActiveClaims", "calledWith": {"payload": "{\"pageSize\":10,\"pageNumber\":1,\"parameters\":{\"employeeNumber\":\"80083033\"},\"clientCode\":\"GOODYEAR\"}", "config": {"headers": {"Authorization": "Bearer ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Type": "application/json"}}}, "data": {"items": [{"formType": "INCIDENT_GENERIC_LEAVE", "leaveIncidentId": 12467, "outcomeDate": null, "dischargeDate": null, "dateIncidentReported": "2022-11-22T00:19:06.133", "caseManagerUserAccountId": 11930, "outcomeType": "", "updatedDate": "2023-01-31T16:09:42.66", "claimStartDate": "2022-11-21T00:00:00", "ltdEffectiveDate": null, "caseManager": "<PERSON><PERSON><PERSON><PERSON>", "caseManagerEmail": "<EMAIL>", "caseManagerPhone": "", "outcomeTypeLocale": [], "workReadyDate": null, "caseManagerComment": "", "dateCaseComment": null, "notificationCompletedDate": "2022-11-22T00:00:00", "claimAge": 0, "claimAgeLocale": [], "businessUnit": null, "primaryContact": null, "reoccurrenceDate": null, "leaveAbsenceType": "continuous", "leaveAbsenceTypeLocale": [{"locale": "EN", "description": "Continuous", "shortDescription": ""}, {"locale": "FR", "description": "<PERSON><PERSON> continu", "shortDescription": ""}], "leaveRequestDate": "2022-11-22T00:13:51", "leaveStartDate": "2022-11-21T00:00:00", "leaveEndDate": "2022-11-24T00:00:00", "certificationDueDate": null, "certificationStartDate": null, "certificationEndDate": null, "questionResponses": {"CQ_BEST_PHONE_NUMBER_TO_BE_REACHED": "**********", "CQ_EMAIL_ADDRESS_TO_RECEIVE": "<EMAIL>", "CQ_APPLY_FOR_LEAVE_REFERRAL": "true", "PS_dateOfRequest": "2022-11-21", "PS_dateStart": "2022-11-21", "PS_dateEnd": "2022-11-24", "PS_relationship": "self", "PS_seriousHealthCondition": "true", "PS_mostRecentHireDate": "2018-02-26", "PS_lengthOfService": "56", "PS_usedAccruedTimeOff": "false", "PS_qualifyingQuestion_isThisEmployeesSiblingUnder19YearsOfAge": "false", "CQ_RelationshipToEmployee": "self", "CQ_EmployedInMonth": "12", "CQ_EE_QUESTION": "Other"}, "totalAbsenceHours": 32, "incidentReportedState": "BC", "absenceCategory": "continuous", "overallExhaustionDate": null, "jobProtectionExhaustionDate": null, "bestPhoneNumber": "**********", "unpaidTime": 0, "conditionCategory": null, "seriousHealthCondition": null, "conditionCategoryLocale": [], "seriousHealthConditionLocale": [], "leaveIncidentAbsenceDates": [{"date": "2022-11-24T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-11-23T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-11-22T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-11-21T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}], "caseTypeAlias": "Leave Case", "caseTypeAliasLocale": [{"locale": "EN", "description": "Regulated Leave Case", "shortDescription": ""}], "leaveStatus": "PENDING", "leaveStatusLocale": [{"locale": "EN", "description": "Pending", "shortDescription": "Pending"}, {"locale": "FR", "description": "En attente", "shortDescription": "En attente"}], "associatedAbsenceId": 654, "associatedSTDCaseId": 0, "primaryReasonOfAbsenceId": "f862ce45-a4ff-4aa2-8c47-b112235cf61c", "secondaryReasonOfAbsenceId": "ace1e051-e35f-4074-9729-2925414d6a68", "primaryReasonOfAbsenceLocale": [{"locale": "EN", "description": "My own illness or injury", "shortDescription": ""}, {"locale": "FR", "description": "Ma propre maladie ou blessure", "shortDescription": ""}], "secondaryReasonOfAbsenceLocale": [{"locale": "EN", "description": "I have an injury or illness that occurred outside of work", "shortDescription": ""}, {"locale": "FR", "description": "J’ai une blessure ou une maladie qui est survenue en dehors du travail", "shortDescription": ""}], "employeeClaimsListData": {}, "adhocClaimsListData": {}, "incidentTypeCode": 5, "incidentFrom": "Vistamed", "claimNumber": "298711", "caseType": "LEAVE", "caseState": "INIT", "claimType": "Continuous", "incidentReportedDate": "2022-11-22T00:19:06.133", "incidentId": 298711, "createdDate": "2022-11-22T00:19:06.133", "claimStatus": "Open", "claimStatusLocale": [{"locale": "EN", "description": "Open", "shortDescription": ""}, {"locale": "FR", "description": "Ouvert", "shortDescription": ""}], "claimTypeLocale": [{"locale": "EN", "description": "Continuous", "shortDescription": ""}, {"locale": "FR", "description": "PHASE1", "shortDescription": ""}], "caseCancelled": 0, "clientCode": "STARBUCKS", "companyId": 118, "expectedRTWDate": "2022-11-25T00:00:00", "firstDateOfAbsence": "2022-11-21T00:00:00", "dateOfIncident": null, "lastDateWorked": null, "caseTypeGroupType": "RL", "planCaseTypeShortCode": "Leave-Case", "caseEmploymentType": "Full time Sal<PERSON>", "caseEmploymentTypeLocale": [{"locale": "EN", "description": "Full time Sal<PERSON>", "shortDescription": ""}, {"locale": "FR", "description": "Salarié à temps plein", "shortDescription": ""}], "employee": {"employeeId": 586165, "organization": {"organizationId": 166537, "orgLevel1": "STARBUCKS", "orgLevel2": "026", "orgLevel3": "0264", "orgLevel4": "20831"}, "employeeNumber": "00262843", "employeeNumberPadded": "000000000262843", "employeeFirstName": "TARA", "employeeLastName": "SMITH", "employeeName": "TARA SMITH", "union": "", "emP_BusinessUnit": null, "emP_CostCentre": null, "emP_WorkCentreName_Description": null, "isOtherIncidentsRequired": null, "supervisorFirstName": "<PERSON>", "supervisorLastName": "<PERSON>", "supervisorName": "<PERSON>", "employeeGender": "F", "employeeGenderLocale": [{"locale": "EN", "description": "Female", "shortDescription": ""}, {"locale": "FR", "description": "<PERSON>mme", "shortDescription": ""}], "birthDate": "1979-03-09T00:00:00", "dateOfHire": "2018-02-26T00:00:00", "jobTitle": "asst manager", "province": "BC", "emP_Department": "20831", "age": 43, "yearsOfService": 4, "yearsOfServiceValue": "4", "employeeSource": null}, "dateFormatUsed": null, "incidentTypeCssClass": "INCIDENT_GENERIC_LEAVE", "status": "1", "phaseType": "PHASE1", "claimDates": [{"startDate": "2022-11-21T09:00:00-05:00", "endDate": "2022-11-21T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-11-22T09:00:00-05:00", "endDate": "2022-11-22T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-11-23T09:00:00-05:00", "endDate": "2022-11-23T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-11-24T09:00:00-05:00", "endDate": "2022-11-24T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}], "relationShip": "self", "relationShipLocale": [{"locale": "EN", "description": "Self", "shortDescription": ""}, {"locale": "FR", "description": "Soi", "shortDescription": ""}], "policies": [], "leaveTypeCode": "medicalNonOccupational", "leaveTypeLocale": [{"locale": "EN", "description": "Medical Non-Occupational", "shortDescription": ""}, {"locale": "FR", "description": "Medical Non-Occupational", "shortDescription": ""}]}, {"formType": "INCIDENT_GENERIC_LEAVE", "leaveIncidentId": 12469, "outcomeDate": null, "dischargeDate": null, "dateIncidentReported": "2022-12-09T22:18:47.227", "caseManagerUserAccountId": 11930, "outcomeType": "", "updatedDate": "2023-01-31T16:16:49.617", "claimStartDate": "2022-12-09T00:00:00", "ltdEffectiveDate": null, "caseManager": "<PERSON><PERSON><PERSON><PERSON>", "caseManagerEmail": "<EMAIL>", "caseManagerPhone": "", "outcomeTypeLocale": [], "workReadyDate": null, "caseManagerComment": "", "dateCaseComment": null, "notificationCompletedDate": "2022-12-12T00:00:00", "claimAge": 0, "claimAgeLocale": [], "businessUnit": null, "primaryContact": null, "reoccurrenceDate": null, "leaveAbsenceType": "continuous", "leaveAbsenceTypeLocale": [{"locale": "EN", "description": "Continuous", "shortDescription": ""}, {"locale": "FR", "description": "<PERSON><PERSON> continu", "shortDescription": ""}], "leaveRequestDate": "2022-12-09T22:16:01", "leaveStartDate": "2022-12-09T00:00:00", "leaveEndDate": "2022-12-16T00:00:00", "certificationDueDate": null, "certificationStartDate": null, "certificationEndDate": null, "questionResponses": {"CQ_BEST_PHONE_NUMBER_TO_BE_REACHED": "**********", "CQ_EMAIL_ADDRESS_TO_RECEIVE": "<EMAIL>", "CQ_APPLY_FOR_LEAVE_REFERRAL": "true", "PS_dateOfRequest": "2022-12-09", "PS_dateStart": "2022-12-09", "PS_dateEnd": "2022-12-16", "PS_relationship": "self", "PS_seriousHealthCondition": "true", "PS_mostRecentHireDate": "2018-02-26", "PS_lengthOfService": "57", "PS_usedAccruedTimeOff": "false", "PS_resultOfMilitaryService": "false", "PS_victimViolence": "true", "PS_victimCrime": "true", "PS_qualifyingQuestion_isThisEmployeesSiblingUnder19YearsOfAge": "false", "CQ_RelationshipToEmployee": "spouse", "CQ_EmployedInMonth": "1500", "CQ_EE_QUESTION": "Other"}, "totalAbsenceHours": 48, "incidentReportedState": "BC", "absenceCategory": "continuous", "overallExhaustionDate": "2023-06-10T00:00:00", "jobProtectionExhaustionDate": "2023-06-10T00:00:00", "bestPhoneNumber": "**********", "unpaidTime": 0, "conditionCategory": null, "seriousHealthCondition": null, "conditionCategoryLocale": [], "seriousHealthConditionLocale": [], "leaveIncidentAbsenceDates": [{"date": "2022-12-09T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-12-10T00:00:00-05:00", "absenceMinutes": 0, "workMinutes": 0, "isFullDate": true, "approved": null}, {"date": "2022-12-11T00:00:00-05:00", "absenceMinutes": 0, "workMinutes": 0, "isFullDate": true, "approved": null}, {"date": "2022-12-12T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-12-13T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-12-14T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-12-15T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-12-16T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}], "caseTypeAlias": "Leave Case", "caseTypeAliasLocale": [{"locale": "EN", "description": "Regulated Leave Case", "shortDescription": ""}], "leaveStatus": "PENDING", "leaveStatusLocale": [{"locale": "EN", "description": "Pending", "shortDescription": "Pending"}, {"locale": "FR", "description": "En attente", "shortDescription": "En attente"}], "associatedAbsenceId": 656, "associatedSTDCaseId": 0, "primaryReasonOfAbsenceId": "f862ce45-a4ff-4aa2-8c47-b112235cf61c", "secondaryReasonOfAbsenceId": "ace1e051-e35f-4074-9729-2925414d6a68", "primaryReasonOfAbsenceLocale": [{"locale": "EN", "description": "My own illness or injury", "shortDescription": ""}, {"locale": "FR", "description": "Ma propre maladie ou blessure", "shortDescription": ""}], "secondaryReasonOfAbsenceLocale": [{"locale": "EN", "description": "I have an injury or illness that occurred outside of work", "shortDescription": ""}, {"locale": "FR", "description": "J’ai une blessure ou une maladie qui est survenue en dehors du travail", "shortDescription": ""}], "employeeClaimsListData": {}, "adhocClaimsListData": {}, "incidentTypeCode": 5, "incidentFrom": "Vistamed", "claimNumber": "299231", "caseType": "LEAVE", "caseState": "INIT", "claimType": "Continuous", "incidentReportedDate": "2022-12-09T22:18:47.227", "incidentId": 299231, "createdDate": "2022-12-09T22:18:47.227", "claimStatus": "Open", "claimStatusLocale": [{"locale": "EN", "description": "Open", "shortDescription": ""}, {"locale": "FR", "description": "Ouvert", "shortDescription": ""}], "claimTypeLocale": [{"locale": "EN", "description": "Continuous", "shortDescription": ""}, {"locale": "FR", "description": "PHASE1", "shortDescription": ""}], "caseCancelled": 0, "clientCode": "STARBUCKS", "companyId": 118, "expectedRTWDate": null, "firstDateOfAbsence": "2022-12-09T00:00:00", "dateOfIncident": null, "lastDateWorked": null, "caseTypeGroupType": "RL", "planCaseTypeShortCode": "Leave-Case", "caseEmploymentType": "Full time Sal<PERSON>", "caseEmploymentTypeLocale": [{"locale": "EN", "description": "Full time Sal<PERSON>", "shortDescription": ""}, {"locale": "FR", "description": "Salarié à temps plein", "shortDescription": ""}], "employee": {"employeeId": 586165, "organization": {"organizationId": 166537, "orgLevel1": "STARBUCKS", "orgLevel2": "026", "orgLevel3": "0264", "orgLevel4": "20831"}, "employeeNumber": "00262843", "employeeNumberPadded": "000000000262843", "employeeFirstName": "TARA", "employeeLastName": "SMITH", "employeeName": "TARA SMITH", "union": "", "emP_BusinessUnit": null, "emP_CostCentre": null, "emP_WorkCentreName_Description": null, "isOtherIncidentsRequired": null, "supervisorFirstName": "<PERSON>", "supervisorLastName": "Kearney", "supervisorName": "<PERSON>", "employeeGender": "F", "employeeGenderLocale": [{"locale": "EN", "description": "Female", "shortDescription": ""}, {"locale": "FR", "description": "<PERSON>mme", "shortDescription": ""}], "birthDate": "1979-03-09T00:00:00", "dateOfHire": "2018-02-26T00:00:00", "jobTitle": "asst manager", "province": "BC", "emP_Department": "20831", "age": 43, "yearsOfService": 4, "yearsOfServiceValue": "4", "employeeSource": null}, "dateFormatUsed": null, "incidentTypeCssClass": "INCIDENT_GENERIC_LEAVE", "status": "1", "phaseType": "PHASE1", "claimDates": [{"startDate": "2022-12-09T09:00:00-05:00", "endDate": "2022-12-09T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-10T09:00:00-05:00", "endDate": "2022-12-10T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-11T09:00:00-05:00", "endDate": "2022-12-11T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-12T09:00:00-05:00", "endDate": "2022-12-12T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-13T09:00:00-05:00", "endDate": "2022-12-13T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-14T09:00:00-05:00", "endDate": "2022-12-14T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-15T09:00:00-05:00", "endDate": "2022-12-15T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-16T09:00:00-05:00", "endDate": "2022-12-16T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}], "relationShip": "self", "relationShipLocale": [{"locale": "EN", "description": "Self", "shortDescription": ""}, {"locale": "FR", "description": "Soi", "shortDescription": ""}], "policies": [], "leaveTypeCode": "medicalNonOccupational", "leaveTypeLocale": [{"locale": "EN", "description": "Medical Non-Occupational", "shortDescription": ""}, {"locale": "FR", "description": "Medical Non-Occupational", "shortDescription": ""}]}, {"formType": "INCIDENT_GENERIC_LEAVE", "leaveIncidentId": 12472, "outcomeDate": null, "dischargeDate": null, "dateIncidentReported": "2022-12-13T22:07:42.383", "caseManagerUserAccountId": 11930, "outcomeType": "", "updatedDate": "2022-12-16T16:25:13.45", "claimStartDate": "2022-12-13T00:00:00", "ltdEffectiveDate": null, "caseManager": "<PERSON><PERSON><PERSON><PERSON>", "caseManagerEmail": "<EMAIL>", "caseManagerPhone": "", "outcomeTypeLocale": [], "workReadyDate": null, "caseManagerComment": "", "dateCaseComment": null, "notificationCompletedDate": "2022-12-14T00:00:00", "claimAge": 0, "claimAgeLocale": [], "businessUnit": null, "primaryContact": null, "reoccurrenceDate": null, "leaveAbsenceType": "continuous", "leaveAbsenceTypeLocale": [{"locale": "EN", "description": "Continuous", "shortDescription": ""}, {"locale": "FR", "description": "<PERSON><PERSON> continu", "shortDescription": ""}], "leaveRequestDate": "2022-12-13T22:05:59", "leaveStartDate": "2022-12-19T00:00:00", "leaveEndDate": "2022-12-22T00:00:00", "certificationDueDate": null, "certificationStartDate": null, "certificationEndDate": null, "questionResponses": {"PS_dateStart": "12/19/2022 00:00:00", "PS_dateOfRequest": "2022-12-13", "PS_dateEnd": "12/22/2022 00:00:00", "PS_relationship": "self", "PS_usedAccruedTimeOff": "false", "PS_victimViolence": "true"}, "totalAbsenceHours": 32, "incidentReportedState": "BC", "absenceCategory": "continuous", "overallExhaustionDate": "2023-06-10T00:00:00", "jobProtectionExhaustionDate": "2023-06-10T00:00:00", "bestPhoneNumber": "**********", "unpaidTime": 0, "conditionCategory": null, "seriousHealthCondition": null, "conditionCategoryLocale": [], "seriousHealthConditionLocale": [], "leaveIncidentAbsenceDates": [{"date": "2022-12-19T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-12-20T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-12-21T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-12-22T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}], "caseTypeAlias": "Leave Case", "caseTypeAliasLocale": [{"locale": "EN", "description": "Regulated Leave Case", "shortDescription": ""}], "leaveStatus": "PENDING", "leaveStatusLocale": [{"locale": "EN", "description": "Pending", "shortDescription": "Pending"}, {"locale": "FR", "description": "En attente", "shortDescription": "En attente"}], "associatedAbsenceId": 659, "associatedSTDCaseId": 0, "primaryReasonOfAbsenceId": "f862ce45-a4ff-4aa2-8c47-b112235cf61c", "secondaryReasonOfAbsenceId": "ace1e051-e35f-4074-9729-2925414d6a68", "primaryReasonOfAbsenceLocale": [{"locale": "EN", "description": "My own illness or injury", "shortDescription": ""}, {"locale": "FR", "description": "Ma propre maladie ou blessure", "shortDescription": ""}], "secondaryReasonOfAbsenceLocale": [{"locale": "EN", "description": "I have an injury or illness that occurred outside of work", "shortDescription": ""}, {"locale": "FR", "description": "J’ai une blessure ou une maladie qui est survenue en dehors du travail", "shortDescription": ""}], "employeeClaimsListData": {}, "adhocClaimsListData": {}, "incidentTypeCode": 5, "incidentFrom": "Vistamed", "claimNumber": "299317", "caseType": "LEAVE", "caseState": "ACTIVE", "claimType": "Continuous", "incidentReportedDate": "2022-12-13T22:07:42.383", "incidentId": 299317, "createdDate": "2022-12-13T22:07:42.383", "claimStatus": "Open", "claimStatusLocale": [{"locale": "EN", "description": "Open", "shortDescription": ""}, {"locale": "FR", "description": "Ouvert", "shortDescription": ""}], "claimTypeLocale": [{"locale": "EN", "description": "Continuous", "shortDescription": ""}, {"locale": "FR", "description": "PHASE1", "shortDescription": ""}], "caseCancelled": 0, "clientCode": "STARBUCKS", "companyId": 118, "expectedRTWDate": null, "firstDateOfAbsence": "2022-12-19T00:00:00", "dateOfIncident": null, "lastDateWorked": null, "caseTypeGroupType": "RL", "planCaseTypeShortCode": "Leave-Case", "caseEmploymentType": "Full time Sal<PERSON>", "caseEmploymentTypeLocale": [{"locale": "EN", "description": "Full time Sal<PERSON>", "shortDescription": ""}, {"locale": "FR", "description": "Salarié à temps plein", "shortDescription": ""}], "employee": {"employeeId": 586165, "organization": {"organizationId": 166537, "orgLevel1": "STARBUCKS", "orgLevel2": "026", "orgLevel3": "0264", "orgLevel4": "20831"}, "employeeNumber": "00262843", "employeeNumberPadded": "000000000262843", "employeeFirstName": "TARA", "employeeLastName": "SMITH", "employeeName": "TARA SMITH", "union": "", "emP_BusinessUnit": null, "emP_CostCentre": null, "emP_WorkCentreName_Description": null, "isOtherIncidentsRequired": null, "supervisorFirstName": "<PERSON>", "supervisorLastName": "Kearney", "supervisorName": "<PERSON>", "employeeGender": "F", "employeeGenderLocale": [{"locale": "EN", "description": "Female", "shortDescription": ""}, {"locale": "FR", "description": "<PERSON>mme", "shortDescription": ""}], "birthDate": "1979-03-09T00:00:00", "dateOfHire": "2018-02-26T00:00:00", "jobTitle": "asst manager", "province": "BC", "emP_Department": "20831", "age": 43, "yearsOfService": 4, "yearsOfServiceValue": "4", "employeeSource": null}, "dateFormatUsed": null, "incidentTypeCssClass": "INCIDENT_GENERIC_LEAVE", "status": "1", "phaseType": "PHASE1", "claimDates": [{"startDate": "2022-12-19T09:00:00-05:00", "endDate": "2022-12-19T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-20T09:00:00-05:00", "endDate": "2022-12-20T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-21T09:00:00-05:00", "endDate": "2022-12-21T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-22T09:00:00-05:00", "endDate": "2022-12-22T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}], "relationShip": "self", "relationShipLocale": [{"locale": "EN", "description": "Self", "shortDescription": ""}, {"locale": "FR", "description": "Soi", "shortDescription": ""}], "policies": [], "leaveTypeCode": "domesticViolence", "leaveTypeLocale": [{"locale": "EN", "description": "Domestic Violence", "shortDescription": ""}, {"locale": "FR", "description": "Domestic Violence", "shortDescription": ""}]}, {"formType": "INCIDENT_GENERIC_LEAVE", "leaveIncidentId": 12477, "outcomeDate": null, "dischargeDate": null, "dateIncidentReported": "2022-12-16T16:23:38.253", "caseManagerUserAccountId": 11930, "outcomeType": "", "updatedDate": "2022-12-16T16:25:29.837", "claimStartDate": "2022-12-16T00:00:00", "ltdEffectiveDate": null, "caseManager": "<PERSON><PERSON><PERSON><PERSON>", "caseManagerEmail": "<EMAIL>", "caseManagerPhone": "", "outcomeTypeLocale": [], "workReadyDate": null, "caseManagerComment": "", "dateCaseComment": null, "notificationCompletedDate": "2022-12-16T00:00:00", "claimAge": 0, "claimAgeLocale": [], "businessUnit": null, "primaryContact": null, "reoccurrenceDate": null, "leaveAbsenceType": "continuous", "leaveAbsenceTypeLocale": [{"locale": "EN", "description": "Continuous", "shortDescription": ""}, {"locale": "FR", "description": "<PERSON><PERSON> continu", "shortDescription": ""}], "leaveRequestDate": "2022-12-16T16:21:57", "leaveStartDate": "2022-12-27T00:00:00", "leaveEndDate": "2023-01-04T00:00:00", "certificationDueDate": null, "certificationStartDate": null, "certificationEndDate": null, "questionResponses": {"PS_dateStart": "12/27/2022 00:00:00", "PS_dateOfRequest": "2022-12-16", "PS_dateEnd": "01/04/2023 00:00:00", "PS_relationship": "self", "PS_usedAccruedTimeOff": "false", "PS_victimViolence": "true"}, "totalAbsenceHours": 56, "incidentReportedState": "BC", "absenceCategory": "continuous", "overallExhaustionDate": "2023-06-10T00:00:00", "jobProtectionExhaustionDate": "2023-06-10T00:00:00", "bestPhoneNumber": "**********", "unpaidTime": 0, "conditionCategory": null, "seriousHealthCondition": null, "conditionCategoryLocale": [], "seriousHealthConditionLocale": [], "leaveIncidentAbsenceDates": [{"date": "2022-12-27T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-12-28T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-12-29T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-12-30T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2022-12-31T00:00:00-05:00", "absenceMinutes": 0, "workMinutes": 0, "isFullDate": true, "approved": null}, {"date": "2023-01-01T00:00:00-05:00", "absenceMinutes": 0, "workMinutes": 0, "isFullDate": true, "approved": null}, {"date": "2023-01-02T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2023-01-03T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}, {"date": "2023-01-04T00:00:00-05:00", "absenceMinutes": 480, "workMinutes": 480, "isFullDate": true, "approved": null}], "caseTypeAlias": "Leave Case", "caseTypeAliasLocale": [{"locale": "EN", "description": "Regulated Leave Case", "shortDescription": ""}], "leaveStatus": "PENDING", "leaveStatusLocale": [{"locale": "EN", "description": "Pending", "shortDescription": "Pending"}, {"locale": "FR", "description": "En attente", "shortDescription": "En attente"}], "associatedAbsenceId": 666, "associatedSTDCaseId": 0, "primaryReasonOfAbsenceId": "f862ce45-a4ff-4aa2-8c47-b112235cf61c", "secondaryReasonOfAbsenceId": "ace1e051-e35f-4074-9729-2925414d6a68", "primaryReasonOfAbsenceLocale": [{"locale": "EN", "description": "My own illness or injury", "shortDescription": ""}, {"locale": "FR", "description": "Ma propre maladie ou blessure", "shortDescription": ""}], "secondaryReasonOfAbsenceLocale": [{"locale": "EN", "description": "I have an injury or illness that occurred outside of work", "shortDescription": ""}, {"locale": "FR", "description": "J’ai une blessure ou une maladie qui est survenue en dehors du travail", "shortDescription": ""}], "employeeClaimsListData": {}, "adhocClaimsListData": {}, "incidentTypeCode": 5, "incidentFrom": "Vistamed", "claimNumber": "299385", "caseType": "LEAVE", "caseState": "ACTIVE", "claimType": "Continuous", "incidentReportedDate": "2022-12-16T16:23:38.253", "incidentId": 299385, "createdDate": "2022-12-16T16:23:38.253", "claimStatus": "Open", "claimStatusLocale": [{"locale": "EN", "description": "Open", "shortDescription": ""}, {"locale": "FR", "description": "Ouvert", "shortDescription": ""}], "claimTypeLocale": [{"locale": "EN", "description": "Continuous", "shortDescription": ""}, {"locale": "FR", "description": "PHASE1", "shortDescription": ""}], "caseCancelled": 0, "clientCode": "STARBUCKS", "companyId": 118, "expectedRTWDate": null, "firstDateOfAbsence": "2022-12-27T00:00:00", "dateOfIncident": null, "lastDateWorked": null, "caseTypeGroupType": "RL", "planCaseTypeShortCode": "Leave-Case", "caseEmploymentType": "Full time Sal<PERSON>", "caseEmploymentTypeLocale": [{"locale": "EN", "description": "Full time Sal<PERSON>", "shortDescription": ""}, {"locale": "FR", "description": "Salarié à temps plein", "shortDescription": ""}], "employee": {"employeeId": 586165, "organization": {"organizationId": 166537, "orgLevel1": "STARBUCKS", "orgLevel2": "026", "orgLevel3": "0264", "orgLevel4": "20831"}, "employeeNumber": "00262843", "employeeNumberPadded": "000000000262843", "employeeFirstName": "TARA", "employeeLastName": "SMITH", "employeeName": "TARA SMITH", "union": "", "emP_BusinessUnit": null, "emP_CostCentre": null, "emP_WorkCentreName_Description": null, "isOtherIncidentsRequired": null, "supervisorFirstName": "<PERSON>", "supervisorLastName": "Kearney", "supervisorName": "<PERSON>", "employeeGender": "F", "employeeGenderLocale": [{"locale": "EN", "description": "Female", "shortDescription": ""}, {"locale": "FR", "description": "<PERSON>mme", "shortDescription": ""}], "birthDate": "1979-03-09T00:00:00", "dateOfHire": "2018-02-26T00:00:00", "jobTitle": "asst manager", "province": "BC", "emP_Department": "20831", "age": 43, "yearsOfService": 4, "yearsOfServiceValue": "4", "employeeSource": null}, "dateFormatUsed": null, "incidentTypeCssClass": "INCIDENT_GENERIC_LEAVE", "status": "1", "phaseType": "PHASE1", "claimDates": [{"startDate": "2022-12-27T09:00:00-05:00", "endDate": "2022-12-27T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-28T09:00:00-05:00", "endDate": "2022-12-28T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-29T09:00:00-05:00", "endDate": "2022-12-29T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-30T09:00:00-05:00", "endDate": "2022-12-30T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2022-12-31T09:00:00-05:00", "endDate": "2022-12-31T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2023-01-01T09:00:00-05:00", "endDate": "2023-01-01T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2023-01-02T09:00:00-05:00", "endDate": "2023-01-02T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2023-01-03T09:00:00-05:00", "endDate": "2023-01-03T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}, {"startDate": "2023-01-04T09:00:00-05:00", "endDate": "2023-01-04T17:00:00-05:00", "isPartialAbsence": null, "unpaidTimeInMinutes": null, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null}], "relationShip": "self", "relationShipLocale": [{"locale": "EN", "description": "Self", "shortDescription": ""}, {"locale": "FR", "description": "Soi", "shortDescription": ""}], "policies": [], "leaveTypeCode": "victimOfViolentCrime", "leaveTypeLocale": [{"locale": "EN", "description": "Victim of Violent Crime", "shortDescription": ""}, {"locale": "FR", "description": "Victim of Violent Crime", "shortDescription": ""}]}], "totalRecords": 4, "timeTakenToQuery": 22}}], "GET": [{"endpoint": "Employee/80083033/esEmployeeByNumber?clientCode=GOODYEAR", "calledWith": {"config": {"headers": {"Authorization": "Bearer ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "params": {"clientCode": "GOODYEAR"}}}, "data": {"recordFrom": "Vistamed", "employeeId": 587196, "organizationId": 166459, "organizationName": "026", "salutation": "", "employeeNumber": "80083033", "firstName": "<PERSON><PERSON><PERSON>", "middleInitial": "", "lastName": "Regression Qa", "gender": "M", "language": "EN", "dateOfHire": "2007-01-30T00:00:00", "employeeWorkStatusId": 0, "employmentType": "FULLTIME", "jobTitle": "TEST", "phoneNumber": "7656758585", "altPhoneNumber": "7656758585", "workPhoneNumber": "", "workAltPhoneNumber": "", "createdBy": "madhulika.petluri", "createDateTime": "2023-01-11T20:30:22.31", "updatedBy": "madhulika.petluri", "updateDateTime": "2023-01-11T20:30:30.277", "payRateFrequency": "", "provinceCode": "ON", "supervisorId": 0, "businessEmail": "<EMAIL>", "homeEmail": "<EMAIL>", "birthDate": "1980-01-01T00:00:00", "isSearchable": true, "wageUnit": "", "emP_Department": "", "supervisorEmployeeNumber": "", "supervisorEmail": "", "supervisorFirstName": "", "supervisorLastName": "", "union": "", "supervisorAREmail": "", "supervisorWCBEmail": "", "supervisorSTDEmail": "", "employeeNumberPadded": "00000000080083033", "companyId": 118, "clientCode": "STARBUCKS", "employeeCustomFields": [], "employeeAddresses": [], "homeAddressLine1": null, "homeAddressLine2": null, "homePostalCode": null, "homeCountry": null, "homeProvinceCode": null, "workAddressLine1": null, "workAddressLine2": null, "workPostalCode": null, "workCountry": null, "workProvinceCode": null, "employmentTypeDescription": null, "supervisorName": null, "hash": null}}], "PUT": [{"endpoint": "12345/submit", "calledWith": {"config": {"headers": {"Authorization": "Bearer ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "params": {"AbsenceType": "continuous", "ReasonConfig": {"ReportableDaysInFuture": 60, "ReportableDaysInPast": 2, "MaxDaysAllowed": 120}, "QuestionResponses": {}, "SecondaryReason": "5818e815-3466-470c-87f2-2258b5a7ef80", "FirstName": "<PERSON><PERSON><PERSON>", "NextScheduledShift": null, "EmployeeNumber": "80083033", "BestPhoneNumber": null, "AbsenceIncidentId": 22735, "RequestDate": "2023-05-03T18:24:58.6524815Z", "MaxReportableDays": 120, "LinkedIncident": null, "PrimaryReason": "72bd21d7-efc8-45ba-bf23-1fea48fa002b", "ClaimStatus": "I", "MaxDaysAllowed": 120, "ClientCode": "GOODYEAR", "LastName": "<PERSON>", "ProvinceCode": "AR", "AbsenceDates": [{"StartDate": "2023-05-05 08:00:00", "ShiftDuration": null, "ShiftEndTime": "2023-05-05 17:00:00", "ShiftStartTime": "2023-05-05 08:00:00", "IsUpdated": false, "EndDate": "2023-05-05 17:00:00", "IncidentId": 22735}], "ReturnToWorkDate": "2023-05-06 00:00:00", "ReportedBy": "Employee"}}}, "data": {"returnValue": {"absenceIncidentModel": {"absenceIncidentId": 22735, "employeeNumber": "80083033", "clientCode": "GOODYEAR", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "bestPhoneNumber": null, "provinceCode": "AR", "returnToWorkDate": "2023-05-06T00:00:00", "nextScheduledShift": null, "requestDate": "2023-05-03T18:24:58.6524815Z", "primaryReason": "72bd21d7-efc8-45ba-bf23-1fea48fa002b", "secondaryReason": "5818e815-3466-470c-87f2-2258b5a7ef80", "absenceType": "continuous", "reportedBy": "Employee", "absenceDates": [{"shiftStartTime": "2023-05-05T08:00:00-04:00", "shiftEndTime": "2023-05-05T17:00:00-04:00", "shiftDuration": null, "isPartialAbsence": false, "scheduledShiftStartTime": null, "scheduledShiftEndTime": null, "unpaidTimeInMinutes": null}], "questionResponses": {}, "linkedIncident": null, "claimStatus": "S", "maxDaysAllowed": 120, "maxReportableDays": 120, "attachments": []}, "closingScripts": [{"closingScriptId": "9bfbf641-1626-48d3-a9aa-be44db4b84fc", "text": [{"locale": "EN", "description": "<p>Thank you for reporting absence.&nbsp;</p>\n", "shortDescription": ""}], "redirectURL": null, "displayOptionType": null}]}, "validationErrors": [], "success": true}}]}, "configApiEndpoint": {"GET": [{"endpoint": "AbsenceConfig/goodyear?state=US:MI", "calledWith": {"config": {"headers": {"Authorization": "Bearer ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "params": {"clientCode": "GOODYEAR"}}}, "data": {"absenceReportingConfigId": "33a4248d-4fbe-4795-90a3-be0cd8282421", "clientCode": "GOODYEAR", "refChannelId": "00000000-0000-0000-0000-000000000000", "clientCareRepresentativeCanReport": true, "employeeCanReport": true, "peopleLeaderCanReport": false, "reportingRoles": ["1be5e183-b28a-49d3-931d-d0e12fe5ae51", "f4320557-bcfd-4c5e-a9bb-6ac9fd39807a"], "isPartialAbsences": true, "isLateReporting": false, "lateReportingThresholdHours": null, "minimumThresholdForContinousAbsence": 1, "lateReportingThresholdMessages": [], "isIntermittentAbsenceIncidentCountResetEnabled": false, "isCheckStatusOfIACase": false, "displayRTWDate_Intermittent": true, "isRTWDateMandatory_Intermittent": false, "displayRTWDate_Continous": true, "isRTWDateMandatory_Continous": false, "displayRTWDate_ReducedHours": true, "isRTWDateMandatory_ReducedHours": false, "isReferralOrderOfOperationsEnabled": false, "absenceDurationMinutes": 15, "timeZoneEntityType": null, "timeZoneClientUtcOffset": null, "timeZone": "US Eastern Standard Time", "reasonOfAbsence": {"questionId": "9147fa48-e0af-4465-929e-295d47bfe072", "questionName": null, "questionDescription": null, "locales": [{"locale": "EN", "description": "Please enter your reason for absence"}], "manager": [{"locale": "EN", "description": "Please enter your reason for absence"}], "employee": [{"locale": "EN", "description": "Please enter your reason for absence"}], "familyMember": [{"locale": "EN", "description": "Please enter your reason for absence"}], "answers": [{"answerId": "72bd21d7-efc8-45ba-bf23-1fea48fa002b", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Illness or Injury (not FMLA)"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": 60, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "4e3de78f-9748-4686-a054-7a640068d576", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": null, "reportableDaysInPast": null, "subAnswers": [{"answerId": "5818e815-3466-470c-87f2-2258b5a7ef80", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Self"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "4e3de78f-9748-4686-a054-7a640068d576", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Self - updated"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-sick-injured", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-sick-injured-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-sick-injured-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "516e4f3e-69d5-4c09-8166-f878d86eafbd", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Family Member"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "4e3de78f-9748-4686-a054-7a640068d576", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Family Member - updated"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-sick-injured", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-sick-injured-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-sick-injured-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Test Illness or Injury (not FMLA - updated)"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-sick-injured", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-sick-injured-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-sick-injured-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "d31f99ac-deb9-441b-be53-faf4c1ccba00", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "STD, Accident & Sickness or FMLA Absence"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": 60, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "5777c594-fc77-4a09-96df-edbe66ab731a", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": null, "reportableDaysInPast": null, "subAnswers": [{"answerId": "5b6df012-c1d0-4238-a639-5ff62bbff25c", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "New Claim"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "5777c594-fc77-4a09-96df-edbe66ab731a", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": true, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "New Claim"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-other", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "a60cc0b5-e69e-4909-8b72-3fbd4fdaf9a8", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Associated with an existing intermittent claim"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "5777c594-fc77-4a09-96df-edbe66ab731a", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": true, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Associated with an existing intermittent claim"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-other", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "56e98d28-ec7f-42de-b918-b0603fae149a", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Associated with a continuous existing claim"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "5777c594-fc77-4a09-96df-edbe66ab731a", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": true, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Associated with a continuous existing claim"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-other", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": true, "isSpecialMenu": true, "locales": [{"locale": "EN", "description": "STD/Accident & Sickness or FMLA Absence"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-other", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "c0e86cff-484c-4191-948c-49470d53c0a8", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Personal Reasons"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": 60, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "5777c594-fc77-4a09-96df-edbe66ab731a", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": null, "reportableDaysInPast": null, "subAnswers": [{"answerId": "35a2b078-1cc1-44ef-9dd4-34c208900081", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Full Shift"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "5777c594-fc77-4a09-96df-edbe66ab731a", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Full Shift"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-other", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "ceae96bc-383b-427e-96bc-d59bf762e216", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Arrive Late"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "5777c594-fc77-4a09-96df-edbe66ab731a", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Arrive Late"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-other", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "19037f01-a53f-42ca-aeb3-b5d1928dbd59", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Leave Early"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "5777c594-fc77-4a09-96df-edbe66ab731a", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Leave Early"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-other", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Personal Reasons"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-other", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-other-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "5d5548c1-adad-417e-92cf-cc85ae6acb6e", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Bereavement"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": 60, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "408ec1c5-9486-464c-9d7f-0648073cfa99", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": null, "reportableDaysInPast": null, "subAnswers": [{"answerId": "e93333ee-e0f6-46a6-b341-a0394f9b43d1", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Immediate Family"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "408ec1c5-9486-464c-9d7f-0648073cfa99", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Immediate Family"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-bereavement", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-bereavement-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-bereavement-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "943cf946-6559-4d46-a069-8b8232f9dbfd", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Non-Immediate Family"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "408ec1c5-9486-464c-9d7f-0648073cfa99", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Non-Immediate Family"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-bereavement", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-bereavement-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-bereavement-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Bereavement"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-bereavement", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-bereavement-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-bereavement-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "1f477ad2-fffc-4b6b-a42e-dc1f3c6142cf", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Jury Duty"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": 60, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "51b4fa3d-7bb7-4ee3-9043-66aee88ad00b", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": null, "reportableDaysInPast": null, "subAnswers": [{"answerId": "8a746e46-7365-4318-944d-0c17a71bd546", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Jury Duty"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "51b4fa3d-7bb7-4ee3-9043-66aee88ad00b", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Jury Duty"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-jury-duty", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-jury-duty-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-jury-duty-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": true, "locales": [{"locale": "EN", "description": "Jury Duty"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-jury-duty", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-jury-duty-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-jury-duty-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "8f7b0b9d-e996-43ba-a477-b09918dcb870", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Caring for or bonding with a new Child"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": 60, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "daf2caad-eb88-4c57-9ad2-ce2fbf90822e", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": null, "reportableDaysInPast": null, "subAnswers": [{"answerId": "37027f5b-e327-40d5-82d5-b35a3ae0a9cb", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Caring for or bonding with a new Child"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "daf2caad-eb88-4c57-9ad2-ce2fbf90822e", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": true, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Caring for or bonding with a new Child"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-baby", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-baby-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-baby-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": true, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Caring for or bonding with a new Child"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-baby", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-baby-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-baby-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "b08f943b-3ea5-4390-aab9-23405d901aca", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Military Leave"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": 60, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "70432e28-473e-4654-84d4-28d806596e60", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": null, "reportableDaysInPast": null, "subAnswers": [{"answerId": "82db97c9-f027-4c2d-b4e8-c71a268cd600", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Non-FMLA Military Leave"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "70432e28-473e-4654-84d4-28d806596e60", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Military Leave"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-military", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-military-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-military-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "ba7f935d-4560-49cc-86f9-6f5e5e1fde99", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Military FMLA"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "70432e28-473e-4654-84d4-28d806596e60", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": true, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Military FMLA"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-military", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-military-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-military-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Military Leave"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-military", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-military-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-military-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}, {"answerId": "********-c96c-438d-98dc-acd0c991724a", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Weather Conditions"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": 60, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "16575cc1-30a6-4662-b9e7-24823c7a248e", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": null, "reportableDaysInPast": null, "subAnswers": [{"answerId": "b29f9af9-d4b8-46be-a64f-6e773d5ce912", "answerCode": null, "isBereavement": false, "isIllness": false, "isNoneOfTheAbove": false, "locales": [{"locale": "EN", "description": "Weather Conditions"}], "order": 0, "ivrShouldTransferToCsr": false, "parentAnswerId": null, "maxDaysAllowed": null, "ivrShortAudioIds": [], "ivrLongAudioIds": [], "invalidMsgs": [], "ivrInvalidMsgAudioIds": [], "associatedAnswerId": null, "iconResourceId": "16575cc1-30a6-4662-b9e7-24823c7a248e", "ivrInvalidTransferNumbers": [], "ptoBankId": null, "isTriggerTransfer": false, "nofOfAbsence": 3, "transferPhoneId": null, "higTransferOverrides": null, "transferOverrides": [], "nofOfSTDAbsence": null, "reportableDaysInFuture": 60, "reportableDaysInPast": 3, "subAnswers": [], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Weather Conditions"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-weather", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-weather-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-weather-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}], "requestLeaveTypes": [], "relatedLeaveTypes": [], "ar3IvrReasonSettings": {"isTransfer": false, "isSpecialMenu": false, "locales": [{"locale": "EN", "description": "Weather Conditions"}]}, "iconResource": {"resourceType": "MYABILITI_ROA_ICONS", "resourceName": "illu-weather", "fileName": null, "mimeType": "image/svg", "source": "AZURECDN", "sourceUrl": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-weather-mobile.svg", "metadata": {"additionalURLs": {"desktop2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "desktop3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/illu-weather-mobile.png", "mobile2x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>", "mobile3x": "https://hps-abiliti-cdn-qa.azureedge.net/abiliti-resources/config-api/assets/icons/roa/<EMAIL>"}}}}]}, "eeInformationScripts": [], "workShifts": [{"shiftDescription": [{"locale": "EN", "description": "9-5"}], "shiftHours": 8.0, "order": 0, "startTimeHours": 9, "startTimeMinutes": 0, "endTimeHours": 17, "endTimeMinutes": 0, "isDefault": true}], "workScheduleInfo": {"isClientWorkScheduleProvided": null, "isSuppliedAsDuration": null, "isDurationAsTime": null, "isDurationAsDecimal": null, "isSuppliedAsTime": null, "startNoOfDays": 60, "endNoOfDays": 120, "isDurationValidation": null, "isTimeValidation": null, "warnWhenOverridingSchedule": false, "warningMessages": [{"locale": "EN", "description": null}]}, "preQualifyingQuestions": [], "companySpecificQuestions": [{"questionId": "bf36d86c-5103-46c1-afab-b52c067155d9", "questionName": "Warning", "questionDescription": null, "order": 1, "answerMaxLength": null, "isRequired": true, "ivrAudioIds": {}, "isActive": null, "pythonScript": "{MyAbiltiAR.PrimaryReasonEn} == 'Illness or Injury (not FMLA)' and ({MyAbiltiAR.SecondaryReasonEn} == 'Self'  or {MyAbiltiAR.SecondaryReasonEn} == 'Family Member' )", "pythonScriptDescription": null, "isCustomWrittenScript": true, "questionStyle": "dropdown", "defaultValue": null, "longDescription": [], "locales": [{"locale": "EN", "description": "Do you think your illness is contagious?"}, {"locale": "FR", "description": "Do you think your illness is contagious? FR"}], "manager": [], "employee": [], "familyMember": [], "answers": [{"answerId": "f843d8bd-5d6b-4918-87f9-0f954ebdeb32", "answerValue": "true", "isValid": false, "locales": [{"locale": "EN", "description": "Yes"}, {"locale": "FR", "description": "O<PERSON>"}], "invalidMsgs": [{"locale": "EN", "description": null}, {"locale": "FR", "description": null}], "order": 0, "computedAnswerId": null}, {"answerId": "c0df43d4-92cb-476e-8ca0-e28563be14de", "answerValue": "false", "isValid": false, "locales": [{"locale": "EN", "description": "No"}, {"locale": "FR", "description": "Non"}], "invalidMsgs": [{"locale": "EN", "description": null}, {"locale": "FR", "description": null}], "order": 0, "computedAnswerId": null}], "leaveTypes": [], "linkedPresagiaQuestion": null, "defaultConfigurationData": null, "displayOnlyWhenRequestingALeave": false}], "fixedQuestions": [{"questionId": "0e8b78ab-c295-433c-b637-4491ac0b8b95", "questionName": "BEST_PHONE_NUMBER_TO_BE_REACHED", "questionDescription": "Confirm preferred phone number", "order": 0, "answerMaxLength": null, "isRequired": false, "ivrAudioIds": {}, "isActive": false, "pythonScript": null, "pythonScriptDescription": null, "isCustomWrittenScript": null, "questionStyle": "text", "defaultValue": null, "longDescription": [], "locales": [{"locale": "EN", "description": "What is the best phone number to reach you at?"}], "manager": [], "employee": [], "familyMember": [], "answers": [], "leaveTypes": [], "linkedPresagiaQuestion": null, "defaultConfigurationData": null, "displayOnlyWhenRequestingALeave": false}, {"questionId": "88597a60-3673-4266-87f8-548992642f54", "questionName": "RECEIVE_EMAILS", "questionDescription": "Would you prefer to receive emails?", "order": 0, "answerMaxLength": null, "isRequired": false, "ivrAudioIds": {}, "isActive": false, "pythonScript": null, "pythonScriptDescription": null, "isCustomWrittenScript": null, "questionStyle": "boolean", "defaultValue": null, "longDescription": [], "locales": [{"locale": "EN", "description": "Do you want to receive an email confirmation of your submission?"}], "manager": [], "employee": [], "familyMember": [], "answers": [], "leaveTypes": [], "linkedPresagiaQuestion": null, "defaultConfigurationData": null, "displayOnlyWhenRequestingALeave": false}, {"questionId": "da14d814-c5a9-412a-a4e3-faf8e408edb2", "questionName": "EMAIL_ADDRESS_TO_RECEIVE", "questionDescription": "Receive emails preference question - Email address", "order": 0, "answerMaxLength": null, "isRequired": false, "ivrAudioIds": {}, "isActive": false, "pythonScript": null, "pythonScriptDescription": null, "isCustomWrittenScript": null, "questionStyle": "email", "defaultValue": null, "longDescription": [], "locales": [{"locale": "EN", "description": "Which email address would you prefer to be reached?"}], "manager": [], "employee": [], "familyMember": [], "answers": [], "leaveTypes": [], "linkedPresagiaQuestion": null, "defaultConfigurationData": null, "displayOnlyWhenRequestingALeave": false}, {"questionId": "ec059b4c-8753-4780-abb7-a6b5a6c2d335", "questionName": "APPLY_FOR_STD_REFERRAL", "questionDescription": "Ask STD application question", "order": 0, "answerMaxLength": null, "isRequired": true, "ivrAudioIds": {}, "isActive": false, "pythonScript": null, "pythonScriptDescription": null, "isCustomWrittenScript": null, "questionStyle": "boolean", "defaultValue": null, "longDescription": [], "locales": [{"locale": "EN", "description": "Do you want to apply for STD Referral ?"}], "manager": [], "employee": [], "familyMember": [], "answers": [], "leaveTypes": [], "linkedPresagiaQuestion": null, "defaultConfigurationData": null, "displayOnlyWhenRequestingALeave": false}, {"questionId": "581f5278-a78e-4b1d-aa2e-9fd3499cc350", "questionName": "APPLY_FOR_LEAVE_REFERRAL", "questionDescription": "Ask Leaves application question", "order": 0, "answerMaxLength": null, "isRequired": true, "ivrAudioIds": {}, "isActive": false, "pythonScript": null, "pythonScriptDescription": null, "isCustomWrittenScript": null, "questionStyle": "boolean", "defaultValue": null, "longDescription": [], "locales": [{"locale": "EN", "description": "Do you want to apply for Leave Referral ?"}], "manager": [], "employee": [], "familyMember": [], "answers": [], "leaveTypes": [], "linkedPresagiaQuestion": null, "defaultConfigurationData": null, "displayOnlyWhenRequestingALeave": false}, {"questionId": "dd9f1ba3-955e-49f6-a15f-df73b4ef4821", "questionName": "ASK_FOR_EXISTING_LEAVE_CASE", "questionDescription": "Ask if this incident is associated with any of the qualified open leave case.", "order": 0, "answerMaxLength": null, "isRequired": true, "ivrAudioIds": {}, "isActive": false, "pythonScript": null, "pythonScriptDescription": null, "isCustomWrittenScript": null, "questionStyle": "dropdown", "defaultValue": null, "longDescription": [], "locales": [{"locale": "EN", "description": "Do you want to associate this incident with any of open Leave Case ?"}], "manager": [], "employee": [], "familyMember": [], "answers": [], "leaveTypes": [], "linkedPresagiaQuestion": null, "defaultConfigurationData": null, "displayOnlyWhenRequestingALeave": false}, {"questionId": "6b38adfd-9655-4b89-bd81-776f11c4f867", "questionName": "SUPPRESS_EMAIL", "questionDescription": "Would you like to suppress the email for this incident ?", "order": 0, "answerMaxLength": null, "isRequired": true, "ivrAudioIds": {}, "isActive": false, "pythonScript": null, "pythonScriptDescription": null, "isCustomWrittenScript": null, "questionStyle": "boolean", "defaultValue": null, "longDescription": [], "locales": [{"locale": "EN", "description": "Would you like to suppress the email for this incident ?"}], "manager": [], "employee": [], "familyMember": [], "answers": [], "leaveTypes": [], "linkedPresagiaQuestion": null, "defaultConfigurationData": null, "displayOnlyWhenRequestingALeave": false}, {"questionId": "abb0d2cd-0c63-4034-a949-5902b32d5635", "questionName": "ADDITIONAL_COMMENT_EMAIL_BODY", "questionDescription": "Additional comments to be inserted in the email body.", "order": 0, "answerMaxLength": 250, "isRequired": true, "ivrAudioIds": {}, "isActive": false, "pythonScript": null, "pythonScriptDescription": null, "isCustomWrittenScript": null, "questionStyle": "textArea", "defaultValue": null, "longDescription": [], "locales": [{"locale": "EN", "description": "Additional comments to be inserted in the email body."}], "manager": [], "employee": [], "familyMember": [], "answers": [], "leaveTypes": [], "linkedPresagiaQuestion": null, "defaultConfigurationData": null, "displayOnlyWhenRequestingALeave": false}], "employmentDetailsQuestions": [], "alerts": [{"alertid": "f7982d1d-8048-42c3-bcef-bc5bace001fb", "questionTypeId": "ReasonOfAbsence", "isTemporary": false, "startDate": null, "endDate": null, "messages": [{"locale": "EN", "description": "<p>If you or your family member is hospitalized or you expect your absence to be greater than 3 days due to an illness or injury,&nbsp; you will need go back and then select option 2.&nbsp; Once you have reported your absence(s), you are required to call UNUM at ************** or visit www.unum.com to provide additional information.&nbsp;</p>\n"}], "pythonScript": "{MyAbilitiAR.AbsenceSource} != 'IV' ", "pythonScriptDescription": null, "isCustomWrittenScript": null, "audioId": null, "questionId": "ROA", "triggerAnswer": "72bd21d7-efc8-45ba-bf23-1fea48fa002b", "operator": "==", "appliesTo": ["Manager", "Employee", "Leave Administrator"]}, {"alertid": "8287d04d-9720-491b-8669-c50fdd170e2f", "questionTypeId": "ReasonOfAbsence", "isTemporary": false, "startDate": null, "endDate": null, "messages": [{"locale": "EN", "description": "<p>Since you have reported an absence related to Short-Term Disability/Accident &amp; Sickness or FMLA, you are required to call UNUM at ************** or visit www.unum.com to provide additional information.</p>\n"}], "pythonScript": "{MyAbilitiAR.AbsenceSource} != 'IV' ", "pythonScriptDescription": null, "isCustomWrittenScript": null, "audioId": null, "questionId": "ROA", "triggerAnswer": "d31f99ac-deb9-441b-be53-faf4c1ccba00", "operator": "==", "appliesTo": ["Manager", "Employee", "Leave Administrator"]}, {"alertid": "0db9b971-a421-45c6-91e1-79d985289a5b", "questionTypeId": "ReasonOfAbsence", "isTemporary": false, "startDate": null, "endDate": null, "messages": [{"locale": "EN", "description": "<p>If you will be off work for more than 3 days due to a serious health condition, or if you require ongoing intermittent absences due to a serious health condition, you are required to call UNUM at ************** or visit www.unum.com to see if you are eligible and qualified for Family and Medical Leave or STD/Accident &amp; Sickness.</p>\n"}], "pythonScript": "{MyAbilitiAR.AbsenceSource} != 'IV' ", "pythonScriptDescription": null, "isCustomWrittenScript": null, "audioId": null, "questionId": "ROA", "triggerAnswer": "c0e86cff-484c-4191-948c-49470d53c0a8", "operator": "==", "appliesTo": ["Manager", "Employee", "Leave Administrator"]}, {"alertid": "316b3b2d-ed99-44b3-9e2d-937274a09691", "questionTypeId": "ReasonOfAbsence", "isTemporary": false, "startDate": null, "endDate": null, "messages": [{"locale": "EN", "description": "<p>Please provide required documentation to department upon your return.</p>\n"}], "pythonScript": null, "pythonScriptDescription": null, "isCustomWrittenScript": null, "audioId": null, "questionId": "ROA", "triggerAnswer": "5d5548c1-adad-417e-92cf-cc85ae6acb6e", "operator": "==", "appliesTo": ["Manager", "Employee", "Leave Administrator"]}, {"alertid": "e256cb01-6e58-4d0e-adaf-4abd60aeeeb8", "questionTypeId": "ReasonOfAbsence", "isTemporary": false, "startDate": null, "endDate": null, "messages": [{"locale": "EN", "description": "<p>Please provide required documentation to department upon your return.</p>\n"}], "pythonScript": null, "pythonScriptDescription": null, "isCustomWrittenScript": null, "audioId": null, "questionId": "ROA", "triggerAnswer": "1f477ad2-fffc-4b6b-a42e-dc1f3c6142cf", "operator": "==", "appliesTo": ["Manager", "Employee", "Leave Administrator"]}, {"alertid": "afa19c2e-6d72-4e82-a875-d3f61f3215d1", "questionTypeId": "ReasonOfAbsence", "isTemporary": false, "startDate": null, "endDate": null, "messages": [{"locale": "EN", "description": "<p>Since you have reported a parental leave, you are required to call UNUM at ************** or visit www.unum.com to request your leave.&nbsp;</p>\n"}], "pythonScript": "{MyAbilitiAR.AbsenceSource} != 'IVR' ", "pythonScriptDescription": null, "isCustomWrittenScript": null, "audioId": null, "questionId": "ROA", "triggerAnswer": "8f7b0b9d-e996-43ba-a477-b09918dcb870", "operator": "==", "appliesTo": ["Manager", "Employee", "Leave Administrator"]}, {"alertid": "18d1f25b-e83d-4412-a2eb-0da289786c26", "questionTypeId": "ReasonOfAbsence", "isTemporary": false, "startDate": null, "endDate": null, "messages": [{"locale": "EN", "description": "<p>Please contact HR following the reporting of this absence.</p>\n"}], "pythonScript": null, "pythonScriptDescription": null, "isCustomWrittenScript": null, "audioId": null, "questionId": "ROA", "triggerAnswer": "82db97c9-f027-4c2d-b4e8-c71a268cd600", "operator": "==", "appliesTo": ["Manager", "Employee", "Leave Administrator"]}, {"alertid": "3fbdf7b1-2d3f-4f0b-b78b-aeaeee1bdf4b", "questionTypeId": "ReasonOfAbsence", "isTemporary": false, "startDate": null, "endDate": null, "messages": [{"locale": "EN", "description": "<p>Since you have reported an absence(s) related to an FMLA military leave, you are required to call UNUM at ************** or visit www.unum.com to request&nbsp; your leave.</p>\n"}], "pythonScript": "{MyAbilitiAR.AbsenceSource} != 'IVR' ", "pythonScriptDescription": null, "isCustomWrittenScript": null, "audioId": null, "questionId": "ROA", "triggerAnswer": "ba7f935d-4560-49cc-86f9-6f5e5e1fde99", "operator": "==", "appliesTo": ["Manager", "Employee", "Leave Administrator"]}, {"alertid": "aa1a678b-a309-4389-84ed-6b05d41415d6", "questionTypeId": "ReasonOfAbsence", "isTemporary": false, "startDate": null, "endDate": null, "messages": [{"locale": "EN", "description": "<p>Since you have reported an absence related to Short-Term Disability, Accident &amp; Sickness or FMLA, after you have reported your absence dates, you are required to stay on the line to be transferred to UNUM to provide additional information to request approval for your leave.</p>\n"}], "pythonScript": "{MyAbilitiAR.AbsenceSource} == 'IVR' ", "pythonScriptDescription": null, "isCustomWrittenScript": null, "audioId": null, "questionId": "ROA", "triggerAnswer": "d31f99ac-deb9-441b-be53-faf4c1ccba00", "operator": "==", "appliesTo": ["Manager", "Employee", "Leave Administrator"]}, {"alertid": "4350ddb1-b334-4455-a979-6cbca61ce21f", "questionTypeId": "ReasonOfAbsence", "isTemporary": false, "startDate": null, "endDate": null, "messages": [{"locale": "EN", "description": "<p>Since you have reported a parental leave, after you have reported your absence dates, you are required to stay on the line to be transferred to UNUM to request your leave.</p>\n"}], "pythonScript": "{MyAbilitiAR.AbsenceSource} == 'IVR' ", "pythonScriptDescription": null, "isCustomWrittenScript": null, "audioId": null, "questionId": "ROA", "triggerAnswer": "8f7b0b9d-e996-43ba-a477-b09918dcb870", "operator": "==", "appliesTo": ["Manager", "Employee", "Leave Administrator"]}, {"alertid": "a2958d9f-b463-4b26-823e-da65fadbe871", "questionTypeId": "ReasonOfAbsence", "isTemporary": false, "startDate": null, "endDate": null, "messages": [{"locale": "EN", "description": "<p>Since you have reported an absence related to a FMLA military leave, after you have reported your absence dates, you are required to stay on the line to be transferred to UNUM to request your leave.</p>\n"}], "pythonScript": "{MyAbilitiAR.AbsenceSource} == 'IVR' ", "pythonScriptDescription": null, "isCustomWrittenScript": null, "audioId": null, "questionId": "ROA", "triggerAnswer": "ba7f935d-4560-49cc-86f9-6f5e5e1fde99", "operator": "==", "appliesTo": ["Manager", "Employee", "Leave Administrator"]}], "closingScripts": [{"isActive": false, "text": [{"locale": "EN", "description": "<p>Thank you for reporting your absence.</p>\n\n<p>You will now be transferred to UNUM.</p>\n\n<p>Your absence will be reported to your manager and will be reviewed for compliance with local policies and procedures.</p>\n\n<p>Please remember that if you have questions about applying for leave or about an open claim, please contact your manager or HR representative.</p>\n"}], "reasonOfAbsenceId": null, "redirectURL": null, "description": "Transfer Closing Script for STD", "displayOptionType": null, "pythonScript": "{MyAbiltiAR.PrimaryReasonEn} == 'STD/Accident & Sickness or FMLA Absence' and {MyAbiltiAR.PrimaryReasonEn} == 'Caring for or bonding with a new Child' and {MyAbiltiAR.SecondaryReasonEn} == 'Military FMLA' and {MyAbilitiAR.AbsenceSource} == 'IVR' ", "pythonScriptDescription": null, "isCustomWrittenScript": false, "appliesTo": null, "leaveTypes": []}, {"isActive": true, "text": [{"locale": "EN", "description": "<p>Thank you for reporting your absence.</p>\n\n<p>Your absence will be reported to your manager and will be reviewed for compliance with local policies and procedures.</p>\n\n<p>If you have any questions, please contact your manager or HR representative</p>\n"}], "reasonOfAbsenceId": null, "redirectURL": null, "description": "Non UNUM Transfer Closing Script - IVR", "displayOptionType": null, "pythonScript": "{MyAbiltiAR.PrimaryReasonEn} != 'STD, Accident & Sickness or FMLA Absence' and {MyAbiltiAR.PrimaryReasonEn} != 'Caring for or bonding with a new Child' and {MyAbiltiAR.SecondaryReasonEn} != 'Military FMLA' and {MyAbilitiAR.AbsenceSource} == 'IVR' ", "pythonScriptDescription": null, "isCustomWrittenScript": false, "appliesTo": null, "leaveTypes": []}, {"isActive": true, "text": [{"locale": "EN", "description": "<p>Thank you for reporting your absence.</p>\n\n<p>You will now be transferred to UNUM.</p>\n\n<p>Your absence will be reported to your manager and will be reviewed for compliance with local policies and procedures.</p>\n\n<p>Please remember that if you have questions about applying for leave or about an open claim, please contact your manager or HR representative.</p>\n"}], "reasonOfAbsenceId": "d31f99ac-deb9-441b-be53-faf4c1ccba00", "redirectURL": null, "description": "Closing Script  STD - IVR", "displayOptionType": null, "pythonScript": "{MyAbiltiAR.PrimaryReasonEn} == 'STD, Accident & Sickness or FMLA Absence' and {MyAbilitiAR.AbsenceSource} == 'IVR' ", "pythonScriptDescription": null, "isCustomWrittenScript": false, "appliesTo": null, "leaveTypes": []}, {"isActive": true, "text": [{"locale": "EN", "description": "<p>Thank you for reporting your absence.</p>\n\n<p>This is THE AR3.0 alert message for NON UNUM absence types</p>\n"}], "reasonOfAbsenceId": null, "redirectURL": null, "description": "Closing Script Non-UNUM AR3.0", "displayOptionType": null, "pythonScript": "{MyAbiltiAR.PrimaryReasonEn} != 'STD/Accident & Sickness or FMLA Absence' and {MyAbiltiAR.PrimaryReasonEn} != 'Caring for or bonding with a new Child' and {MyAbiltiAR.SecondaryReasonEn} != 'Military FMLA' and {MyAbilitiAR.AbsenceSource} != 'IVR' ", "pythonScriptDescription": null, "isCustomWrittenScript": false, "appliesTo": null, "leaveTypes": []}, {"isActive": true, "text": [{"locale": "EN", "description": "<p>Thank you for reporting your absence.</p>\n\n<p>You will now be transferred to UNUM.</p>\n\n<p>Your absence will be reported to your manager and will be reviewed for compliance with local policies and procedures.</p>\n\n<p>Please remember that if you have questions about applying for leave or about an open claim, please contact your manager or HR representative.</p>\n"}], "reasonOfAbsenceId": null, "redirectURL": null, "description": "Closing script UNUM - Caring - IVR", "displayOptionType": null, "pythonScript": "{MyAbiltiAR.PrimaryReasonEn} == 'Caring for or bonding with a new Child' and {MyAbilitiAR.AbsenceSource} == 'IVR' ", "pythonScriptDescription": null, "isCustomWrittenScript": false, "appliesTo": null, "leaveTypes": []}, {"isActive": true, "text": [{"locale": "EN", "description": "<p>Thank you for reporting your absence.</p>\n\n<p>You will now be transferred to UNUM.</p>\n\n<p>Your absence will be reported to your manager and will be reviewed for compliance with local policies and procedures.</p>\n\n<p>Please remember that if you have questions about applying for leave or about an open claim, please contact your manager or HR representative.</p>\n"}], "reasonOfAbsenceId": null, "redirectURL": null, "description": "Closing Script UNUM - Military FMLA - IVR", "displayOptionType": null, "pythonScript": "{MyAbiltiAR.PrimaryReasonEn} == 'Military Leave' and {MyAbiltiAR.SecondaryReasonEn} == 'Military FMLA' and {MyAbilitiAR.AbsenceSource} == 'IVR' ", "pythonScriptDescription": null, "isCustomWrittenScript": false, "appliesTo": null, "leaveTypes": []}], "notifications": [{"description": "Absence Notification UNUM", "notificationCaseType": null, "templateCode": "AbsenceNotificationUNUM", "from": "<EMAIL>", "isNotifyManager": false, "isNotifyEmployee": false, "isNotifyFromDemoFeed": true, "ccEmail": "<EMAIL>;sapna.gulraj<PERSON>@lifeworks.com", "bccEmail": null, "pythonScript": "{Employee.DataSource} != 'DEMOLOADER' ", "pythonScriptDescription": null, "ruleClassName": null, "order": null, "isActive": true, "isCustomWrittenScript": null}, {"description": "Absence Notification", "notificationCaseType": null, "templateCode": "AbsenceNotification", "from": "<EMAIL>", "isNotifyManager": false, "isNotifyEmployee": true, "isNotifyFromDemoFeed": false, "ccEmail": "<EMAIL>;sapna.gulraj<PERSON>@lifeworks.com;", "bccEmail": null, "pythonScript": null, "pythonScriptDescription": null, "ruleClassName": null, "order": null, "isActive": true, "isCustomWrittenScript": null}], "referrals": [], "ptoBanks": [], "leaveTypePTOBanks": [{"leaveCode": "GOODYEAR_assault", "name": "Assault", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_AZ_Victims_of_Juvenile_Offense-otherUS.AZ", "name": "AZ Victims of Juvenile Offense", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_bereavement", "name": "Morneau Shepell - bereavement", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_bloodDonor", "name": "Blood Donor", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_bone<PERSON><PERSON>row", "name": "<PERSON>", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_breastfeeding", "name": "Breastfeeding", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_civilAirPatrol", "name": "Civil Air Patrol", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_CO_Civil_Defense-otherUS.CO", "name": "CO Civil Defense", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_courtAttendance", "name": "Court Attendance", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_csdog", "name": "Care of Service Dog", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_CT_Homicide_Victim_Leave-otherUS.CT", "name": "CT Homicide Victim Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_CT_Paid_Sick_Leave-otherUS.CT", "name": "CT Paid Sick Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_CT_Personal_Protected_Leave-otherUS.CT", "name": "CT Personal Protected Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_CT_Victims_of_Violence/Court-otherUS.CT", "name": "CT Victims of Violence/Court", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_DE_Quarantine-otherUS.DE", "name": "DE Quarantine", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_domesticViolence", "name": "Domestic Violence", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_election", "name": "Election", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_emergencyDuty", "name": "Emergency Duty", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_emergencyEvacuation", "name": "Emergency Evacuation", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_emergencyTraining", "name": "Emergency Training", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_harassment", "name": "Harassment", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_IA_Board_Service_Leave-otherUS.IA", "name": "IA Board Service Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_IA_Elected_Official_Leave-otherUS.IA", "name": "IA Elected Official Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_IA_Veterans_Day_Leave-otherUS.IA", "name": "IA Verterans Day Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_IL_Witness_Leave-otherUS.IL", "name": "IL Witness Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_juryDuty", "name": "Jury Duty", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_jurySelection", "name": "Jury Selection", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_MA_Crime_Victims-otherUS.MA", "name": "MA Crime Victims", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_MA_Small_Necessities_Leave-otherParentingUS.MA", "name": "MA Small Necessities Leave - Other Parenting", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_MA_Small_Necessities_Leave-otherUS.MA", "name": "MA Small Necessities Leave - Other", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_mchild", "name": "Missing Child", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_MD_Crime_Victims_Leave-otherUS.MD", "name": "MD Crime Victims Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_ME_Elected_Official_Leave-otherUS.ME", "name": "ME Elected Official Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_ME_Quarantine-otherUS.ME", "name": "ME Quarantine", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_medicalNonOccupational", "name": "Medical Non-Occupational", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_medicalOccupational", "name": "Medical Occupational", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_Miami-Dade_County_Domestic_Leave-otherParentingUS.FL", "name": "Miami-Dade County Domestic Leave - Other Parenting", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_Miami-Dade_County_Domestic_Leave-otherUS.FL", "name": "Miami-Dade County Domestic Leave - Other", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_militaryExigency", "name": "Military Exigency", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_militaryService", "name": "Military Service", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_militaryTraining", "name": "Military Training", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_MN_Leave_for_Immediate_Family_Members_of_Military_Personnel_Injured_or_Killed_in_Active_Service-otherUS.MN", "name": "MN Injured/Killed Military Personnel", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_MN_Time_Off_for_Party_Officers/Delegates-otherUS.MN", "name": "MN Time Off for Party Officers and Delegates", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_Montgomery_County_Sick_and_Safe_Leave-otherUS.MD", "name": "Montgomery County Sick and Safe Leave - Other", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_NC_Small_Necessities-otherParentingUS.NC", "name": "NC Small Necessities", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_NV_Legislative_Leave-otherUS.NV", "name": "NV Legislative Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_NY_Crime_Victims_Leave-otherUS.NY", "name": "NY Crime Victims Leave (Witness only)", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_NY_Victims_of_Domestic_Violence_Leave-otherParentingUS.NY", "name": "NY Victims of Domestic Violence Leave - Other Parenting", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_NY_Victims_of_Domestic_Violence_Leave-otherUS.NY", "name": "NY Victims of Domestic Violence Leave - Other", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_OR_Court_Attendance_Leave-otherUS.OR", "name": "OR Court Attendance Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_OR_Elected_Official_Leave-otherUS.OR", "name": "OR Elected Official Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_organDonor", "name": "Organ Donor", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEA<PERSON>_orsickchild", "name": "<PERSON><PERSON>", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_PA_State_of_Emergency_leave/road_closure-otherUS.PA", "name": "PA State of Emergency Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_parenting", "name": "Parenting", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_PR_Court_Attendance-otherUS.PR", "name": "PR Court Attendance", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_PR_Special_Leave_for_Victims_of_Certain_Domestic_and_Sexual_Crimes-otherParentingUS.PR", "name": "PR Special Leave for Victims of Certain Domestic and Sexual Crimes - Other Parenting", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_PR_Special_Leave_for_Victims_of_Certain_Domestic_and_Sexual_Crimes-otherUS.PR", "name": "PR Special Leave for Victims of Certain Domestic and Sexual Crimes - Other", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_PR_Sports_Leave-otherUS.PR", "name": "PR Sports Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_PR_Training_or_Competition_Leave-otherUS.PR", "name": "PR Training or Competition Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_pregnancyComplication", "name": "Pregnancy Disability or Complication", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_publicHealthEmergency", "name": "Public Health Emergency", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_publicService", "name": "Public Service", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_restAndRecuperation", "name": "Rest and Recuperation", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_schoolActivities", "name": "School Activities", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_schoolClosure", "name": "School Closure", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_schoolInvolvement", "name": "School Involvement", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_SchoolVisitation", "name": "School Visitation", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_SD_Legislative_Leave-otherUS.SD", "name": "SD Legislative Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_stalk", "name": "Stalking", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_TX_Political_Activity-otherUS.TX", "name": "Texas Political Activity", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_UT_Witness_leave-otherUS.UT", "name": "UT Witness Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_VA_Crime_victims-otherUS.VA", "name": "VA Crime Victims (Witness Leave only)", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_vaccination", "name": "Vaccination", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_veterans", "name": "Veterans", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_victimOfSexualAssault", "name": "Victim of Sexual Assault", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_victimOfViolentCrime", "name": "Victim of Violent Crime", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_voting", "name": "Voting", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_VT_Crime_Victims-otherUS.VT", "name": "Vermont Crime Victims Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_VT_Jury_duty/Witness_leave-otherUS.VT", "name": "Vermont Jury Duty and Witness Leave (Witness Leave only)", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_VT_Legislative_Leave-otherUS.VT", "name": "Vermont Legislative Leave", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_VT_Short_Term_Family_Leave-otherParentingUS.VT", "name": "Vermont Short Term Family Leave - Other Parenting", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_VT_Short_Term_Family_Leave-otherUS.VT", "name": "Vermont Short Term Family Leave - Other", "bankCodes": [], "isMandatory": false}, {"leaveCode": "GOODYEAR_VT_Town_Hall-otherUS.VT", "name": "Vermont Town Hall Leave", "bankCodes": [], "isMandatory": false}], "supplementPTOStatement": [{"locale": "EN", "description": "."}], "transferAlertSetting": {"transferToAlert1Enabled": null, "transferToAlert2Enabled": null, "transferToAlert3Enabled": null, "transferToAlert1": [{"locale": "EN", "description": null}], "transferToAlert2": [{"locale": "EN", "description": null}], "transferToAlert3": [{"locale": "EN", "description": null}]}, "aR3IvrConfiguration": {"specialOpeningStatement": [{"locale": "EN", "description": null}], "openingStatement": [{"locale": "EN", "description": "Thank you for calling the Goodyear Absence Reporting System."}], "subOpeningStatement": [{"locale": "EN", "description": "Sub-opening message"}], "subOpeningSpecialStatement": [{"locale": "EN", "description": null}], "previousAbsenceWithinAA": 3, "maxRetryText": [{"locale": "EN", "description": "I am sorry, you have reached last maximum retry attempt."}], "maxRetry1Value": 3, "unexpectedErrorText": [{"locale": "EN", "description": "I am sorry, due to an unexpected error I cannot assist you right now. You may try again later or Please contact your supervisor or HR team for further assistance. Goodbye."}], "maxRetryMenu1": 3, "invalidEntryAndRetryText": [{"locale": "EN", "description": "Sorry, this is an invalid entry. \nPlease try again one more time."}], "repeatMenuValue": 3, "dateFormatMask": "MMDDYYYY", "timeFormatMask": "HHMM", "rtwText1": [{"locale": "EN", "description": "Our records indicate that you have had a recent absence on {LastAbsenceDate}.\nPress 1, if you have returned to work since this date. Press 2, if you have not returned to work.\nThis is for test."}], "isAMorPMText": [{"locale": "EN", "description": "For AM, press 1,\nFor PM, press 2."}], "invalidAbsenceDateText": [{"locale": "EN", "description": "The date you entered is not a valid date or it has already been entered."}], "transferToDisconnectText": [{"locale": "EN", "description": "To better assist you, call your supervisor or HR."}], "maxShiftLengthThresholdInMinutes": 900, "enterEmployeeIdText": [{"locale": "EN", "description": "Please enter your Employee ID using your keypad followed by the pound key."}], "enterDobText": [{"locale": "EN", "description": "To confirm your identification, please enter your Date of Birth using two digits for the Month, two digits for the Day and 4 digits for the Year."}], "invalidAuthenticationText": [{"locale": "EN", "description": "I am sorry the Employee ID and Date of Birth you entered do not match with our records. \nPlease try again."}], "enterTotalAbsenceDaysText": [{"locale": "EN", "description": "Please remember that all absences must be reported. Enter the total number of Missed Absences you're reporting absent on this call.\nKindly note that you can report a maximum of {TotalAbsenceDays} absences per call.\nTo repeat, press the * key."}], "primaryReasonIntroText": [{"locale": "EN", "description": "Please select the reason for your absence from one of the following options. If you or your family member is hospitalized or you expect your absence to be greater than 3 days due to an illness or injury,  Please select option number 2 ."}], "primaryReasonSpecialText": [{"locale": "EN", "description": null}], "primaryReasonMenuOptionText": [{"locale": "EN", "description": "For {ReasonName}, press {Number}."}], "prOptionRepeatText": [{"locale": "EN", "description": "To repeat the primary reason of absence, press the {RepeatDigit} key."}], "prRepeatDigit": "*", "secondaryReasonIntroText": [{"locale": "EN", "description": "Please select from one of the following secondary reasons"}], "secondaryReasonMenuOptionText": [{"locale": "EN", "description": "For {ReasonName}, press {Number}."}], "srOptionRepeatText": [{"locale": "EN", "description": "To repeat the secondary reason of absence, press the {RepeatDigit} key."}], "srRepeatDigit": "*", "secondaryReasonSpecialText": [{"locale": "EN", "description": null}], "enterAbsenceDateText": [{"locale": "EN", "description": "Please enter your absence date.\nUse two digits for the Month, two digits for the Day and 4 digits for the Year.\nFor example, February 3rd would be 0 2 0 3 2 0 2 2.\nTo repeat, press the * key."}], "confirmAbseneDate1Text": [{"locale": "EN", "description": "The absence is recorded for {ReportedAbsenceDateString}  \nPress 1 if this is correct,\nPress 2 if this is incorrect."}], "invalidOutOfRangeAbsenceDateText": [{"locale": "EN", "description": "The date you entered is not a valid date or it has already been entered."}], "enterShiftStartText": [{"locale": "EN", "description": "Please enter the start time of your absence. \nEnter the hour and then the minutes. \nFor example, to enter 07:30AM, please enter 0 7 3 0. \nTo enter 10PM, please enter 1 0 0 0.\nTo repeat, press the * key."}], "enterShiftEndText": [{"locale": "EN", "description": "Please enter the end time of your absence. \nEnter the hour and then the minutes. \nFor example, to enter 05:00PM, please enter 0 \n5 0 0. \nTo enter 10PM, please enter 1 0 0 0.\nTo repeat, press the * key."}], "conflictAbsenceDateText": [{"locale": "EN", "description": "You have a conflicting absence for {{Date}}. Press 1 to Overwrite your existing absence. Press 2 to Re-enter your absence information.\nTo repeat, press the star key."}], "enterRtwDateText": [{"locale": "EN", "description": "Please use two digits for the Month, two digits for the Day and 4 digits for the Year. For example, August 2nd would be 0 8 0 2 2 0 2 2. To repeat, press the star key."}], "invalidRtwDateText": [{"locale": "EN", "description": "Your Return to Work Date must be after your last absence date. Press 1 to re-enter your Return to Work Date for a date that is after your last absence date. Press 2 to re-enter your Absent Date. To repeat, press the * key."}], "confirmRtwDateText": [{"locale": "EN", "description": "We have your expected return to work date as {ReturnToWorkDateString} Press 1, if this is correct. Press 2, if this is incorrect."}], "isRtwKnownText": [{"locale": "EN", "description": "Do you know when will you return to work? if Yes, press 1, if No, press 2."}], "isRtwShiftTimeEnabled": false, "cancelThresholdInMinutes": null, "carrierTransferNumber": "+12893279912", "transferToCarrierText": [{"locale": "EN", "description": "Please stay on the line as we will now be transferring you to your carrier."}], "failTransferToCarrierText": [{"locale": "EN", "description": "We are unable to transfer your call at this time. Since you have reported an absence related to Short-Term Disability/Accident & Sickness or FMLA, you are required to call UNUM at ************** or visit www.unum.com to provide additional information."}], "transferToAgentText": [{"locale": "EN", "description": null}], "confirmationNumber1Text": [{"locale": "EN", "description": "The confirmation number for your absence is {ConfirmationNumber}. To repeat your confirmation number, press 1.\nTo acknowledge, press 2."}], "confirmationNumber2Text": [{"locale": "EN", "description": null}], "isEnterEmployeePhoneNumberRequired": false, "employeePhoneNumberText": [{"locale": "EN", "description": null}], "specialMessageEnabled": false, "specialMessageOption": [{"locale": "EN", "description": null}], "enableFmlaBalance": false, "fmlaBalanceText1": [{"locale": "EN", "description": null}], "fmlaBalanceText2": [{"locale": "EN", "description": null}], "enableLeaveCreation": null, "leaveCaseAssociationText": [{"locale": "EN", "description": null}], "leaveOptionText": [{"locale": "EN", "description": null}], "enableStdCreation": null, "stdCaseCreationText": [], "stdCaseAssociationText": [], "stdOptionSelectionText": []}, "enabledLocales": ["EN"], "linkedIncidentsLookbackThreshold": 7, "trackUnpaidTime": true, "noDaysNotificationRemain": 7, "isCustomVanity": false, "isCustomPhone": true, "isCustomClosingscript": false, "isPTOEnabled": false, "isTransferEnabled": true, "demographicFileUsed": true, "showAddNewEmployeeLink": true, "globalMaxDaysAllowed": 60, "globalReportableDaysInFuture": 60, "globalReportableDaysInPast": 3}}], "POST": []}, "authApiUrl": {"POST": [{"endpoint": "", "calledWith": {"payload": {"grant_type": "client_credentials", "client_id": "transform_leaves_api", "client_secret": "secret", "scope": "connect"}, "config": {"headers": {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}}}, "data": {"access_token": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}], "GET": []}}