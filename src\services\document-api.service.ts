import { DocumentManagementEntityTypes } from "@/util";
import Cache<PERSON><PERSON> from "./cache-service";
import { AuthClient } from "./mixins";
import { AbsenceIncident,  ParsedVoiceMail, UploadDocumentReturnModel } from "@/models";
import FormData from "form-data";

export default class DocumentApiService extends AuthClient(CacheClient){
    public async AddDocument(email: ParsedVoiceMail, incident: AbsenceIncident, contactId: string ): Promise<UploadDocumentReturnModel>{
        try{
            const absApiEndpointBase = process.env.documentApiEndpoint;
            const token = await this.getToken(contactId);
            let formData = this.preparePayload(email, incident);
            const config = {
                headers: {
                  Authorization: `Bearer ${token}`,
                  ...formData.getHeaders()
                },
              };
            const apiEndpoint = absApiEndpointBase + `?clientCode=${incident.ClientCode}`;
            this.Logger.log("PERFORMANCE: addDocument BEFORE post for absenceid", incident.AbsenceIncidentId?.toString());
            const response = await this.AxiosClient?.post(apiEndpoint, formData, config);
            this.Logger.log("PERFORMANCE: addDocument After post for absenceid", incident.AbsenceIncidentId?.toString());
            this.Logger.log(
                `API Request: ${apiEndpoint}  parameters:`,
                incident.ClientCode,
                response?.data,
              );
            
            return  this.MapToUploadDocumentResult(response);//this data should have incidentid or validation errors
            }
            catch (error: any) {
                this.Logger.log(error.message);
                if (error.response) {
                    this.Logger.log(`Error in addDocument API call with status ${error.response.status}`);
                    if(!error.response.data?.success){
                        let validationErrors: any[] = error.response.data.validationErrors;
                        this.Logger.log(`Validation Errors returned in addDocument API call:  ${JSON.stringify(validationErrors)}`);
                        return this.MapToUploadDocumentResult(error.response.data);
                    }
                    
                } else if (error.request) {
                    // Client made a request but response is not received
                    this.Logger.log('called', error.request);
                } else {
                    // Other case
                    this.Logger.log('Error', error.message);
                }
                throw Error(error);
            }
    };
    MapToUploadDocumentResult(data: any) : UploadDocumentReturnModel{
        let toReturn: UploadDocumentReturnModel = {
            DocumentId: '', Success: false,  ValidationErrors: []
        }       
        if(data)
        {
            toReturn.DocumentId = data.data;
            toReturn.Success = data.status == 200 ? true:false;
            let err: any[] = data.validationErrors as Array<any>;
            if(!toReturn.Success){
            err.forEach((item: any) => {
                toReturn.ValidationErrors.push({
                    ErrorCode: item.errorCode,
                    ErrorDescription: item.errorDescription,
                    ErrorSeverity: item.errorSeverity,
                    ErrorProvider: item.errorProvider,
                    MetaData: item.metaData
                })
            });
        }
    }
        return toReturn;
    }

private  preparePayload(email: ParsedVoiceMail, incident: AbsenceIncident): FormData {
    const formData = new FormData();
    formData.append('Data.EntityId', incident.AbsenceIncidentId?.toString());
    formData.append('Data.EntityType', DocumentManagementEntityTypes.MyAbilitiAbsenceVM);
    formData.append('Data.Source', "IVR");

    // Get the last attachment
    const att = email.Attachment.peekLast();
    const buffer = att?.Content;

    if (buffer && att) {
        formData.append('File', buffer, {filename: att.FileName, contentType: att.ContentType, knownLength: att.Size});
        
    } else {
        this.Logger.error("Attachment content is missing");
    }
    return formData;
}

}