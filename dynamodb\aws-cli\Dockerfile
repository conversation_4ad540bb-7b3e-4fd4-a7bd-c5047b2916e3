FROM amazon/aws-cli AS base

ENV AWS_DYNAMODB_ENDPOINT="--endpoint-url=http://dynamo-db-local:8000"
ENV AWS_REGION="us-east-1"
ENV AWS_DEFAULT_PROFILE="dynamo-db-local"
ENV AWS_ACCESS_KEY_ID="********************"
ENV AWS_SECRET_ACCESS_KEY="y5TGlFJz5hH31uAh742CT3GDZpj9puYQX1ITDLTQ"
ENV defaultLanguage="EN"
ENV ivrCallsTableName="ablarivr-call-session"
ENV cacheTableName="ablarivr-data-cache"
ENV clientConfigTableName="ablarivr-client-config"
ENV reasonMenuTableName="ablarivr-reasonmenu"

COPY ./create-tables.sh .

RUN mkdir /install-logs

ENTRYPOINT ["/bin/sh", "-c", "./create-tables.sh > /install-logs/install.log"]