export const ValidationErrors = {
    InvalidDoBFormat: 'invalid DOB format',
    InvalidDoB:'invalid DOB',
    InvalidEmployee: 'invalid employee',
    EmployeePhoneNumberInvalid: 'invalid employee phone number ',
    InvalidShiftStartTime: 'invalid shift start time',
    InvalidShiftTimeFormat: 'invalidformat',
    ShiftEndTimeEarlierThanStartTime: 'endbeforestart',
    ShiftTimeShouldBeIncrementalOfConfig: 'shift time should be incremental of config',
    ShiftDurationTooEarly: 'shiftdurationtooearly',
    ShiftDurationTooLate: 'shiftdurationtoolate',
    InvalidNumber: 'invalid number',
    AbsenceDateNotWithinValidRange: 'absence date not within valid range',
    InvalidEmployer: 'invalid employer',
    ReturnToWorkDateDuplicate: 'duplicate',
    ReturnToWorkDateTooEarly: 'tooearly',
    ReturnToWorkDateTooFar: 'toofar',
    InvalidRTWDateFormat: 'invalid',//invalid RTW date format
    InvalidDateFormat: 'invaliddateformat',
    DuplicateEntry: 'duplicateentry',
    InvalidDateFdaGreateThanLda: 'LDA is greater than FDA',
    InvalidEmployeeId: 'invalid employee number',
    AbsenceReasonQuestionIsMissing: 'absence reason question is missing',
    InvalidRTWShiftStartTime: 'invalid return to work shift start time',
    OverlappedEntry:'date already entered in previous claims',
    IsTotalDaysGreaterThanMax: 'total days greater than Max days'
};