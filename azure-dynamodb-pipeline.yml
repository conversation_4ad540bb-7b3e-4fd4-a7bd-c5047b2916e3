# this is one-time script to create the dynamodb tables. It can be run to set up aws environment.
trigger: none
pool:
    vmImage: 'ubuntu-latest'

variables:
  - name: Region
    value: "us-east-1"
  - name: AwsCredentialsQA
    value: "lifeworks-connect-lab"
  - name: AwsCredentialsUAT
    value: "lifeworks-connect-uat"
  - name: AwsCredentialsPROD
    value: "lifeworks-connect-PROD"

  
  

stages:
  - stage: DeployQA
    displayName: Deploy to QA
    variables: 
    - group: ablarivr_QA
    jobs:
      - deployment: BuildAndDeploy
        displayName: Build and Deploy
        environment: 'AbilitiApiIVRLambda-QA'
        strategy:
            runOnce:
              deploy:
                  steps:
                    - checkout: self
                    - task: S3Upload@1
                      displayName: Create Deploy Bucket
                      inputs:
                        awsCredentials: $(AwsCredentialsQA)
                        bucketName: $(AwsBucket)
                        regionName: $(Region)
                        globExpressions: "undefined"
                        createBucket: true                

                    - task: AWSShellScript@1
                      displayName: Package db tables
                      env:
                        workingDir: "$(System.DefaultWorkingDirectory)"
                      inputs:
                        awsCredentials: $(AwsCredentialsQA)
                        regionName: $(Region)
                        scriptType: 'inline'
                        inlineScript: |
                          echo $workingDir &&
                          sam build --debug \
                          -s . \
                          --template-file ./CFTemplates/CF_ddbtemplate.yaml

                    - task: AWSShellScript@1
                      displayName: Deploy DynamoDB tables
                      inputs:
                        awsCredentials: $(AwsCredentialsQA)
                        regionName: $(Region)
                        scriptType: "inline"
                        inlineScript: |                
                          sam deploy \
                          --template-file ./CFTemplates/CF_ddbtemplate.yaml \
                          --no-confirm-changeset \
                          --no-fail-on-empty-changeset \
                          --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
                          --stack-name $(DbStackName) \
                          --s3-bucket $(AwsBucket) \
                          --s3-prefix $(DbStackName) \

  - stage: DeployUAT
    displayName: Deploy to UAT
    variables: 
    - group: ablarivr_UAT
    jobs:
      - deployment: BuildAndDeploy
        displayName: Build and Deploy
        environment: 'AbilitiApiIVRLambda-UAT'
        strategy:
            runOnce:
              deploy:
                  steps:
                    - checkout: self
                    - task: S3Upload@1
                      displayName: Create Deploy Bucket
                      inputs:
                        awsCredentials: $(AwsCredentialsUAT)
                        bucketName: $(AwsBucket)
                        regionName: $(Region)
                        globExpressions: "undefined"
                        createBucket: true                

                    - task: AWSShellScript@1
                      displayName: Package db tables
                      env:
                        workingDir: "$(System.DefaultWorkingDirectory)"
                      inputs:
                        awsCredentials: $(AwsCredentialsUAT)
                        regionName: $(Region)
                        scriptType: 'inline'
                        inlineScript: |
                          echo $workingDir &&
                          sam build --debug \
                          -s . \
                          --template-file ./CFTemplates/CF_ddbtemplate.yaml

                    - task: AWSShellScript@1
                      displayName: Deploy DynamoDB tables
                      inputs:
                        awsCredentials: $(AwsCredentialsUAT)
                        regionName: $(Region)
                        scriptType: "inline"
                        inlineScript: |                
                          sam deploy \
                          --template-file ./CFTemplates/CF_ddbtemplate.yaml \
                          --no-confirm-changeset \
                          --no-fail-on-empty-changeset \
                          --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
                          --stack-name $(DbStackName) \
                          --s3-bucket $(AwsBucket) \
                          --s3-prefix $(DbStackName) \

  - stage: DeployPROD
    displayName: Deploy to PROD
    variables: 
    - group: ablarivr_PROD
    jobs:
      - deployment: BuildAndDeploy
        displayName: Build and Deploy
        environment: 'AbilitiApiIVRLambda-PROD'
        strategy:
            runOnce:
              deploy:
                  steps:
                    - checkout: self
                    - task: S3Upload@1
                      displayName: Create Deploy Bucket
                      inputs:
                        awsCredentials: $(AwsCredentialsPROD)
                        bucketName: $(AwsBucket)
                        regionName: $(Region)
                        globExpressions: "undefined"
                        createBucket: true


                    - task: AWSShellScript@1
                      displayName: Package db tables
                      env:
                        workingDir: "$(System.DefaultWorkingDirectory)"
                      inputs:
                        awsCredentials: $(AwsCredentialsQA)
                        regionName: $(Region)
                        scriptType: 'inline'
                        inlineScript: |
                          echo $workingDir &&
                          sam build --debug \
                          -s . \
                          --template-file ./CFTemplates/CF_ddbtemplate.yaml

                    - task: AWSShellScript@1
                      displayName: Deploy DynamoDB tables
                      inputs:
                        awsCredentials: $(AwsCredentialsPROD)
                        regionName: $(Region)
                        scriptType: "inline"
                        inlineScript: |                
                          sam deploy \
                          --template-file ./CFTemplates/CF_ddbtemplate.yaml \
                          --no-confirm-changeset \
                          --no-fail-on-empty-changeset \
                          --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
                          --stack-name $(DbStackName) \
                          --s3-bucket $(AwsBucket) \
                          --s3-prefix $(DbStackName) \

                   
 