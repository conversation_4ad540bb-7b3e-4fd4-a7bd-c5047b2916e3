import { BaseService, IService, IDBService } from '@/services';
import { TESTS_RUNNING } from '@/util'
import { AnyConstructor } from 'Mixins';
import {
  DynamoDBClient,
  GetItemCommand,
  PutItemCommand,
  //QueryItemCommand,
  ListTablesCommand,
} from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  QueryCommand,
  PutCommand,
  GetCommand,
  UpdateCommand,
  DeleteCommand
} from '@aws-sdk/lib-dynamodb';
import {
  GetItemCommandInput,
  PutItemCommandInput,
} from '@aws-sdk/client-dynamodb/dist-types/commands';
import { DynamoDbDTO } from '@/models';

//TODO - MOVE TO ANOTHER FILE FOR CONFIG
const marshallOptions = {
  // Whether to automatically convert empty strings, blobs, and sets to `null`.
  convertEmptyValues: true, // false, by default.
  // Whether to remove undefined values while marshalling.
  removeUndefinedValues: true, // false, by default.
  // Whether to convert typeof object to map attribute.
  convertClassInstanceToMap: true, // false, by default.
};

const unmarshallOptions = {
  // Whether to return numbers as a string instead of converting them to native JavaScript numbers.
  wrapNumbers: false, // false, by default.
};

//END TODO

export const DynamoClient = <T extends AnyConstructor<BaseService>>(
  target: T,
) => {
  return class extends target implements IService, IDBService {
    
    db: DynamoDBClient | any = null;
    
    get dbClient () : DynamoDBDocumentClient {
      if (!this.db) {
        this.db = new DynamoDBClient({
          endpoint:
            process.env.MOCK_DYNAMODB_ENDPOINT ||
            process.env.AWS_DYNAMODB_ENDPOINT,
            region: TESTS_RUNNING ? 'local' : process.env.AWS_REGION,
            //** FOR LOCAL DEBUGGING ONLY ******************************
            // credentials: {
            //  accessKeyId: '********************', 
            //  secretAccessKey: 'y5TGlFJz5hH31uAh742CT3GDZpj9puYQX1ITDLTQ',
            //}, 
            
        });
  
        this._dbClient = DynamoDBDocumentClient.from(this.db, {
          marshallOptions,
          unmarshallOptions,
        });
  
        if (TESTS_RUNNING) {
          (<DynamoDBClient>this.db)
            .send(new ListTablesCommand({}))
            .then((r) => console.log(`IN client OUTPUT: ${JSON.stringify(r)}`));
        }
      }
      return this._dbClient;
    }

   _dbClient: DynamoDBDocumentClient | any;
    

    get DynamoDb(): DynamoDBClient {
      return this.db;
    }

    constructor(...args: any[]) {
      super(...arguments);
      //&console.log(
      //&  `In decorator: ${
       //&   process.env.MOCK_DYNAMODB_ENDPOINT ||
       //&   process.env.AWS_DYNAMODB_ENDPOINT
      //&  } & ${process.env.cacheTableName} & ${process.env.AWS_REGION}`,
    //&  );
    }

    public async get(cmd: any) { //ToDo:
      try {
        //let _cmd = this.MapToGet(cmd);
        let toReturn = await this.dbClient.send(new GetCommand(cmd));
        return toReturn;
      } catch (err: any) {
        this.Logger.error('Invalid get command' + err);
        return null;
      }
    }
    public async update(cmd: any) {
      try {
        //let _cmd = this.MapToGet(cmd);
       let toReturn = await this.dbClient.send(new UpdateCommand(cmd));
        return toReturn;
      } catch (err: any) {
        this.Logger.error('Invalid update command' + err);
        return null;
      }
    }
    public async query(cmd: any) {
      try {
        return await this.dbClient.send(new QueryCommand(cmd)); //TODO - MOVE TO DYNAMODBCLIENT
      } catch (err: any) {
        this.Logger.error('Invalid query command' + err);
        return null;
      }
    }

    public async put(cmd: any) {
      try {
        let toReturn = await this.dbClient.send(new PutCommand(cmd)); //TODO - MOVE TO DYNAMODBCLIENT
        return toReturn;
      } catch (err: any) {
        this.Logger.error('Invalid put command' + err);
        return null;
      }
    }
    
    public async delete(cmd: any) {
      try {
        let toReturn = await this.dbClient.send(new DeleteCommand(cmd)); //TODO - MOVE TO DYNAMODBCLIENT
        return toReturn;
      } catch (err: any) {
        this.Logger.error('Invalid delete command' + err);
        return null;
      }
    }
    public async Get(cmd: DynamoDbDTO) {
    
      return await this.db.send(new GetItemCommand(this.MapToGet(cmd)));
    }

// TTL TimeToLive - Start
public getTTL(key:string): number {
        
  if(key.includes('auth')){
      return this.getAuthTTL();
  }
  return this.getTTLDate();
}

public getTTLDate = () => {
  let today: Date = new Date();
  return Math.round((new Date(today.getTime() + 0.5 * 24 * 60 * 60000)).getTime()/1000);
};

private getAuthTTL = () => {
  //&console.log('inside auth ttl');
  let today: Date = new Date();
  return Math.round((new Date(today.getTime() + 60 * 60000 )).getTime()/1000);
};
// TTL TimeToLive - Finish

    private MapToGet(cmd: DynamoDbDTO): GetItemCommandInput {
      let keyName = cmd.Key;

      let newCmd: GetItemCommandInput = {
        TableName: cmd.TableName,
        Key: cmd.Key,
      };
      return newCmd;
    }

    private MapToPut(cmd: DynamoDbDTO): PutItemCommand {
      let keyName = cmd.Key;

      let newCmd: PutItemCommandInput = {
        TableName: cmd.TableName,
        Item: cmd.Item,
      };
      return new PutItemCommand(newCmd);
    }
  };
};
