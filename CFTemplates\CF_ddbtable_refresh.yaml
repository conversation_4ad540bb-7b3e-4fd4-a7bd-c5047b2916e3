AWSTemplateFormatVersion : '2010-09-09'
Description: DEV Abiliti IVR dynamoDB.

Resources:  
  ablarivrClientConfig:
    Type: AWS::DynamoDB::Table
    Properties:
        TableName: ablarivr-client-config
        AttributeDefinitions:
          - AttributeName: ClientName
            AttributeType: S
          - AttributeName: ClientCode
            AttributeType: S
        KeySchema:
          - AttributeName: ClientName
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
        PointInTimeRecoverySpecification:
            PointInTimeRecoveryEnabled: true
        GlobalSecondaryIndexes:
          - IndexName: ClientCodeIdx
            KeySchema:
              - AttributeName: ClientCode
                KeyType: HASH
            Projection:
                ProjectionType: ALL
        Tags:
        - Key: "LOB"
          Value: "adm"
        - Key: "Project"
          Value: "ablarivr"