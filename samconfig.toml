version = 0.1
[default]
[default.deploy]
[default.deploy.parameters]
stack_name = "ABLARIVR-Lambda-stack-dev"
s3_bucket = "aws-sam-cli-managed-default-samclisourcebucket-1v4nfxl2hcnyc"
s3_prefix = "ABLARIVR-Lambda-stack-dev"
region = "us-east-1"
confirm_changeset = true
capabilities = "CAPABILITY_IAM"
image_repositories = []

[uat.deploy]
[uat.deploy.parameters]
stack_name = "ABLARIVR-Lambda-stack-uat"
s3_bucket = "aws-sam-cli-managed-uat-samclisourcebucket-1v4nfxl2hcnyc"
s3_prefix = "ABLARIVR-Lambda-stack-uat"
region = "us-east-1"
confirm_changeset = true
capabilities = "CAPABILITY_IAM"
image_repositories = []

[prod.deploy]
[prod.deploy.parameters]
stack_name = "ABLARIVR-Lambda-stack-prod"
s3_bucket = "aws-sam-cli-managed-prod-samclisourcebucket-1v4nfxl2hcnyc"
s3_prefix = "ABLARIVR-Lambda-stack-prod"
region = "us-east-1"
confirm_changeset = true
capabilities = "CAPABILITY_IAM"
image_repositories = []


