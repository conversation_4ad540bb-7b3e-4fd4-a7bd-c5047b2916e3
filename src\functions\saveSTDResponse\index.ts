import { ConnectContactFlowEvent } from 'aws-lambda';
import { CallLog, AbsenceReportingService } from '@/services';

const callService = new CallLog();
const absenceService = new AbsenceReportingService();
absenceService.CallLog = callService;
export const handler = async (event: ConnectContactFlowEvent) => {
  const lambdaName = 'saveSTDResponse';
  callService.Logger.log('Input data', event);
//change code to check if warning msg enabled in iVR config
  const clientCode = event.Details.Parameters.ClientCode;
  const contactId = event.Details.ContactData.ContactId;
  const selectedAnswer: number = parseInt(event.Details.Parameters.SelectedSTDResponse,);
  callService.Logger.log(`Selected STD response - ${selectedAnswer}`);
  const callData = await callService.getCallDataAsync(contactId);
  const currentIncident = (callData.Absences)?.pop();
  if (currentIncident && currentIncident.AbsenceIncident ) {
      
    let stdReferralResponse = { "CQ_APPLY_FOR_STD_REFERRAL" : selectedAnswer === 1 ? "true": "false"} ;
    let leaveReferralResponse = { "CQ_APPLY_FOR_LEAVE_REFERRAL": selectedAnswer === 1 ? "true" : "false" };
    let associateClaimResponse = { "CQ_ASK_FOR_EXISTING_LEAVE_CASE": selectedAnswer === 1 ? "0" : "" };
    currentIncident.AbsenceIncident.QuestionResponses = currentIncident.AbsenceIncident.QuestionResponses? {...currentIncident.AbsenceIncident.QuestionResponses, ...stdReferralResponse} : {stdReferralResponse};
    currentIncident.AbsenceIncident.QuestionResponses = currentIncident.AbsenceIncident.QuestionResponses ? { ...currentIncident.AbsenceIncident.QuestionResponses, ...leaveReferralResponse } : { leaveReferralResponse };
    currentIncident.AbsenceIncident.QuestionResponses = currentIncident.AbsenceIncident.QuestionResponses ? { ...currentIncident.AbsenceIncident.QuestionResponses, ...associateClaimResponse } : { associateClaimResponse };
    callService.Logger.log("Updating absence and saving with STD referral responses");
    callData.Absences.push(currentIncident);
    await callService.setCallDataAsync(contactId, {
      IsSaved: "true",
      Absences: callData.Absences 
    });
  }

return {IsSaveSuccess: true}

}