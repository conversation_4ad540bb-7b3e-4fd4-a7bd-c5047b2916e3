import {  BaseService } from '@/services'
import { HttpClient } from './mixins';


export class PublicHoliday extends HttpClient(BaseService) {

    private nagerApiUrl: string = 'https://date.nager.at/Api/v2/';

    public async isTodayPublicHolidayAsync(countryCode?: string): Promise<boolean> {
        let cc = countryCode || 'US';
        const methodUrl = this.nagerApiUrl + 'IsTodayPublicHoliday/' + cc;

        const response = await this.AxiosClient?.get(methodUrl);
        return response?.status === 200;
    }
};
