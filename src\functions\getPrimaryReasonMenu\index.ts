import { ReasonMenuService,  ScopedHttpService, LocalConfiguration, CallLog } from '@/services';
import { ConnectContactFlowEvent } from 'aws-lambda';
import {  ReasonWrapper } from '@/models/ReasonModel';
import { GetCountry, GetProvinceCode } from '@/models/EmployeeData';

const reasonConfig = new ReasonMenuService();
const localConfig = new LocalConfiguration();
const session = new CallLog();

export const handler = async (event: ConnectContactFlowEvent) => {
  const lambdaName = 'getPrimaryReasonMenu';
  reasonConfig.Logger.log('Input data', event);
  let contactId;
  let clientCode = event.Details.Parameters.ClientCode;
  contactId = event.Details.ContactData.ContactId;
  reasonConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
  localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
  const config = await localConfig.getClientConfigAsync(clientCode);
  const sessionData = await session.getCallDataAsync(contactId);
  var locale = sessionData.SelectedLang ?? 'EN';
  var empData: any = sessionData.EmployeeInfo;

  let employeeNumber: string = '';
  if (sessionData && sessionData.EmployeeInfo)
  {
    employeeNumber = sessionData.EmployeeInfo.employeeNumber;
  }
  let primaryReasons: ReasonWrapper = await reasonConfig.getReasonMenuAsync(clientCode, employeeNumber);
    
    let toReturn = primaryReasons.getMenuForConnect(config, locale);      
  
  return {
    PromptPrimaryReasonMenuOptionsSSML: toReturn.PromptPrimaryReasonMenuOptionsSSML , //TODO: locale?
    NumberOfOptions: primaryReasons?.Answers.length ?? 0,
    OptionToRepeat: toReturn.OptionToRepeat,
    PromptSpecialMsgSSML: toReturn.PromptSpecialMsgSSML,
    IntroPrimaryMenuBypassOption: toReturn.IntroPrimaryMenuBypassOption ,
    PromptIntroPrimaryReasonMenuSSML: toReturn.PromptIntroPrimaryReasonMenuSSML
  };
};
