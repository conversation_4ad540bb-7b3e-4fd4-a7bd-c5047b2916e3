import { AuthClient } from 'Mixins'
import { CacheClient } from '@/services'
import { EmployeeClaim, EmployeeClaimItem, EmployeeDataDetails } from '@/models'
import { AbilitiClaimStatus } from '@/util';

export default class EmployeeApiService extends AuthClient(CacheClient) {
  public async getEmployeeAsync(clientCode: string, employeeNumber: string | number, contactId: string, ) {
    try {
      //&console.log(`inside emp api${clientCode} ${employeeNumber}, ${contactId}`,);
      const eeApiEndpointBase = process.env.eeApiEndpoint;

      let token = await this.getToken(contactId);
      if (token.S) {
        token = token.S;
      }
    
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: {
          clientCode: clientCode,
        },
      };
      const apiEndpoint = eeApiEndpointBase + `Employee/${employeeNumber}/esEmployeeByNumber?clientCode=${clientCode}`;

      const response = await this.AxiosClient?.get(apiEndpoint, config);
      this.log.log(`API Request: ${apiEndpoint}  parameters:`, clientCode, response && response.data,);
      //this.MapEmployeeInfo(response?.data);
      ////&console.log(response?.data);
      return response?.data;
    } catch (error: any) {
      if (error?.response) {
         this.log.error('Error', error.message);
      }
      throw Error(error);
    }
  }
  private MapEmployeeInfo(response: any): EmployeeDataDetails{
    let toReturn: EmployeeDataDetails ={
      EmployeeInfo: {},
     
    };

    if(response)
    {

    }


    return toReturn;
  }

  //gets Employee claims

  public async getEmployeeClaims(clientCode: string, contactId: string, employeeNumber?: string | number, sortingField?: string, claimNumbers?: string[], claimType?: string): Promise<EmployeeClaim> {
    try {
      const eeApiEndpointBase = process.env.eeApiEndpoint;
      const token = await this.getToken(contactId);

      const payload = JSON.stringify({
        pageSize: 1000,
        pageNumber: 1,
        sortingFields: [
          {
            fieldName: `${sortingField}`,//"leaveStartDate",
            order: "DESC",
            fieldType: "date"
          }
        ],
        parameters: {
          employeeNumber: employeeNumber ?? '',
          claimNumbers: claimNumbers
        },
        clientCode: clientCode,
      });
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      };

      const apiEndpoint =
        eeApiEndpointBase + 'ElasticSearch/SearchClaims';
      const response = await this.AxiosClient?.post(
        apiEndpoint,
        payload,
        config,
      );

      //&console.log(JSON.stringify(response?.status || { error: 'no_response' }));
      this.Logger.log(
        `getEmployeeClaims API Request: ${apiEndpoint}  parameters:`,
        JSON.stringify(payload),
        response?.data,
      );
      ////&console.log(response?.data?.items);

      return {items: this.MapResponseToClaimsData(response?.data)};
    } catch (error: any) {
      this.Logger.log('Error in getEmployeeClaims', error);
      throw Error(error);
    }
  }


  // TODO: Reusable?
  private MapResponseToClaimsData(apiResponse: any): EmployeeClaimItem[] {
    if (!apiResponse.items) {
      return [];
    }
      return apiResponse.items.filter((claimItem: any) => claimItem?.claimStatus !== AbilitiClaimStatus.InProgress).map((claimItem: any) => ({
      incidentId: claimItem.incidentId,
      incidentTypeCssClass: claimItem.incidentTypeCssClass,
      lastDateOfAbsence: claimItem?.lastDateOfAbsence ?? null,
      claimStartDate: claimItem?.claimStartDate,
      leaveStartDate: claimItem?.leaveStartDate,
      leaveEndDate: claimItem?.leaveEndDate ?? null,
      claimStatus: claimItem?.claimStatus,
      claimDates: claimItem.claimDates ? claimItem.claimDates.map((x: any) => ({
        startDate: x?.startDate,
        endDate: x?.endDate,
        isPartialAbsence: x?.isPartialAbsence,
        unpaidTimeInMinutes: x?.unpaidTimeInMinutes,
      })) : [],
      primaryReasonOfAbsenceId: claimItem.primaryReasonOfAbsenceId,
      primaryReasonOfAbsenceLocale: claimItem.primaryReasonOfAbsenceLocale? 
                                    claimItem.primaryReasonOfAbsenceLocale.map((pr: any) => ({
                                      Locale: pr?.locale,
                                      Description: pr?.description
                                    })) : claimItem.primaryReasonLocale ? claimItem.primaryReasonLocale.map((pr: any) => ({
                                      Locale: pr?.locale,
                                      Description: pr?.description
                                    })) : [],
      secondaryReasonOfAbsenceId: claimItem.secondaryReasonOfAbsenceId,
      secondaryReasonOfAbsenceLocale: claimItem.secondaryReasonOfAbsenceLocale ? 
                                      claimItem.secondaryReasonOfAbsenceLocale.map((sr: any) => ({
                                          Locale: sr?.locale,
                                          Description: sr?.description
                                      })) : claimItem.secondaryReasonLocale ? claimItem.secondaryReasonLocale.map((pr: any) => ({
                                          Locale: pr?.locale,
                                          Description: pr?.description
                                      })) : [],
      policies: (claimItem.policies != undefined || (Array.isArray(claimItem.policies) && claimItem.policies.length > 0)) ?
                claimItem.policies.map((p: any) => ({
                  policyCode: p?.policyCode
                })): null
      
    }));
  }
}
