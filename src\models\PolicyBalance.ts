import moment,{ Moment} from "moment";
import { LocaleModel } from "./AbsenceModel";

export interface PolicyBalanceModel{
    Used?: number;
    Planned?: number;
    Remaining?: number;
    Total?: number;
    UsedHours?: number;
    PlannedHours?: number;
    RemainingHours?: number;
    TotalHours?: number;
    Unit?: string;
    PolicyCode?: string;
    Eligibility?: boolean;
    PolicyCodeLocale?: LocaleModel[];
    ResetDate?: string;
    CalculationMethod? :string;
    EntitlementDescription?: string;
    EntitlementType?: string;
    Determinations: LeaveDeterminationModel[];
    CalculationMethodDescription: string;
    DisplayHours: boolean;
}

interface LeaveDeterminationModel{
    StartDate?: string | Moment;
    EndDate?: string | Moment;
    Resolution?: string;
    ResolutionLocale?: LocaleModel[];
    PolicyCode: string;
    PolicyCodeLocale?: LocaleModel[];
    CompletedOn? :string | Moment;
    EntitlementUnit?: string;
    RequestedAbsence?: number;

}