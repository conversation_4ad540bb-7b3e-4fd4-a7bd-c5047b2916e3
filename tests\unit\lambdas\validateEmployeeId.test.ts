import { flowEvent } from "../mocks/mock-all";
import { employeeId<PERSON>and<PERSON> } from '@/functions'
import { startDb, stopDb, createTables, deleteTables } from "jest-dynalite";
import {DynamoDBClient, ListTablesCommand } from '@aws-sdk/client-dynamodb'
import {MockAll, TearDown} from '../mocks/mock-all'
import { AuthResponse } from "@/models";
import Sinon from "sinon";
jest.setTimeout(60000)




describe('Unit test for app handler',  () => {

  beforeAll(async () => {});
  beforeEach( async() => { 
      MockAll();  
      ////&console.log("In create");
  })

  afterEach ( async () => {
      ////&console.log("In delete");
      TearDown();
      await deleteTables();
      await createTables();
  })
  afterAll(async () => {
      ////&console.log("In stopdb");
      await stopDb();
  })
  it('verfies successful response', async () => {
    
      const result = await employeeIdHandler(flowEvent)
      console.log(result);
      expect(result).toBeDefined();
      expect((result).IsEmployeeValid).toBe(true);
  });
  it('verifies validation error if wrong emp num', async () => {
    let flowObject: any =  {
      Details : {
       ContactData: {
        Attributes: {},
        ContactId:  'e6gkw3e3-a66b-4362-af33-65c752a6321',
        },
        Parameters : {
          ClientCode: 'longos',
          EmployeeId: '123456',
          DoB: '19800101'
        }
    }
      };
      const result = await employeeIdHandler(flowObject)
      console.log(result);
      expect(result).toBeDefined();
      expect((result).IsEmployeeIdValid).toBe(false);
  });
});
