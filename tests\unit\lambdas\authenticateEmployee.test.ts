import { flowEvent } from "../mocks/mock-all";
import { authenticate<PERSON><PERSON><PERSON> } from '../../../src/functions'
import { startDb, stopDb, createTables, deleteTables } from "jest-dynalite";
import {DynamoDBClient, ListTablesCommand } from '@aws-sdk/client-dynamodb'
import {MockAll, TearDown} from '../mocks/mock-all'
import { AuthResponse } from "../../../src/models";

import Sinon from "sinon";
jest.setTimeout(60000)
describe('Unit test for app handler',  () => {

    beforeAll(async () => {
        ////&console.log("In startdb"); 
        //await startDb(); 
        //await createTables();
        /*let db = new DynamoDBClient({
            endpoint:
              process.env.MOCK_DYNAMODB_ENDPOINT ||
              process.env.AWS_DYNAMODB_ENDPOINT,
            region: 'local' || process.env.AWS_REGION,
          });
        let count = 0;
        while (count < 4) {
        await db.send(new ListTablesCommand({}))
        .then((r) => {
            console.error(`IN client OUTPUT: ${JSON.stringify(r)}`)
            count = r.TableNames?.length || 0;
        });
        }*/
       
       
    });

    beforeEach( async() => { 
        MockAll();  
        ////&console.log("In create");
    })

    afterEach ( async () => {
        ////&console.log("In delete");
        TearDown();
        await deleteTables();
        await createTables();
    })
    afterAll(async () => {
        ////&console.log("In stopdb");
        await stopDb();
    })

    it('verifies successful response', async () => {
       
        const result = await authenticateHandler(flowEvent)
        //&console.log(result);
        expect(result).toBeDefined();        
        expect((<AuthResponse>result).IsEmployeeValid).toBe(true);
        expect((<AuthResponse>result).IsDoBValid).toBe(true);
        expect((<AuthResponse>result).IsPrevAbsWithinAADays).toBe(false);
        // expect(result.message).toEqual(null);
    });

    it('check for failing result; unexisting record on client config', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'e6gkw3e3-a66b-4362-af33-65c752a6321',
              },
              Parameters : {
                ClientCode: 'longos',
                EmployeeId: '123456',
                DoB: '19800101'
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await authenticateHandler(flowObject)

        expect(result).toBeDefined();
        expect((<any>result).error).toBeDefined();
        expect((<any>result).result).toBeNull()
    });
    
});