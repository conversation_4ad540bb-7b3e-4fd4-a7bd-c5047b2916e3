#this is the main pipeline which build all code and deploys.
# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

# Hardcoded values for now for LAB ENV usage.
# TODO:
#  - Variables for different environments
#  - Loop for template tasks
#  - Add testing step 

trigger:
    - Development
pool:
    vmImage: 'ubuntu-24.04'

variables:
  - name: Region
    value: "us-east-1"
  - name: AwsCredentialsQA
    value: "lifeworks-connect-lab"
  - name: AwsCredentialsUAT
    value: "lifeworks-connect-uat"
  - name: AwsCredentialsPROD
    value: "lifeworks-connect-PROD"
  

  
  

stages:
  - stage: DeployQA
    displayName: Deploy to QA
    variables: 
    - group: ablarivr_QA
    jobs:
      - deployment: BuildAndDeploy
        displayName: Build and Deploy
        environment: 'AbilitiApiIVRLambda-QA'
        strategy:
            runOnce:
              deploy:
                  steps:
                    - checkout: self
                    - task: S3Upload@1
                      displayName: Create Deploy Bucket
                      inputs:
                        awsCredentials: $(AwsCredentialsQA)
                        bucketName: $(AwsBucket)
                        regionName: $(Region)
                        globExpressions: "undefined"
                        createBucket: true

                    - task: NodeTool@0
                      inputs:
                        versionSource: 'spec'
                        versionSpec: '22.x'
                        checkLatest: true
                

                    - task: AWSShellScript@1
                      displayName: Package lambdas
                      env:
                        workingDir: "$(System.DefaultWorkingDirectory)/QA"
                      inputs:
                        awsCredentials: $(AwsCredentialsQA)
                        regionName: $(Region)
                        scriptType: 'inline'
                        inlineScript: |
                          echo $workingDir &&
                          sam build --debug \
                          -s . \
                          --template-file ./template.yaml
                    - task: AWSShellScript@1
                      displayName: Deploy Infrastructure
                      inputs:
                        awsCredentials: $(AwsCredentialsQA)
                        regionName: $(Region)
                        scriptType: "inline"
                        inlineScript: |                
                          sam deploy \
                          --template-file .aws-sam/build/template.yaml \
                          --no-confirm-changeset \
                          --no-fail-on-empty-changeset \
                          --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
                          --stack-name $(StackName) \
                          --s3-bucket $(AwsBucket) \
                          --s3-prefix $(StackName) \
                          --parameter-overrides ParameterKey=EeApiEndpoint,ParameterValue=$(EeApiEndpoint) ParameterKey=AbsenceApiEndpoint,ParameterValue=$(AbsenceApiEndpoint) ParameterKey=ConfigApiEndpoint,ParameterValue=$(ConfigApiEndpoint) ParameterKey=AuthApiEndpoint,ParameterValue=$(AuthApiEndpoint) ParameterKey=GrantType,ParameterValue=$(GrantType) ParameterKey=ScopeConfig,ParameterValue=$(ScopeConfig) ParameterKey=Scope,ParameterValue=$(Scope) ParameterKey=ClientId,ParameterValue=$(ClientId) ParameterKey=ClientSecret,ParameterValue=$(ClientSecret) ParameterKey=AwsDynamodbEndpoint,ParameterValue=$(AwsDynamodbEndpoint) ParameterKey=DefaultLanguage,ParameterValue=$(DefaultLanguage)  ParameterKey=IvrCallsTableName,ParameterValue=$(IvrCallsTableName)  ParameterKey=CacheTableName,ParameterValue=$(CacheTableName)  ParameterKey=ClientConfigTableName,ParameterValue=$(ClientConfigTableName) ParameterKey=ReasonMenuTableName,ParameterValue=$(ReasonMenuTableName) ParameterKey=SessionMonitorCallInterval,ParameterValue=$(SessionMonitorCallInterval) ParameterKey=LambdaExecutionRole,ParameterValue=$(LambdaExecutionRole) ParameterKey=SecurityGroupId,ParameterValue=$(SecurityGroupId) ParameterKey=SubnetId,ParameterValue=$(SubnetId) ParameterKey=InstanceId,ParameterValue=$(InstanceId) ParameterKey=Environment,ParameterValue=$(Environment) ParameterKey=RefreshLocalDBInMinutes,ParameterValue=$(RefreshLocalDBInMinutes)  ParameterKey=DocumentApiEndpoint,ParameterValue=$(DocumentApiEndpoint) ParameterKey=SesVMEmailId,ParameterValue=$(SesVMEmailId) ParameterKey=VoiceEmailBucket,ParameterValue=$(VoiceEmailBucket) \





  - stage: DeployUAT
    displayName: Deploy to UAT
    variables: 
    - group: ablarivr_UAT
    jobs:
      - deployment: BuildAndDeploy
        displayName: Build and Deploy
        environment: 'AbilitiApiIVRLambda-UAT'
        strategy:
            runOnce:
              deploy:
                  steps:
                    - checkout: self
                    - task: S3Upload@1
                      displayName: Create Deploy Bucket
                      inputs:
                        awsCredentials: $(AwsCredentialsUAT)
                        bucketName: $(AwsBucket)
                        regionName: $(Region)
                        globExpressions: "undefined"
                        createBucket: true

                    - task: NodeTool@0
                      inputs:
                        versionSource: 'spec'
                        versionSpec: '22.x'
                        checkLatest: true
                

                    - task: AWSShellScript@1
                      displayName: Package
                      env:
                        workingDir: "$(System.DefaultWorkingDirectory)/UAT"
                      inputs:
                        awsCredentials: $(AwsCredentialsUAT)
                        regionName: $(Region)
                        scriptType: 'inline'
                        inlineScript: |
                          echo $workingDir &&
                          sam build --debug \
                          -s . \
                          --template-file ./template.yaml
                    
                    - task: AWSShellScript@1
                      displayName: Deploy Infrastructure
                      inputs:
                        awsCredentials: $(AwsCredentialsUAT)
                        regionName: $(Region)
                        scriptType: "inline"
                        inlineScript: |                
                          sam deploy \
                          --template-file .aws-sam/build/template.yaml \
                          --no-confirm-changeset \
                          --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
                          --force-upload \
                          --stack-name $(StackName) \
                          --s3-bucket $(AwsBucket) \
                          --s3-prefix $(StackName) \
                          --parameter-overrides ParameterKey=EeApiEndpoint,ParameterValue=$(EeApiEndpoint) ParameterKey=AbsenceApiEndpoint,ParameterValue=$(AbsenceApiEndpoint) ParameterKey=ConfigApiEndpoint,ParameterValue=$(ConfigApiEndpoint) ParameterKey=AuthApiEndpoint,ParameterValue=$(AuthApiEndpoint) ParameterKey=GrantType,ParameterValue=$(GrantType) ParameterKey=ScopeConfig,ParameterValue=$(ScopeConfig) ParameterKey=Scope,ParameterValue=$(Scope) ParameterKey=ClientId,ParameterValue=$(ClientId) ParameterKey=ClientSecret,ParameterValue=$(ClientSecret) ParameterKey=AwsDynamodbEndpoint,ParameterValue=$(AwsDynamodbEndpoint) ParameterKey=DefaultLanguage,ParameterValue=$(DefaultLanguage)  ParameterKey=IvrCallsTableName,ParameterValue=$(IvrCallsTableName)  ParameterKey=CacheTableName,ParameterValue=$(CacheTableName)  ParameterKey=ClientConfigTableName,ParameterValue=$(ClientConfigTableName) ParameterKey=ReasonMenuTableName,ParameterValue=$(ReasonMenuTableName) ParameterKey=SessionMonitorCallInterval,ParameterValue=$(SessionMonitorCallInterval) ParameterKey=LambdaExecutionRole,ParameterValue=$(LambdaExecutionRole) ParameterKey=SecurityGroupId,ParameterValue=$(SecurityGroupId) ParameterKey=SubnetId,ParameterValue=$(SubnetId) ParameterKey=InstanceId,ParameterValue=$(InstanceId) ParameterKey=Environment,ParameterValue=$(Environment) ParameterKey=RefreshLocalDBInMinutes,ParameterValue=$(RefreshLocalDBInMinutes)  ParameterKey=DocumentApiEndpoint,ParameterValue=$(DocumentApiEndpoint) ParameterKey=SesVMEmailId,ParameterValue=$(SesVMEmailId) ParameterKey=VoiceEmailBucket,ParameterValue=$(VoiceEmailBucket) \
                          



  - stage: DeployPROD
    displayName: Deploy to PROD
    variables: 
    - group: ablarivr_PROD
    jobs:
      - deployment: BuildAndDeploy
        displayName: Build and Deploy
        environment: 'AbilitiApiIVRLambda-PROD'
        strategy:
            runOnce:
              deploy:
                  steps:
                    - checkout: self
                    - task: S3Upload@1
                      displayName: Create Deploy Bucket
                      inputs:
                        awsCredentials: $(AwsCredentialsPROD)
                        bucketName: $(AwsBucket)
                        regionName: $(Region)
                        globExpressions: "undefined"
                        createBucket: true

                    - task: NodeTool@0
                      inputs:
                        versionSource: 'spec'
                        versionSpec: '22.x'
                        checkLatest: true
                

                    - task: AWSShellScript@1
                      displayName: Package
                      env:
                        workingDir: "$(System.DefaultWorkingDirectory)/PROD"
                      inputs:
                        awsCredentials: $(AwsCredentialsPROD)
                        regionName: $(Region)
                        scriptType: 'inline'
                        inlineScript: |
                          echo $workingDir &&
                          sam build --debug \
                          -s . \
                          --template-file ./template.yaml
                    
                    - task: AWSShellScript@1
                      displayName: Deploy Infrastructure
                      inputs:
                        awsCredentials: $(AwsCredentialsPROD)
                        regionName: $(Region)
                        scriptType: "inline"
                        inlineScript: |                
                          sam deploy \
                          --template-file .aws-sam/build/template.yaml \
                          --no-confirm-changeset \
                          --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
                          --stack-name $(StackName) \
                          --s3-bucket $(AwsBucket) \
                          --s3-prefix $(StackName) \
                          --parameter-overrides ParameterKey=EeApiEndpoint,ParameterValue=$(EeApiEndpoint) ParameterKey=AbsenceApiEndpoint,ParameterValue=$(AbsenceApiEndpoint) ParameterKey=ConfigApiEndpoint,ParameterValue=$(ConfigApiEndpoint) ParameterKey=AuthApiEndpoint,ParameterValue=$(AuthApiEndpoint) ParameterKey=GrantType,ParameterValue=$(GrantType) ParameterKey=ScopeConfig,ParameterValue=$(ScopeConfig) ParameterKey=Scope,ParameterValue=$(Scope) ParameterKey=ClientId,ParameterValue=$(ClientId) ParameterKey=ClientSecret,ParameterValue=$(ClientSecret) ParameterKey=AwsDynamodbEndpoint,ParameterValue=$(AwsDynamodbEndpoint) ParameterKey=DefaultLanguage,ParameterValue=$(DefaultLanguage)  ParameterKey=IvrCallsTableName,ParameterValue=$(IvrCallsTableName)  ParameterKey=CacheTableName,ParameterValue=$(CacheTableName)  ParameterKey=ClientConfigTableName,ParameterValue=$(ClientConfigTableName) ParameterKey=ReasonMenuTableName,ParameterValue=$(ReasonMenuTableName) ParameterKey=SessionMonitorCallInterval,ParameterValue=$(SessionMonitorCallInterval) ParameterKey=LambdaExecutionRole,ParameterValue=$(LambdaExecutionRole) ParameterKey=SecurityGroupId,ParameterValue=$(SecurityGroupId) ParameterKey=SubnetId,ParameterValue=$(SubnetId) ParameterKey=InstanceId,ParameterValue=$(InstanceId) ParameterKey=Environment,ParameterValue=$(Environment) ParameterKey=RefreshLocalDBInMinutes,ParameterValue=$(RefreshLocalDBInMinutes)  ParameterKey=DocumentApiEndpoint,ParameterValue=$(DocumentApiEndpoint) ParameterKey=SesVMEmailId,ParameterValue=$(SesVMEmailId) ParameterKey=VoiceEmailBucket,ParameterValue=$(VoiceEmailBucket) \
                          

                
                
 