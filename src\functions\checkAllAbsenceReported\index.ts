'use strict';
import {AbsenceApiService, CallLog} from '@/services'
import { ConnectContactFlowEvent } from 'aws-lambda'

const callService = new CallLog();
const absenceApi = new AbsenceApiService();
export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'checkAllAbsenceReported';
    callService.Logger.log('Input data', event);
    let contactId= event.Details.ContactData.ContactId;;
    try {
        const callData = await callService.getCallDataAsync(contactId);
        const totalAbsenceDays = parseInt(callData.TotalAbsenceDays, 10);

        let currentAbsence = callData.Absences.peekLast();        
        const currentSessionReportedDays = currentAbsence?.AbsenceIncident?.AbsenceDates?.length || -1;        

        if (isNaN(currentSessionReportedDays) || currentSessionReportedDays < 0) {
            return {
                IsAllAbsReported: false
            }
        } 
        callService.Logger.log('Num Of Remaining Absence', totalAbsenceDays - currentSessionReportedDays);
        if(totalAbsenceDays - currentSessionReportedDays == 0){
        callService.Logger.log('Update with abs date in checkAllAbsReported for', currentAbsence.AbsenceIncident);
        let updatedIncident = await absenceApi.updateIncidentAsync(callData.ClientCode, contactId, currentAbsence.AbsenceIncident );
        callService.Logger.log('Update done with abs date in checkAllAbsReported for', currentAbsence.AbsenceIncident);
        if(updatedIncident.AbsenceIncident != null){
            currentAbsence = { ...currentAbsence, ...updatedIncident };
            callService.Logger.log('Update absence dates', JSON.stringify(currentAbsence.AbsenceIncident));
            callService.Logger.log('Update absence', JSON.stringify(currentAbsence.QuestionReponse));
            callData.Absences.pop();
            callData.Absences.push(currentAbsence);
            await callService.setCallDataAsync(contactId, {
                IsSaved: "true",
                Absences: callData.Absences
            });
        }        
    }
                  
           
           return{
                IsAllAbsReported: totalAbsenceDays <= currentSessionReportedDays,
                NumOfReportedAbsence: currentSessionReportedDays,
                NumOfRemainingAbsence: totalAbsenceDays - currentSessionReportedDays,
            };

    }
    catch (error: any) {
        callService.Logger.error('Error ', error);
        return {
            IsAllAbsReported: false,
            error: error.message
        };
    }


}