AWSTemplateFormatVersion : '2010-09-09'
Transform: 
  - AWS::Serverless-2016-10-31 
Description: DEV Abiliti IVR VM Stack.
Parameters:
  SesVMEmailId:
    Type: String
  SecurityGroupId:
    Type: String
  SubnetId:
    Type: String
Globals:
  Function:
    VpcConfig:
      SecurityGroupIds:
        -  !Ref SecurityGroupId #sg-066853a8514fbe24e
      SubnetIds:
         -  !Ref SubnetId #subnet-01d38b015e855905e  


  
Resources:
   # creating new bucket
  VoiceEmailBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    Properties:
      BucketName: "ablarivr-voiceemailbucket"
      Tags:
        - Key: LOB
          Value: ADM
        - Key: Project
          Value: Ablarivr
      LifecycleConfiguration:
        Rules:
          - Id: "DeleteAfterOneMonth"
            Status: "Enabled"
            ExpirationInDays: 120
            Prefix: "VoiceEmail/*"  # Apply to all objects
            
  VoiceEmailBucketPolicy:
    Type: AWS::S3::BucketPolicy    
    Properties:
      Bucket: !Ref VoiceEmailBucket
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AllowSesToWriteToS3
            Effect: Allow
            Principal: "ses.amazonaws.com"
            Action: "s3:PutObject"
            Resource: !Sub "arn:aws:s3:::${VoiceEmailBucket}/VoiceEMail/*" 
          # - Effect: Allow
          #   Principal:
          #     Service: "*"
          #   Action: "s3:PutObject"
          #   Resource: !Sub "${VoiceEmailBucket.Arn}/VoiceEMail/*"
  