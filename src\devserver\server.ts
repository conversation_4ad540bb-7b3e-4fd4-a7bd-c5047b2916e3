// eslint-disable-next-line no-console
import express, { Express, Request, Response } from 'express'
import {LogService, PromptHelper} from '@/util'
import {handler as authHandler} from '@/functions/authenticateEmployee'
import {handler as validateHandler} from '@/functions/validateEmployeeId'
import {handler as rtwFlagHandler} from '@/functions/setReturnToWork'
import {handler as totalAbsHandler} from '@/functions/saveTotalAbsenceDays'
import {handler as validAbsDateHandler} from '@/functions/validateAbsenceDate'
import {handler as saveAbsDateHandler} from '@/functions/saveAbsenceDate'
import {handler as shiftStartHandler} from '@/functions/validateShiftStartTime'
import {handler as shiftEndHandler} from '@/functions/validateShiftEndTime'
import {handler as rtwDateHandler} from '@/functions/validateReturnToWorkDate'
import {handler as saveRtwDateHandler} from '@/functions/saveReturnToWorkDate'
import {handler as rtwShiftHandler} from '@/functions/validateRTWShiftTime'
import {handler as allAbsenceReportedHandler} from '@/functions/checkAllAbsenceReported'
import {handler as openMsgHandler} from '@/functions/getOpeningMsgV1'
import {handler as empPhoneValidateHandler} from '@/functions/validateEmployeePhoneNumber'
import {handler as submitAbsenceHandler} from '@/functions/submitAbsence'
import {handler as clearabsenceHandler} from '@/functions/clearLastAddedAbsenceDate'
import { reasonMenuHandler, secondaryReasonHandler, saveSecondaryHandler, resolveConflictHandler, clientConfigHandler, droppedSessionHandler, policyHandler, saveThirdLevelHandler, checkOpenCasesHandler, checkCanBeLinkedHandler, checkEEStdHandler, saveSTdHandler, saveLeaveResponseHandler, vmCallbackHandler } from '@/functions'
import {LocalConfiguration, EmployeeApiService, AbsenceReportingService, AbsenceApiService, CallLog, ManageApiService, DocumentApiService} from '@/services'
import { ConnectContactFlowEvent } from 'aws-lambda'
import { AbsenceIncident, ParsedVoiceMail } from '@/models'
import { CancelAbsenceModel } from '@/models/AbsenceModel'
import fs from "fs";
import { AttachmentModel } from '@/models/VoiceMailModel'

//&console.log('Hello world!');

const app: Express = express();
const port: number = 5505;

app.get('/', (req: Request, res: Response)=>{
    res.send('Hello, this is Express + TypeScript');
});

app.get('/testLog',(req: Request, res: Response)=>{
    let log = new LogService();
    log.log('Called log!');
    res.send('calling log');
});

app.get('/testconfigService',async (req: Request, res: Response)=>{
    let config = new LocalConfiguration();
    //&console.log(await config.getConfigurationAsync('authCache_test123'));
    res.send('calling log');
});

app.get('/testEmployeeService',async (req: Request, res: Response)=>{
    let config = new EmployeeApiService();
    //console.log(await config.getEmployeeAsync('kent','80083033','94a6748e-4aee-4c7b-b18d-0b8a33dd83b5'));
    console.log(await config.getEmployeeClaims('disd',  '89a9d991-cbed-4b83-bab5-35d38cbf5247', '100419' ))
    res.send('calling log');
});
app.get('/testabsenceReportingService',async (req: Request, res: Response)=>{
  let config = new AbsenceReportingService();
  ////&console.log(await config.getEmployeeDataAsync('e6gkw3e3-a66b-4362-af33-65c752a6844d','kent','80083033', '03091979', 'MMDDYYYY'));
  //&console.log(await config.getAbsenceDatesAsync('e6gkw3e3-a66b-4362-af33-65c752a6844d'));
  res.send('calling log');
});


app.get('/lambdas/getClientConfig',async (req: Request, res: Response)=>{
  let msg: any = {};
  let opt = req.query.opt;
  const event: any  =  {
    Details : {
     ContactData: {
      ContactId: '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      Attributes: {},
      },
      Parameters : {
        ClientCode: 'kent',
        Language: 'en'
      }
  }
    };
  try {
      msg = await clientConfigHandler(event);
  }
  catch (err: any) {
      msg = err;
  }

  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});

app.get('/lambdas/authenticateEmployee',async (req: Request, res: Response)=>{
   
    const event: any  =  {
      "Details": {
          "ContactData": {
              "Attributes": {
                  "PromptSubOpeningSSML1": "<speak><amazon:domain name='conversational'>Kindly note you can record your absences for 60 days in future OR within previous 2 days. Please note that you can report a maximum of 60 absences per call. You can also report your absences online via Abiliti Absence at kent.abilitiabsenceus.com. A confirmation number will be provided to you at the end of the call.</amazon:domain></speak>",
                  "DefaultDateFormatType": "1",
                  "LambdaValidateEmployeeIdARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateEmployeeId",
                  "PromptLanugageSelectionS3URI": "",
                  "LambdaValidateEmployeePhoneNumberARN": "",
                  "LambdaSubmitAbsenceARN": "",
                  "PromptEnterEmployeeIdSSML": "<speak><amazon:domain name='conversational'> Please enter your employee I D using your keypad followed by pound key. If you do not know your employee I D, please disconnect and contact your employer to report your absence.</amazon:domain></speak>",
                  "LambdaGetPrimaryReasonMenuARN": "",
                  "LambdaValidateAbsenceDateARN": "",
                  "LambdaValidateShiftStartTimeARN": "",
                  "LambdaValidateShiftEndTimeARN": "",
                  "CallFlowEnterReasonARN": "",
                  "CallFlowSubmitAbsenceIfErrorARN": "",
                  "LambdaSaveSecondaryReasonARN": "",
                  "MaxTotalMissedAbsenceDays": "5",
                  "LambdaAuthenticateEmployeeARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-AuthenticateEmployee",
                  "IsEnterEmployeePhoneNumberRequired": "true",
                  "LambdaSetReturnToWorkARN": "",
                  "LambdaValidateReturnToWorkDateARN": "",
                  "MaxRetryMenu1": "3",
                  "MaxRetryMenu2": "",
                  "Marker1": "test",
                  "Marker": "Add Value here",
                  "CallFlowClosingARN": "",
                  "MaxRetryLambda2": "3",
                  "LambdaGetClientConfigARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetClientConfig-temppoc",
                  "MaxRetryLambda1": "3",
                  "Environment": "LAB",
                  "CallFlowGetOpeningMsgV1ARN": "ec9c237c-1a16-4eed-a729-66061cdf0e53",
                  "CallFlowEnterEmployeePhoneNumberARN": "",
                  "PromptSpeicalOpeningMsgSSML": "",
                  "MaxAllowAbsenceDaysPast": "14",
                  "LambdaValidateDoBARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateDoB",
                  "PromptMaxInvalidEmployeeIdSSML": "<speak><amazon:domain name='conversational'>Sorry, your absence cannot be recorded without your correct Employee number Please call back later Goodbye.</amazon:domain></speak>",
                  "DefaultTimeFormatType": "1",
                  "CallFlowTransferToCSRARN": "",
                  "PreviousAbsenceWithinAA": "2",
                  "LambdaGetSecondaryReasonMenuARN": "",
                  "MaxAllowAbsenceDaysFuture": "60",
                  "LambdaCheckAllAbsReportedARN": "",
                  "CallFlowEnterAbsenceDateARN": "",
                  "IsLambdaMaxErrorRetry": "false",
                  "PromptInvalidEmployeeIdSSML1": "<speak><amazon:domain name='conversational'>The Employee ID you entered is</amazon:domain></speak>",
                  "IsEmployeeIdValid": "true",
                  "PromptInvalidEmployeeIdSSML2": "<speak><amazon:domain name='conversational'>This does not match with our records. Please try again</amazon:domain></speak>",
                  "IsDoBValid": "true",
                  "LambdaValidateRTWShiftStartTimeARN": "",
                  "CallFlowValidateEmployeeARN": "f5467b78-f173-46fc-83b0-06391bdfc027",
                  "IsMenuMaxInvalidRetry": "false",
                  "IsAllowInteruppted": "false",
                  "MutliLanguageId": "0",
                  "LambdaSaveTotalAbsenceDaysARN": "",
                  "LambdaGetCarrierTransferNumberARN": "",
                  "EmployeeId": "80083033",
                  "PromptUnexpectedErrorSSML": "put ssml here",
                  "DefaultLanguage": "English",
                  "CallFlowDynamicFlowV1ARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/contact-flow/78498ff6-24e1-433c-a281-0aed5c8cc131",
                  "LambdaGetOpeningMsgV1ARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetOpeningMsgV1-temppoc",
                  "PromptOpeningSSML": "<speak><amazon:domain name='conversational'>Thank you for calling the kent Absence Reporting System. Please note that effective 1/1/2023, UNUM administers leave under the Family Medical Leave Act (FMLA) and time away from work due to Short Term Disability (STD) and Accident and Sickness. Please listen carefully as the options might have changed.</amazon:domain></speak>",
                  "CallFlowEnterTotalAbsenceDaysARN": "",
                  "LambdaSaveAbsenceDateARN": "",
                  "CallFlowSubmitAbsenceARN": "",
                  "CallFlowEnterRTWARN": "",
                  "CallFlowTransferToExternalARN": "",
                  "PromptTransferNumberGeneralErrorSSML": ""
              },
              "Channel": "VOICE",
              "ContactId": "94a6748e-4aee-4c7b-b18d-0b8a33dd83b5",
              "CustomerEndpoint": {
                  "Address": "+16782033318",
                  "Type": "TELEPHONE_NUMBER"
              },
              "CustomerId": null,
              "Description": null,
              "InitialContactId": "1311fe86-75c1-4f5c-932d-a64f3697eadb",
              "InitiationMethod": "INBOUND",
              "InstanceARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330",
              "LanguageCode": "en-US",
              "MediaStreams": {
                  "Customer": {
                      "Audio": null
                  }
              },
              "Name": null,
              "PreviousContactId": "1311fe86-75c1-4f5c-932d-a64f3697eadb",
              "Queue": null,
              "References": {},
              "SystemEndpoint": {
                  "Address": "+16472438396",
                  "Type": "TELEPHONE_NUMBER"
              }
          },
          "Parameters": {
              "ClientCode": "KENT",
              "DateOfBirth": "07271964",
              "DoBFormatType": "1",
              "ClientName": "",
              "EmployeeId": "9042"
          }
      },
      "Name": "ContactFlowEvent"
  };
      
    const msg = await authHandler(event);
    res.setHeader('Content-Type', 'application/json');
    res.send(msg);
});

app.get('/lambdas/validateEmployee',async (req: Request, res: Response)=>{
    let msg: any = {};
    const event: any  =  {
      Details : {
       ContactData: {
        Attributes: {},
        ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
        },
        Parameters : {
          ClientCode: 'kent',
          
          EmployeeId: '300650'
        }
    }
      };
    try {
        msg = await validateHandler(event);
    }
    catch (err: any) {
        msg = err;
    }
  
    res.setHeader('Content-Type', 'application/json');
    res.send(msg);
});

app.get('/lambdas/setReturnToWork',async (req: Request, res: Response)=>{
   
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        IsReturnToWork: 'true'
      }
  }
    };
    
  const msg = await rtwFlagHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});

app.get('/lambdas/saveTotalAbsenceDays',async (req: Request, res: Response)=>{
   
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        TotalAbsenceDays: '1',
        ClientName: 'kent'
      }
  }
    };
    
  const msg = await totalAbsHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});

app.get('/lambdas/resolveConflictAbsence',async (req: Request, res: Response)=>{
   
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
       ResolveAction: 2
      }
  }
    };
    
  const msg = await resolveConflictHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});

app.get('/lambdas/primaryReasonMenu',async (req: Request, res: Response)=>{
  let msg: any = {};
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        ClientCode: 'kent',
        
      }
  }
    };
  try {
      msg = await reasonMenuHandler(event);
  }
  catch (err: any) {
      msg = err;
  }

  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});


app.get('/lambdas/secondaryReasonMenu',async (req: Request, res: Response)=>{
  let msg: any = {};
  let reasonOpt = req.query.opt;
  const event: any  =  {
    Details : {
     ContactData: {
      ContactId: 'cea0a282-3e64-475e-a3cf-bf0ebe844e44',
      Attributes: {},
      },
      Parameters : {
        ClientCode: 'kent',
        SelectedPrimaryReason: reasonOpt || 1
      }
  }
    };
  try {
      msg = await secondaryReasonHandler(event);
  }
  catch (err: any) {
      msg = err;
  }

  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});

app.get('/lambdas/saveSecondaryReason',async (req: Request, res: Response)=>{
  let msg: any = {};
  let opt = req.query.opt;
  const event: any  =  {
    Details : {
     ContactData: {
      ContactId: 'cea0a282-3e64-475e-a3cf-bf0ebe844e44',
      Attributes: {},
      },
      Parameters : {
        ClientCode: 'kent',
        SelectedSecondaryReason: opt || 2
      }
  }
    };
  try {
      msg = await saveSecondaryHandler(event);
  }
  catch (err: any) {
      msg = err;
  }

  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});


app.get('/lambdas/validateAbsenceDate',async (req: Request, res: Response)=>{
  let date=req.query.date;
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '47ba5d61-61bf-4383-818a-09e81881dc17',
      },
      Parameters : {
        ReportAbsenceDate: date || '10252024',//'05092023', '05102023'
        AbsenceDateFormatType: 'MMDDYYYY',
        IsContinuousFullDaySelected: 'true'
      }
  }
    };
    
  const msg = await validAbsDateHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});

app.get('/lambdas/saveAbsenceDate',async (req: Request, res: Response)=>{
   
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        ReportAbsenceDate: '07292024',//'05092023', '05102023'
      }
  }
    };
    
  const msg = await saveAbsDateHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});

app.get('/lambdas/validateShiftEndTime',async (req: Request, res: Response)=>{
   
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        ShiftEndTime: '0500',
        AMorPM: 2
      }
  }
    };
    
  const msg = await shiftEndHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});

app.get('/lambdas/validateShiftStartTime',async (req: Request, res: Response)=>{
   
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  'cea0a282-3e64-475e-a3cf-bf0ebe844e44',
      },
      Parameters : {
        ShiftStartTime: '0900',
        AMorPM: 1
      }
  }
    };
    
  const msg = await shiftStartHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});

app.get('/lambdas/checkCanBeLinked',async (req: Request, res: Response)=>{
   
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
      
      }
  }
    };
    
  const msg = await checkCanBeLinkedHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});


app.get('/lambdas/validateRtwDate',async (req: Request, res: Response)=>{
   
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        ReturnDayToWork: '02022024',//'09172023'  '09182023'   '09162023'
        RTWDateFormatType: '1'
      }
  }
    };
    
  const msg = await rtwDateHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});

app.get('/lambdas/saveRtwDate',async (req: Request, res: Response)=>{
   
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        ReturnDayToWork: '02022024'
      }
  }
    };
    
  const msg = await saveRtwDateHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});

app.get('/lambdas/validateRtwShiftTime',async (req: Request, res: Response)=>{
   
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        RTWShiftStartTime: '0930',
        AMorPM: 1
      }
  }
    };
    
  const msg = await rtwShiftHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});

app.get('/lambdas/checkAllAbsenceReported',async (req: Request, res: Response)=>{
   
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      }
  }
    };
    
  const msg = await allAbsenceReportedHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
});


app.get('/lambdas/getOpeningMsgV1',async (req: Request, res: Response)=>{
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        ClientCode: 'TESTIVRCLIENT',
        ClientName: 'TESTIVRCLIENT'
      }
  }
    };
    const msg = await openMsgHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
    
});

app.get('/lambdas/savequestion',async (req: Request, res: Response)=>{
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        ClientCode: 'kent',
        SelectedThirdLevelResponse: 1
      }
  }
    };
    const msg = await saveThirdLevelHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
    
});
app.get('/lambdas/checkSTD',async (req: Request, res: Response)=>{
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
       
      }
  }
    };
    const msg = await checkEEStdHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
    
});
app.get('/lambdas/saveSTDOption',async (req: Request, res: Response)=>{
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        ClientCode: 'kent',
        SelectedSTDResponse: 1
      }
  }
    };
    const msg = await saveSTdHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
    
});
app.get('/lambdas/checkOpenCases',async (req: Request, res: Response)=>{
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
      }
  }
    };
    const msg = await checkOpenCasesHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
    
});
app.get('/lambdas/saveLeaveResponse',async (req: Request, res: Response)=>{
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        ClientCode: 'kent',
        AssociateClaim: 'false',
        SelectedClaimOption: null,
        NewLeaveClaimOption: 1,
      }
  }
    };
    const msg = await saveLeaveResponseHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
    
});

app.get('/lambdas/validateEmployeePhoneNumber',async (req: Request, res: Response)=>{
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        EmployeePhoneNumber: '+17347641818'
      }
  }
    };
    const msg = await empPhoneValidateHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
    
});
app.get('/lambdas/submitAbsence',async (req: Request, res: Response)=>{
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        IsRTWKnown: '',
        AssociateClaim: 'false',
        SelectedClaimOption: null,
        ConfirmAbsence: 'true',
        NewLeaveClaimOption: 'true',
        SelectedAbsenceTreatmentOption: 'null'
      }
  }
    };
    const msg = await submitAbsenceHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
    
});


app.get('/lambdas/clearAbsence',async (req: Request, res: Response)=>{
  const event: any  =  {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        ReportAbsenceDate: '04182023',
        AbsenceDateFormatType: 'MMDDYYYY'
      }
  }
    };
    const msg = await clearabsenceHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
    
});
app.get('/lambdas/droppedSessionMonitor',async (req: Request, res: Response)=>{
  const event: any  =  {    };
    const msg = await droppedSessionHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
    
});
app.get('/testCallLogService', async (req: Request, resp: Response) =>{
let service = new CallLog();

const response = await service.getOpenedSessionsAsync();
console.log(response);
resp.send('calling log');
});

app.get('/lambdas/getPolicyBalance',async (req: Request, res: Response)=>{
  const event: any  =   {
    Details : {
     ContactData: {
      Attributes: {},
      ContactId:  '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5',
      },
      Parameters : {
        ClientCode: '04182023',
        AbsenceDateFormatType: 'MMDDYYYY'
      }
  }
    };
    const msg = await policyHandler(event);
  res.setHeader('Content-Type', 'application/json');
  res.send(msg);
    
});
app.get('/testAbsenceApiService',async (req: Request, res: Response)=>{
  let service = new AbsenceApiService();
  let newAbsence : AbsenceIncident= {
    AbsenceIncidentId: 22622,
    EmployeeNumber: "02291569",
    ClientCode: "kent",
    FirstName: "tara",
    LastName: "smith",
    BestPhoneNumber: "6047163449",
    ProvinceCode: "ON",
    ReturnToWorkDate: "2023-03-21T21:12:23.159Z",
    NextScheduledShift: "2023-03-21T21:12:23.159Z",
    RequestDate: "2023-04-24T14:55:58.0081306Z",
    PrimaryReason: "72bd21d7-efc8-45ba-bf23-1fea48fa002b",
    SecondaryReason: "5818e815-3466-470c-87f2-2258b5a7ef80",
    AbsenceType: "continuous",
    ReportedBy: "wolverineadmin",
    ReportingLocale: "en",
    AbsenceDates : [
      {
        ShiftStartTime: "2023-05-24 09:30:00",
        ShiftEndTime: "2023-05-24 09:30:00",
        ShiftDuration: "0",
        IsPartialAbsence: false,
        ScheduledShiftStartTime: "",
        ScheduledShiftEndTime: "",
        UnpaidTimeInMinutes: 0
      }
    ],
    QuestionResponses: {},
    LinkedIncident: false
  }
  let ids: number[] = [22722,22724,22729];
  let reqst : CancelAbsenceModel = {
    CancelComment: "IVR call drop",
    CancelReason: "IVR call drop"
  }
   const saveResponse = await service.createIncidentAsync('kent', '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5', newAbsence);
 // const saveResponse = await service.submitIncidentAsync('kent', '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5', newAbsence);
 //const deleteResponse = await service.cancelIncidentsAsync(ids, reqst );
  console.log(saveResponse)
  res.send('calling log');
});
app.get('/testSaveMapResponse',async (req: Request, res: Response)=>{
  let service = new AbsenceApiService();
  let reponse = {
    "returnValue": {
      "absenceIncidentModel": {
          "absenceIncidentId": 38032,
          "employeeNumber": "100419",
          "clientCode": "DISD",
          "firstName": "KAILYN",
          "lastName": "BRADLEY",
          "bestPhoneNumber": "4693867454",
          "provinceCode": "TX",
          "returnToWorkDate": null,
          "nextScheduledShift": "2023-06-30T21:12:23.159Z",
          "requestDate": "2023-06-29T21:12:23.159Z",
          "primaryReason": "5e67d327-c3c1-4ad2-bbef-a129003cb906",
          "secondaryReason": "f7228026-5979-4aa5-aa85-adc703c4dce1",
          "absenceType": "continuous",
          "reportedBy": "wolverineadmin",
          "absenceDates": [
              {
                  "shiftStartTime": "2023-09-05T08:00:00-04:00",
                  "shiftEndTime": "2023-09-05T17:00:00-04:00",
                  "shiftDuration": null,
                  "isPartialAbsence": false,
                  "scheduledShiftStartTime": null,
                  "scheduledShiftEndTime": null,
                  "unpaidTimeInMinutes": null
              }
          ],
          "questionResponses": {},
          "linkedIncident": false,
          "claimStatus": "I",
          "maxDaysAllowed": 600,
          "maxReportableDays": 600,
          "attachments": []
      },
      "absenceQuestionReturnModel": {
          "preQualifyingQuestions": [],
          "questions": [
              {
                  "pageId": "1FA",
                  "pageHeading": [],
                  "pageHeadingDescription": [],
                  "sections": [
                      {
                          "sectionId": "1FA",
                          "sectionHeading": [],
                          "sectionHeadingDescription": [],
                          "questions": [
                              {
                                  "name": "CQ_ASK_FOR_EXISTING_LEAVE_CASE",
                                  "label": [
                                      {
                                          "locale": "EN",
                                          "description": "Is it associated with any of the following Leave Cases?",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "FR",
                                          "description": "Do you want to associate this incident with any of open Leave Case ? - FR",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "ES",
                                          "description": "Do you want to associate this incident with any of open Leave Case ?",
                                          "shortDescription": ""
                                      }
                                  ],
                                  "longDescription": [
                                      {
                                          "locale": "EN",
                                          "description": "We have detected that you have open Leave cases, is today's reporting associated with any of these?",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "FR",
                                          "description": "Nous avons détecté que vous avez des dossiers de congé ouverts, le rapport d'aujourd'hui est-il associé à l'un d'entre eux?",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "ES",
                                          "description": "We have detected that you have open Leave cases, is today's reporting associated with any of these?",
                                          "shortDescription": ""
                                      }
                                  ],
                                  "placeholder": [],
                                  "source": "COMPANY_SPECIFIC",
                                  "questionType": "card",
                                  "defaultValue": null,
                                  "resolvedValue": null,
                                  "isVisible": true,
                                  "isReadOnly": false,
                                  "reloadIncidentData": true,
                                  "options": [
                                      {
                                          "text": [],
                                          "value": "0"
                                      },
                                      {
                                          "text": [
                                              {
                                                  "locale": "EN",
                                                  "description": "Provide care for a military service member",
                                                  "shortDescription": ""
                                              }
                                          ],
                                          "value": "314022"
                                      },
                                      {
                                          "text": [
                                              {
                                                  "locale": "EN",
                                                  "description": "Provide care for a military service member",
                                                  "shortDescription": ""
                                              }
                                          ],
                                          "value": "314023"
                                      },
                                      {
                                          "text": [
                                              {
                                                  "locale": "EN",
                                                  "description": "Provide care for a military service member",
                                                  "shortDescription": ""
                                              }
                                          ],
                                          "value": "314024"
                                      }
                                  ],
                                  "config": {
                                      "changeMonth": null,
                                      "changeYear": null,
                                      "maxDate": null,
                                      "linkedField": null,
                                      "url": null,
                                      "maxLength": null,
                                      "allowDecimals": null
                                  },
                                  "validationConstraint": {
                                      "required": false,
                                      "number": null,
                                      "min": null,
                                      "max": null,
                                      "digits": null,
                                      "compare": null
                                  },
                                  "visibilityConditions": [],
                                  "alerts": []
                              },
                              {
                                  "name": "CQ_APPLY_FOR_LEAVE_REFERRAL",
                                  "label": [
                                      {
                                          "locale": "EN",
                                          "description": "Do you want to apply for a leave of absence?\nYes should be selected if you need a FML, GML, Military or Hardship Leave",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "FR",
                                          "description": "Do you want to apply for a leave of absence?\nYes should be selected if you need a FML, GML, Military or Hardship Leave - FR",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "ES",
                                          "description": "Do you want to apply for a leave of absence?\nYes should be selected if you need a FML, GML, Military or Hardship Leave",
                                          "shortDescription": ""
                                      }
                                  ],
                                  "longDescription": [],
                                  "placeholder": [],
                                  "source": "COMPANY_SPECIFIC",
                                  "questionType": "boolean",
                                  "defaultValue": "true",
                                  "resolvedValue": null,
                                  "isVisible": true,
                                  "isReadOnly": false,
                                  "reloadIncidentData": true,
                                  "options": [],
                                  "config": {
                                      "changeMonth": null,
                                      "changeYear": null,
                                      "maxDate": null,
                                      "linkedField": null,
                                      "url": null,
                                      "maxLength": null,
                                      "allowDecimals": null
                                  },
                                  "validationConstraint": {
                                      "required": true,
                                      "number": null,
                                      "min": null,
                                      "max": null,
                                      "digits": null,
                                      "compare": null
                                  },
                                  "visibilityConditions": [],
                                  "alerts": []
                              }
                          ]
                      }
                  ]
              },
              {
                  "pageId": "1C",
                  "pageHeading": [],
                  "pageHeadingDescription": [],
                  "sections": [
                      {
                          "sectionId": "1C",
                          "sectionHeading": [],
                          "sectionHeadingDescription": [],
                          "questions": [
                              {
                                  "name": "CQ_PaySupplement",
                                  "label": [
                                      {
                                          "locale": "EN",
                                          "description": "A Paid leave of absence uses available PTO time. Do you want to use your available PTO for this leave of absence?",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "FR",
                                          "description": "A Paid leave of absence uses available PTO time. Do you want to use your available PTO for this leave of absence?",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "ES",
                                          "description": "A Paid leave of absence uses available PTO time. Do you want to use your available PTO for this leave of absence?",
                                          "shortDescription": ""
                                      }
                                  ],
                                  "longDescription": [],
                                  "placeholder": [],
                                  "source": "COMPANY_SPECIFIC",
                                  "questionType": "dropdown",
                                  "defaultValue": null,
                                  "resolvedValue": null,
                                  "isVisible": true,
                                  "isReadOnly": false,
                                  "reloadIncidentData": false,
                                  "options": [
                                      {
                                          "text": [
                                              {
                                                  "locale": "EN",
                                                  "description": "No",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "FR",
                                                  "description": "No",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "ES",
                                                  "description": "No",
                                                  "shortDescription": ""
                                              }
                                          ],
                                          "value": "false"
                                      },
                                      {
                                          "text": [
                                              {
                                                  "locale": "EN",
                                                  "description": "Yes - Compensatory Time (COMP)",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "FR",
                                                  "description": "Yes - Compensatory Time (COMP)",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "ES",
                                                  "description": "Yes - Personal Business (PBUS)",
                                                  "shortDescription": ""
                                              }
                                          ],
                                          "value": "true 2"
                                      },
                                      {
                                          "text": [
                                              {
                                                  "locale": "EN",
                                                  "description": "Yes - Personal Business (PBUS)",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "FR",
                                                  "description": "Yes - Personal Business (PBUS)",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "ES",
                                                  "description": "Yes - Personal Business (PBUS)",
                                                  "shortDescription": ""
                                              }
                                          ],
                                          "value": "true 4"
                                      },
                                      {
                                          "text": [
                                              {
                                                  "locale": "EN",
                                                  "description": "Yes - Personal Illness (PILL)",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "FR",
                                                  "description": "Yes - Personal Illness (PILL)",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "ES",
                                                  "description": "Yes - Personal Illness (PILL)",
                                                  "shortDescription": ""
                                              }
                                          ],
                                          "value": "true 5"
                                      },
                                      {
                                          "text": [
                                              {
                                                  "locale": "EN",
                                                  "description": "Yes - Flex Time",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "FR",
                                                  "description": "Yes - Flex Time",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "ES",
                                                  "description": "Yes - Flex Time",
                                                  "shortDescription": ""
                                              }
                                          ],
                                          "value": "true 6"
                                      },
                                      {
                                          "text": [
                                              {
                                                  "locale": "EN",
                                                  "description": "Yes - Fair Day",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "FR",
                                                  "description": "Yes - Fair Day",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "ES",
                                                  "description": "Yes - Fair Day",
                                                  "shortDescription": ""
                                              }
                                          ],
                                          "value": "true 7"
                                      },
                                      {
                                          "text": [
                                              {
                                                  "locale": "EN",
                                                  "description": "Yes - Vacation",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "FR",
                                                  "description": "Yes - Vacation",
                                                  "shortDescription": ""
                                              },
                                              {
                                                  "locale": "ES",
                                                  "description": "Yes - Vacation",
                                                  "shortDescription": ""
                                              }
                                          ],
                                          "value": "true 8"
                                      }
                                  ],
                                  "config": {
                                      "changeMonth": null,
                                      "changeYear": null,
                                      "maxDate": null,
                                      "linkedField": null,
                                      "url": null,
                                      "maxLength": null,
                                      "allowDecimals": null
                                  },
                                  "validationConstraint": {
                                      "required": true,
                                      "number": null,
                                      "min": null,
                                      "max": null,
                                      "digits": null,
                                      "compare": null
                                  },
                                  "visibilityConditions": [],
                                  "alerts": []
                              }
                          ]
                      }
                  ]
              },
              {
                  "pageId": "1F",
                  "pageHeading": [],
                  "pageHeadingDescription": [],
                  "sections": [
                      {
                          "sectionId": "1F",
                          "sectionHeading": [],
                          "sectionHeadingDescription": [],
                          "questions": [
                              {
                                  "name": "CQ_BEST_PHONE_NUMBER_TO_BE_REACHED",
                                  "label": [
                                      {
                                          "locale": "EN",
                                          "description": "What is the best phone number to reach you at?",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "FR",
                                          "description": "What is the best phone number to reach you at? - FR",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "ES",
                                          "description": "What is the best phone number to reach you at?",
                                          "shortDescription": ""
                                      }
                                  ],
                                  "longDescription": [],
                                  "placeholder": [],
                                  "source": "COMPANY_SPECIFIC",
                                  "questionType": "phone",
                                  "defaultValue": null,
                                  "resolvedValue": null,
                                  "isVisible": true,
                                  "isReadOnly": false,
                                  "reloadIncidentData": false,
                                  "options": [],
                                  "config": {
                                      "changeMonth": null,
                                      "changeYear": null,
                                      "maxDate": null,
                                      "linkedField": null,
                                      "url": null,
                                      "maxLength": null,
                                      "allowDecimals": null
                                  },
                                  "validationConstraint": {
                                      "required": true,
                                      "number": null,
                                      "min": null,
                                      "max": null,
                                      "digits": null,
                                      "compare": null
                                  },
                                  "visibilityConditions": [],
                                  "alerts": []
                              },
                              {
                                  "name": "CQ_RECEIVE_EMAILS",
                                  "label": [
                                      {
                                          "locale": "EN",
                                          "description": "Do you want to receive an email confirmation of your submission?",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "FR",
                                          "description": "Do you want to receive an email confirmation of your submission? - FR",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "ES",
                                          "description": "Do you want to receive an email confirmation of your submission?",
                                          "shortDescription": ""
                                      }
                                  ],
                                  "longDescription": [],
                                  "placeholder": [],
                                  "source": "COMPANY_SPECIFIC",
                                  "questionType": "boolean",
                                  "defaultValue": null,
                                  "resolvedValue": null,
                                  "isVisible": true,
                                  "isReadOnly": false,
                                  "reloadIncidentData": true,
                                  "options": [],
                                  "config": {
                                      "changeMonth": null,
                                      "changeYear": null,
                                      "maxDate": null,
                                      "linkedField": null,
                                      "url": null,
                                      "maxLength": null,
                                      "allowDecimals": null
                                  },
                                  "validationConstraint": {
                                      "required": true,
                                      "number": null,
                                      "min": null,
                                      "max": null,
                                      "digits": null,
                                      "compare": null
                                  },
                                  "visibilityConditions": [],
                                  "alerts": []
                              },
                              {
                                  "name": "CQ_EMAIL_ADDRESS_TO_RECEIVE",
                                  "label": [
                                      {
                                          "locale": "EN",
                                          "description": "Please provide your personal email address to receive correspondence related to your leave of absence",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "FR",
                                          "description": "Please provide your personal email address to receive correspondence related to your leave of absence - FR",
                                          "shortDescription": ""
                                      },
                                      {
                                          "locale": "ES",
                                          "description": "Please provide your personal email address to receive correspondence related to your leave of absence",
                                          "shortDescription": ""
                                      }
                                  ],
                                  "longDescription": [],
                                  "placeholder": [],
                                  "source": "COMPANY_SPECIFIC",
                                  "questionType": "email",
                                  "defaultValue": null,
                                  "resolvedValue": null,
                                  "isVisible": true,
                                  "isReadOnly": false,
                                  "reloadIncidentData": false,
                                  "options": [],
                                  "config": {
                                      "changeMonth": null,
                                      "changeYear": null,
                                      "maxDate": null,
                                      "linkedField": null,
                                      "url": null,
                                      "maxLength": null,
                                      "allowDecimals": null
                                  },
                                  "validationConstraint": {
                                      "required": false,
                                      "number": null,
                                      "min": null,
                                      "max": null,
                                      "digits": null,
                                      "compare": null
                                  },
                                  "visibilityConditions": [],
                                  "alerts": []
                              }
                          ]
                      }
                  ]
              }
          ],
          "reasonOfAbsenceAlerts": []
      }
  },
  "validationErrors": [],
  "success": true
  }
 
   const saveResponse = await service.MapResponseToSaveAbsenceIncident(reponse);
 // const saveResponse = await service.submitIncidentAsync('kent', '94a6748e-4aee-4c7b-b18d-0b8a33dd83b5', newAbsence);
 //const deleteResponse = await service.cancelIncidentsAsync(ids, reqst );
  console.log(saveResponse)
  res.send('calling log');
});
app.get('/testDocApiService',async (req: Request, res: Response)=>{
  let service = new DocumentApiService();
  const filePath = "C:\\git\\abiliti-absenceapi-ivrlambdas\\CF-Data-Samples\\voicemail.wav";
  const fileBuffer = fs.readFileSync(filePath);
  const testAttachment: AttachmentModel = {
    FileName: "voicemail.wav",
    ContentType: "audio/wav",
    Content: fileBuffer, // Assign the buffer
    Size: fileBuffer.length
};
const testEmail: ParsedVoiceMail = {
  Body: "f0c7fa01-5fb7-412c-9843-c442003f8f54",
  Subject: "Test Subject",
  Attachment: []
};
let absence: AbsenceIncident =
{  
    AbsenceDates: [
     {
      EndDate: "2025-03-05T12:00:00-05:00",
      IncidentId: 47602,
      IsOverLap: false,
      IsUpdated: false,
      ScheduledShiftEndTime: "2025-03-05T17:00:00-05:00",
      ScheduledShiftStartTime: "2025-03-05T09:00:00-05:00",
      ShiftDuration: null,
      ShiftEndTime: "2025-03-05T12:00:00-05:00",
      ShiftStartTime: "2025-03-05T09:00:00-05:00",
      StartDate: "2025-03-05T09:00:00-05:00"
     }
    ],
    AbsenceIncidentId: 47602,
    AbsenceType: "intermittent",
   // BestPhoneNumber: null,
    ClaimStatus: "S",
    ClientCode: "KENT",
    EmployeeNumber: "12544",
    FirstName: "Jeffrey",
    LastName: "Love",
    LinkedIncident: null,
    MaxDaysAllowed: 90,
    MaxReportableDays: 90,
   // NextScheduledShift: null,
    PrimaryReason: "27285cc4-c22e-4776-a4e3-2a9cc29a0f8f",
    ProvinceCode: "MI",
    QuestionResponses: {
     "CQ_APPLY_FOR_LEAVE_REFERRAL": "false"
    },
    ReportedBy: "Employee",
    RequestDate: "2025-03-03T21:17:47.3967527Z",
   // ReturnToWorkDate: null,
    SecondaryReason: "3074ea83-fa6f-4b14-bbcb-615db8f21a43"
   };
testEmail.Attachment.push(testAttachment);
const saveResponse = await service.AddDocument(testEmail, absence, testEmail.Body);
});

app.get('/testSns', async(req: Request, res: Response)=>{
  
  let event = {
    "Records": [
        {
          "EventSource": "aws:sns",
          "EventVersion": "1.0",
          "EventSubscriptionArn": "arn:aws:sns:us-east-1:389331747299:ablarivr-snstopic-voiceEmail:46c1e6f8-9221-4c57-8599-83783dd5dfb4",
          "Sns": {
            "Type": "Notification",
            "MessageId": "8da15c35-eee1-5c60-af2b-3c09f9607984",
            "TopicArn": "arn:aws:sns:us-east-1:389331747299:ablarivr-snstopic-voiceEmail",
            "Subject": "Amazon SES Email Receipt Notification",
            "Message": {
              "notificationType": "Received",
              "mail": {
                "timestamp": "2025-02-28T19:57:52.639Z",
                "source": "<EMAIL>",
                "messageId": "gdsmdrt4borppdkhe4oigvgceqbjtvon0csqn581",
                "destination": [
                  "<EMAIL>"
                ],
                "headersTruncated": false,
                "headers": [
                  {
                    "name": "Return-Path",
                    "value": "<<EMAIL>>"
                  },
                  {
                    "name": "Received",
                    "value": "from a48-132.smtp-out.amazonses.com (a48-132.smtp-out.amazonses.com [*************]) by inbound-smtp.us-east-1.amazonaws.com with SMTP id gdsmdrt4borppdkhe4oigvgceqbjtvon0csqn581 for <EMAIL>; Fri, 28 Feb 2025 19:57:52 +0000 (UTC)"
                  },
                  {
                    "name": "Received-SPF",
                    "value": "pass (spfCheck: domain of amazonses.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=a48-132.smtp-out.amazonses.com;"
                  },
                  {
                    "name": "Authentication-Results",
                    "value": "amazonses.com; spf=pass (spfCheck: domain of amazonses.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=a48-132.smtp-out.amazonses.com; dkim=pass header.i=@amazonses.com; dkim=pass header.i=@tel.lifeworks.com; dmarc=pass header.from=tel.lifeworks.com;"
                  },
                  {
                    "name": "X-SES-RECEIPT",
                    "value": "AEFBQUFBQUFBQUFGVVB5TFRodHBVMURaRjRZWjhka3E3UEVKUXJrL0lwNXRUWVdRS3BLcjhHRHlZYnlINXVUVi81cUh5dENKYmhIVzJZb0k2c3BSelBQeHYrL200eFRsaTFVcFBlSk1SR0k4MDBTeDhld2FRNXVjeEVKQnBxYmRvbXJ3d2JUQUp4bDFxQlJ3djRxM3F0ei9wcHpHbFY5SHlJSUtRUjRDa2xzWXZsbVgrS0ZOY2RVaVdiamVBUVc5OTRsQlhtd3RRblhpMEw1djU1Tzd0Z3M5UkVsaFFpZjFpYUtnNUlKMXJubnhNZjQ3R2RyTFFQaGhad2JRaFBLckMrTUxHMUpTLzVIanJJaXh1RkZSOXh4ZHVpNUFrcUpGbFo3aXRLandQQmxuZVNIMUJGVktqWG1jd1hBMksvcC9FTUJybnphUnlocnprM0haZ3lqRTBickFPN0pyUkFzZzFLQkNhWEsvTGhXRjVRRnNxLy80eVUvYjUxT3YraGRwT0JSdGdmZUhEU1U4PQ=="
                  },
                  {
                    "name": "X-SES-DKIM-SIGNATURE",
                    "value": "a=rsa-sha256; q=dns/txt; b=ZvLr6DKhHru7vywhgCTJnhu2FdnQIT05vDrv5Wd0qruhfkE23SVKCjMA/WMRU8/HcWQBQJNNhkuW0eunIVaOnT0wrJXAbotfkJBXLXEzQBueV7UIy0F2YiluSYs+wqoE9ydIJstMrkNbWfcXyjM5pqYkQHym48Hg4dqSYF2Qg/s=; c=relaxed/simple; s=ug7nbtf4gccmlpwj322ax3p6ow6yfsug; d=amazonses.com; t=1740772672; v=1; bh=SxkteNwcisOy8w3RbmSylrYXXGzgYjKArgzq9h5JUsM=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;"
                  },
                  {
                    "name": "DKIM-Signature",
                    "value": "v=1; a=rsa-sha256; q=dns/txt; c=relaxed/simple; s=4uuurqzvnoltpbjljd4snxvxcswymkwi; d=tel.lifeworks.com; t=1740772672; h=Content-Type:From:To:Subject:Message-ID:Date:MIME-Version; bh=SxkteNwcisOy8w3RbmSylrYXXGzgYjKArgzq9h5JUsM=; b=eVbek2a8dF8iGl/5kUvPdPAq+obSijJz6PArAjdtySzGhj3aBBWnvx3BPLsELOctw056m2Ys52hZRRr3PTSDphJipi0Yp3o3yd/p7PSAPi91pHrqFV3ii8RYYsBO/hjuMDvQCa31t0kYdFx1gtW7Te0MLuKLp4C55xHR3U96Tfgn792J5fTb+Tbw88kIEiKIXQas1IWY+CMnF+kffSps9bAuAWuuJt1BNj0AUzq/ecCK8Hht+m2eeZsBvK7hlfv8KI64uCueyHgOn485rzfacCNNCZom8lKE0j6HRCfumWRDwBXDbg4kNNa/BtMGWVTSNhVMXsdDQ6XFP8s8GaCEEg=="
                  },
                  {
                    "name": "DKIM-Signature",
                    "value": "v=1; a=rsa-sha256; q=dns/txt; c=relaxed/simple; s=ug7nbtf4gccmlpwj322ax3p6ow6yfsug; d=amazonses.com; t=1740772672; h=Content-Type:From:To:Subject:Message-ID:Date:MIME-Version:Feedback-ID; bh=SxkteNwcisOy8w3RbmSylrYXXGzgYjKArgzq9h5JUsM=; b=HglAO/RWG1Nu2gFam/g0x3P9oxX7aTFLkPUDy07ysuE4bP+ly49M8J1MJNcEFA48kA2Alqk4zZWRDjNTCltq1KiyPHxy7KUsZA43Ik7y9sLUlrKrbJ5s5NWc2z/qn1bIcljk6Z5w/IUwcDB6UmB+qouuj9ONaTjA81WwRVyKaGQ="
                  },
                  {
                    "name": "Content-Type",
                    "value": "multipart/mixed;"
                  },
                  {
                    "name": "From",
                    "value": "<EMAIL>"
                  },
                  {
                    "name": "To",
                    "value": "<EMAIL>"
                  },
                  {
                    "name": "Subject",
                    "value": "New voicemail from ClientName- KENT via IVR at:+18443406014"
                  },
                  {
                    "name": "Message-ID",
                    "value": "<<EMAIL>>"
                  },
                  {
                    "name": "Date",
                    "value": "Fri, 28 Feb 2025 19:57:51 +0000"
                  },
                  {
                    "name": "MIME-Version",
                    "value": "1.0"
                  },
                  {
                    "name": "Feedback-ID",
                    "value": "::1.us-east-1.yFFASTD431E1fT/f6XPilP78U5WIPo37JWHjfIDw+Ro=:AmazonSES"
                  },
                  {
                    "name": "X-SES-Outgoing",
                    "value": "2025.02.28-*************"
                  }
                ],
                "commonHeaders": {
                  "returnPath": "<EMAIL>",
                  "from": [
                    "<EMAIL>"
                  ],
                  "date": "Fri, 28 Feb 2025 19:57:51 +0000",
                  "to": [
                    "<EMAIL>"
                  ],
                  "messageId": "<<EMAIL>>",
                  "subject": "New voicemail from ClientName- KENT via IVR at:+18443406014"
                }
              },
              "receipt": {
                "timestamp": "2025-02-28T19:57:52.639Z",
                "processingTimeMillis": 484,
                "recipients": [
                  "<EMAIL>"
                ],
                "spamVerdict": {
                  "status": "DISABLED"
                },
                "virusVerdict": {
                  "status": "DISABLED"
                },
                "spfVerdict": {
                  "status": "PASS"
                },
                "dkimVerdict": {
                  "status": "PASS"
                },
                "dmarcVerdict": {
                  "status": "PASS"
                },
                "action": {
                  "type": "S3",
                  "topicArn": "arn:aws:sns:us-east-1:389331747299:ablarivr-snstopic-voiceEmail",
                  "bucketName": "ablarivr-voiceemailbucket",
                  "objectKeyPrefix": "VoiceEMail/",
                  "objectKey": "VoiceEMail/gdsmdrt4borppdkhe4oigvgceqbjtvon0csqn581"
                }
              }
            },
            "Timestamp": "2025-02-28T19:57:53.147Z",
            "SignatureVersion": "1",
            "Signature": "VIbCKlOEsksSUbh/td/pq3KBA6I3t0WokrSdw6ETjxFhFnOHAjkol5veiJmeuNVTh/IXVNCitsvuItetACAGspLgx42Xsidf12ZdoIOUCxwIKbRqp3QqbbrBot7kycydGE3e+PaU5ag8tytyk3TXAJIt7b7VKdGRLKam4J0WnXAzMepUME9c0JN3J1uKe3qRcVN9h8CljBhbYquPg8nhqJXitPL/u9R0bE6o4u+PtLhEX7KkltlmHQibxVUgTPFzBAHwZBypjWpEzkQL+upqkEjdvdHXGh5oUEPwad3xbsJsXz8Asjnsjuegv2i1gTM9GpvwgboQR9P4rJNfY1aHFw==",
            "SigningCertUrl": "https://sns.us-east-1.amazonaws.com/SimpleNotificationService-9c6465fa7f48f5cacd23014631ec1136.pem",
            "UnsubscribeUrl": "https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:389331747299:ablarivr-snstopic-voiceEmail:46c1e6f8-9221-4c57-8599-83783dd5dfb4",
            "MessageAttributes": {}
          }
        }
      ]
    
  };
 // const msg = await vmCallbackHandler(event);
 
 
 })
//
app.listen(port, ()=> {
//&console.log(`[Server]: I am running at https://localhost:${port}`);
});