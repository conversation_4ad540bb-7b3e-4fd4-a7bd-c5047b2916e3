
import {LogService} from '@/util'
import { ConnectContactFlowEvent } from 'aws-lambda'
import {PublicHoliday} from '@/services';

const log = new LogService({ logToConsole: process.env.Debug });
const holidayService = new PublicHoliday();

export const handler = async (event: ConnectContactFlowEvent) => {
    try {
        let result = await holidayService.isTodayPublicHolidayAsync();
        return  { IsTodayUSHoliday: result };
    } catch (error: any) {
        log.error('Checking holiday error', error);
        return (error.message);
    }
};
