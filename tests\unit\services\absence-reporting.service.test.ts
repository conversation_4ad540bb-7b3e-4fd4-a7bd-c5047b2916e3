import moment, { Moment } from 'moment';
import { AbsenceReportingService } from '../../../src/services';
import { AbsenceDates } from '../../../src/models';

const reportingService = new AbsenceReportingService();
it('verifies successful response', async () => {
  const startDate: AbsenceDates = {
    StartDate: '2024-04-05 15:00:00',
    EndDate: '2024-04-05 22:00:00',
    ShiftStartTime: '2024-04-05 15:00:00',
    ShiftEndTime: '2024-04-05 22:00:00',
    ScheduledShiftStartTime: '2024-04-05 09:00:00',
    ScheduledShiftEndTime: '2024-04-05 15:00:00',
  };
  const endDate: AbsenceDates = {
    StartDate: '2024-04-08 00:00:00',
    EndDate: '2024-04-08 00:00:00',
    ShiftStartTime: '2024-04-08 00:00:00',
    ShiftEndTime: '2024-04-08 00:00:00',
  };
  const numberOfDays = 4;
  const dates = AbsenceReportingService.updateAbsDatesInRange(
    startDate,
    endDate,
  );
  expect(dates).toBeDefined();

  expect(dates.length).toBe(numberOfDays);
});

it('verifies successful response for LDA greater than FDA', async () => {
  const startDate: AbsenceDates = {
    StartDate: '2024-04-05 15:00:00',
    EndDate: '2024-04-05 22:00:00',
    ShiftStartTime: '2024-04-05 15:00:00',
    ShiftEndTime: '2024-04-05 22:00:00',
    ScheduledShiftStartTime: '2024-04-05 09:00:00',
    ScheduledShiftEndTime: '2024-04-05 15:00:00',
  };
  const endDate = moment(new Date());

  const isValid = reportingService.isLdaGreaterThanFda(endDate, startDate);
  expect(isValid).toBeDefined();

  expect(isValid).toBe(true);
});

it('verifies successful response', async () => {
  const today = moment(new Date()).startOf('day');

  const endDate = moment(new Date()).add(60, 'days').startOf('day');

  const result = await reportingService.GetAbsenceDateRange(today, endDate);
  expect(result).toBeDefined();
  expect(result.length).toBe(60);
});

it('verifies unsuccessful response', async () => {
  const today = moment(new Date()).startOf('day');

  const endDate = moment(new Date()).add(60, 'days').startOf('day');

  const result = await reportingService.GetAbsenceDateRange(today, endDate);
  expect(result).toBeDefined();
  expect(result.length).toBe(60);
});

it('verifies successful response for today -60 days', async () => {
  const today = moment(new Date()).startOf('day');

  const endDate = moment(new Date()).subtract(60, 'days').startOf('day');

  const result = await reportingService.GetAbsenceDateRange(endDate, today);
  expect(result).toBeDefined();
  expect(result).toContain('2024-06-04');
  expect(result).not.toContain('2024-04-05');
  expect(result.length).toBe(60);
});

it('verifies successful response for today + 60 days', async () => {
  const today = moment(new Date()).startOf('day');

  const endDate = moment(new Date()).add(60, 'days').startOf('day');

  const result = await reportingService.GetAbsenceDateRange(today, endDate);
  expect(result).toBeDefined();
  expect(result).not.toContain('2024-06-04');
  expect(result).toContain('2024-08-03');
  expect(result.length).toBe(60);
});

it('verifies duplicate absence for date range - today + 60 days', async () => {
  const today = moment(new Date()).startOf('day');

  const endDate = moment(new Date()).add(60, 'days').startOf('day');
  const sessionAbsenceDates: AbsenceDates[] = new Array({
    StartDate: '2024-08-03 15:00:00',
    EndDate: '2024-08-03 22:00:00',
    ShiftStartTime: '2024-08-03 15:00:00',
    ShiftEndTime: '2024-08-03 22:00:00',
    ScheduledShiftStartTime: '2024-08-03 09:00:00',
    ScheduledShiftEndTime: '2024-08-03 15:00:00',
  });
  let isDuplicate = false;
  const result = await reportingService.GetAbsenceDateRange(today, endDate);
  isDuplicate = sessionAbsenceDates.some((d) =>
    result.some((x) =>
      moment(d.ShiftStartTime).startOf('day').isSame(moment(x).startOf('day')),
    ),
  );
  expect(result).toBeDefined();
  expect(result).not.toContain('2024-06-04');
  expect(result).toContain('2024-08-03');
  expect(result.length).toBe(60);
  expect(isDuplicate).toBe(true);
});

it('verifies duplicate absence for intermittent - today + 60 days', async () => {
    const today = moment(new Date()).startOf('day');
  
    const endDate = moment(new Date()).add(60, 'days').startOf('day');
    const sessionAbsenceDates: AbsenceDates[] = new Array({
      StartDate: '2024-08-03 15:00:00',
      EndDate: '2024-08-03 22:00:00',
      ShiftStartTime: '2024-08-03 15:00:00',
      ShiftEndTime: '2024-08-03 22:00:00',
      ScheduledShiftStartTime: '2024-08-03 09:00:00',
      ScheduledShiftEndTime: '2024-08-03 15:00:00',
    });
    let isDuplicate = false;
    const enteredDate = '2024-08-03'
    isDuplicate = sessionAbsenceDates.some(d => moment(d.ShiftStartTime, 'YYYY-MM-DD').isSame(moment(enteredDate, 'YYYY-MM-DD'), 'day'));    
    expect(isDuplicate).toBe(true);
  });

  //intermittent absence validating maxDaysInPast <= reportedAbsence <= maxDaysInFuture
  it('verifies future absence date for intermittent within range - today+ 60 days', async () => {
    let isWithinRangeFuture = false;
    let localMaxBeforeAbsenceDays = 60;
    let localMaxValidAbsenceDays = 60;
    let absencedate60daysInFuture = moment(new Date()).add(60, 'days').startOf('day');
    isWithinRangeFuture = await reportingService.isDateWithinValidRange(
      localMaxBeforeAbsenceDays,
      localMaxValidAbsenceDays,
      absencedate60daysInFuture
    );
    expect(isWithinRangeFuture).toBe(true);
  });


  it('verifies future absence date(LDA) for continuous within range - today(FDA) + 60 days', async () => {
     const today = moment(new Date()); //FDA
    const future60thDay = moment(today).add(60, 'days').startOf('day');
    let isWithinRangeFuture = false;
    let localMaxBeforeAbsenceDays = 60;
    let localMaxValidAbsenceDays = 60;
    isWithinRangeFuture = await reportingService.isDateWithinValidRange(
      localMaxBeforeAbsenceDays,
      localMaxValidAbsenceDays,
      future60thDay
    );
    expect(isWithinRangeFuture).toBe(true);
  });

  it('verifies past absence date for intermittent within range - today- 60 days', async () => {
    let isWithinRangeFuture = false;
    let localMaxBeforeAbsenceDays = 60;
    let localMaxValidAbsenceDays = 60;
    let absencedate60daysInPast = moment(new Date()).add(-60, 'days').startOf('day');
    isWithinRangeFuture = await reportingService.isDateWithinValidRange(
      localMaxBeforeAbsenceDays,
      localMaxValidAbsenceDays,
      absencedate60daysInPast
    );
    expect(isWithinRangeFuture).toBe(true);
  });


  it('verifies past absence date for continuous within range - today(FDA) - 60 days', async () => {
     const today = moment(new Date()); //FDA
    const past60thDay = moment(today).add(-60, 'days').startOf('day');
    let isWithinRangeFuture = false;
    let localMaxBeforeAbsenceDays = 60;
    let localMaxValidAbsenceDays = 60;
    isWithinRangeFuture = await reportingService.isDateWithinValidRange(
      localMaxBeforeAbsenceDays,
      localMaxValidAbsenceDays,
      past60thDay
    );
    expect(isWithinRangeFuture).toBe(true);
  });
  it('verifies past absence date for continuous within range - today(FDA) - 61 days', async () => {
    const today = moment(new Date()); //FDA
   const past60thDay = moment(today).add(-61, 'days').startOf('day');
   let isWithinRangeFuture = false;
   let localMaxBeforeAbsenceDays = 60;
   let localMaxValidAbsenceDays = 60;
   isWithinRangeFuture = await reportingService.isDateWithinValidRange(
     localMaxBeforeAbsenceDays,
     localMaxValidAbsenceDays,
     past60thDay
   );
   expect(isWithinRangeFuture).toBe(false);
 });
  it('verifies duplicate absence for date range - today + 60 days', async () => {
    const today = moment(new Date()).add(-56, 'days').startOf('day');
  
    const endDate = moment(today).add(9, 'days').startOf('day');
    const sessionAbsenceDates: AbsenceDates[] = new Array({
      IncidentId: 44167,
      StartDate: '2024-05-29T21:00:00-04:00',
      EndDate: '2024-05-30T05:00:00-04:00',
      ShiftStartTime: '2024-05-29T21:00:00-04:00',
      ShiftEndTime: '2024-05-30T05:00:00-04:00',
      ShiftDuration: undefined,
      ScheduledShiftStartTime: '2024-05-29T21:00:00-04:00',
      ScheduledShiftEndTime: '2024-05-30T05:00:00-04:00',
      IsOverLap: false,
      IsDuplicate: false
    },
    {
      IncidentId: 159703,
      StartDate: '2024-05-13T09:00:00-04:00',
      EndDate: '2024-05-13T13:00:00-04:00',
      ShiftStartTime: '2024-05-13T09:00:00-04:00',
      ShiftEndTime: '2024-05-13T13:00:00-04:00',
      ShiftDuration: undefined,
      ScheduledShiftStartTime: '2024-05-13T09:00:00-04:00',
      ScheduledShiftEndTime: '2024-05-13T13:00:00-04:00',
      IsOverLap: false,
      IsDuplicate: false
    },
    {
      IncidentId: 159703,
      StartDate: '2024-05-14T09:00:00-04:00',
      EndDate: '2024-05-14T13:00:00-04:00',
      ShiftStartTime: '2024-05-14T09:00:00-04:00',
      ShiftEndTime: '2024-05-14T13:00:00-04:00',
      ShiftDuration: undefined,
      ScheduledShiftStartTime: '2024-05-14T09:00:00-04:00',
      ScheduledShiftEndTime: '2024-05-14T13:00:00-04:00',
      IsOverLap: false,
      IsDuplicate: false
    },
    {
      IncidentId: 159703,
      StartDate: '2024-05-15T09:00:00-04:00',
      EndDate: '2024-05-15T17:00:00-04:00',
      ShiftStartTime: '2024-05-15T09:00:00-04:00',
      ShiftEndTime: '2024-05-15T17:00:00-04:00',
      ShiftDuration: undefined,
      ScheduledShiftStartTime: '2024-05-15T09:00:00-04:00',
      ScheduledShiftEndTime: '2024-05-15T17:00:00-04:00',
      IsOverLap: false,
      IsDuplicate: false
    },
    {
      IncidentId: 41021,
      StartDate: '2024-05-13T09:00:00-04:00',
      EndDate: '2024-05-13T13:00:00-04:00',
      ShiftStartTime: '2024-05-13T09:00:00-04:00',
      ShiftEndTime: '2024-05-13T13:00:00-04:00',
      ShiftDuration: undefined,
      ScheduledShiftStartTime: '2024-05-13T09:00:00-04:00',
      ScheduledShiftEndTime: '2024-05-13T13:00:00-04:00',
      IsOverLap: false,
      IsDuplicate: false
    },
    {
      IncidentId: 41021,
      StartDate: '2024-05-14T09:00:00-04:00',
      EndDate: '2024-05-14T13:00:00-04:00',
      ShiftStartTime: '2024-05-14T09:00:00-04:00',
      ShiftEndTime: '2024-05-14T13:00:00-04:00',
      ShiftDuration: undefined,
      ScheduledShiftStartTime: '2024-05-14T09:00:00-04:00',
      ScheduledShiftEndTime: '2024-05-14T13:00:00-04:00',
      IsOverLap: false,
      IsDuplicate: false
    },
    {
      IncidentId: 41021,
      StartDate: '2024-05-15T09:00:00-04:00',
      EndDate: '2024-05-15T17:00:00-04:00',
      ShiftStartTime: '2024-05-15T09:00:00-04:00',
      ShiftEndTime: '2024-05-15T17:00:00-04:00',
      ShiftDuration: undefined,
      ScheduledShiftStartTime: '2024-05-15T09:00:00-04:00',
      ScheduledShiftEndTime: '2024-05-15T17:00:00-04:00',
      IsOverLap: false,
      IsDuplicate: false
    },
    {
      ShiftStartTime: '2024-05-20 21:00:00',
      StartDate: '2024-05-20 21:00:00',
      ShiftDuration: null,
      ScheduledShiftEndTime: '2024-05-20 17:00:00',
      ScheduledShiftStartTime: '2024-05-20 09:00:00',
      IsOverLap: false,
      IsUpdated: false,
      EndDate: '2024-05-21 05:00:00',
      IncidentId: 44172,
      ShiftEndTime: '2024-05-21 05:00:00'
    });
    let isDuplicate = false;
    let enteredDateList =  await reportingService.GetAbsenceDateRange(today, endDate);
    isDuplicate = sessionAbsenceDates.some((d) => enteredDateList.some(x => moment(d.ShiftStartTime, 'YYYY-MM-DD').isSame(moment(x, 'YYYY-MM-DD'), 'day'))); 
    
    expect(isDuplicate).toBe(true);
  });