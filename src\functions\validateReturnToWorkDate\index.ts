import { ConnectContactFlowEvent } from 'aws-lambda'
import { AbsenceReportingService, CallLog, LocalConfiguration, ScopedHttpService } from '@/services'
import { ValidationErrors, PromptHelper } from '@/util'
import moment from 'moment'
import { RtwResponse } from '@/models/FunctionResponses';


const reportingService = new AbsenceReportingService(); // FUTURE - don't instantiate like this
const callService = new CallLog(); // FUTURE - don't instantiate like this
const localConfig = new LocalConfiguration();

export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'validateReturnToWorkDate';
    reportingService.Logger.log('Input data', event);
    let contactId;
    try {
        const enteredDate = event.Details.Parameters.ReturnDayToWork;
        contactId = event.Details.ContactData.ContactId;   
        const clientCode = event.Details.Parameters.ClientCode;
        const dtFormat = event.Details.Parameters?.RTWDateFormatType === '1'? 'MMDDYYYY': 'DDMMYYYY';                        
        const returnToWorkDate = moment(enteredDate, dtFormat).startOf('day');
        reportingService.Logger.log(`Entered return to work date formatted: ${returnToWorkDate}`);
        let res: RtwResponse = {
                IsSaveSuccess: false,
                IsRTWValid : false,
                ReturnDayToWorkplayable: null,
                RTWNotValidReason: null,
                PromptConfirmReturnToWorkSSML1: null
        };                                
        localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
        //Check whether entered date format is correct.
        const isValidDate = moment(enteredDate, dtFormat, true).isValid();

        res.IsRTWValid = isValidDate;
        res.IsSaveSuccess = isValidDate? true : false;
        res.RTWNotValidReason = null;
        res.ReturnDayToWorkplayable = returnToWorkDate.format('YYYY-MM-DD') ;        
        //Return Invalid date error
        if (!isValidDate) {            
            reportingService.Logger.log('Entered return to work date is invalid');
            res.IsRTWValid = false;
            res.IsSaveSuccess = false;
            res.RTWNotValidReason = ValidationErrors.InvalidRTWDateFormat;
            res.ReturnDayToWorkplayable = null;
        }
        else
        {
            //logic to check whether RTW is reported too early.
            const reportedDates = await reportingService.getAbsenceDatesAsync(contactId, true);
            reportingService.Logger.log('Already reported dates:', reportedDates);
            const lastReportedDate = moment.parseZone(reportedDates[reportedDates.length - 1].ShiftEndTime);
            reportingService.Logger.log('Last reported dates:', lastReportedDate); 
            reportingService.Logger.log('Last reported dates object:', reportedDates[reportedDates.length - 1]); 
            //const lastReportedDateTime = lastReportedDate.format("HH:mm:ss");
            const lastReportedDateScheduledShiftEnd = reportedDates[reportedDates.length - 1].ScheduledShiftEndTime;
            const lastReportedDateScheduledShiftEndTime =  lastReportedDateScheduledShiftEnd != null || lastReportedDateScheduledShiftEnd != undefined? moment(reportedDates[reportedDates.length - 1].ScheduledShiftEndTime) : null;
            
            //If RTW is too early. RTW >= last absence date
            if (reportedDates.some((d) => moment.parseZone(d.StartDate).isSameOrAfter(returnToWorkDate, 'day'))){
                if (                    
                    // lastReportedDateScheduledShiftEndTime != null &&
                    moment(returnToWorkDate).startOf('day').isBefore(moment(lastReportedDate).startOf('day'), 'day')
                    //&& ((moment(lastReportedDate).isSameOrAfter(lastReportedDateScheduledShiftEndTime, 'hour')//|| 
                    //(moment(lastReportedDateTime).isSameOrAfter(lastReportedDateScheduledShiftEndTime, 'minute'))
                    //)
                    //)
                )                  
            
            {
                reportingService.Logger.log('Entered return to work date is earlier than already reported', returnToWorkDate);
                res.IsRTWValid = false;
                res.IsSaveSuccess = false;
                res.RTWNotValidReason = ValidationErrors.ReturnToWorkDateTooEarly; 
            
            }            
        }        
    }                          

        //Call log stage for Validate RTW Lambda
        //await callService.logStageAsync(
        //    contactId,
        //    lambdaName,
        //    res.RTWNotValidReason ? null : { Validation: res.RTWNotValidReason },
        //    res
        //);

        let callData = await callService.getCallDataAsync(contactId);
        const locale = callData?.SelectedLang ?? "EN";
        const config = await localConfig.getClientConfigAsync(callData.ClientCode, locale);
        res.PromptConfirmReturnToWorkSSML1 = PromptHelper.getMergedPrompt(config.clientConfig.ConfirmRtwDateText ?? [], locale, '{ReturnToWorkDateString}', res.ReturnDayToWorkplayable !== null? res.ReturnDayToWorkplayable : '', "date");               
        reportingService.Logger.log('Result', res);

        //Return result
        return res;

    } catch (err: any) {
        reportingService.Logger.log('Error occured:', err);
        await callService.logStageAsync(contactId || 'UNDEFINED', lambdaName, err);
        let result: RtwResponse = {
            IsSaveSuccess: false,
            IsRTWValid: false,
            RTWNotValidReason: 'Internal Error' + err.message,
            ReturnDayToWorkplayable: null
        }
        return result;
    }
};
