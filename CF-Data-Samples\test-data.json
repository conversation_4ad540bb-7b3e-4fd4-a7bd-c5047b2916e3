{"Details": {"ContactData": {"Attributes": {"PromptSubOpeningSSML1": "<speak><amazon:domain name='conversational'>Kindly note you can record your absences for 60 days in future OR within previous 2 days. Please note that you can report a maximum of 60 absences per call. You can also report your absences online via Abiliti Absence at goodyear.abiliti absence U S.com. A confirmation number will be provided to you at the end of the call.</amazon:domain></speak>", "DefaultDateFormatType": "1", "PromptLanugageSelectionS3URI": "", "LambdaValidateEmployeeIdARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateEmployeeId", "LambdaValidateEmployeePhoneNumberARN": "", "LambdaSubmitAbsenceARN": "", "PromptEnterEmployeeIdSSML": "<speak><amazon:domain name='conversational'> Please enter your employee I D using your keypad followed by pound key. If you do not know your employee I D, please disconnect and contact your employer to report your absence.</amazon:domain></speak>", "ClientName": "GOODYEAR", "LambdaGetPrimaryReasonMenuARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetPrimaryReasonMenu", "LambdaValidateAbsenceDateARN": "", "LambdaValidateShiftStartTimeARN": "", "IsReferralRequired": "false", "TotalAbsenceDays": "1", "LambdaValidateShiftEndTimeARN": "", "CallFlowEnterReasonARN": "efc54cc2-50e0-466d-a5fe-07025c2f12ef", "CallFlowSubmitAbsenceIfErrorARN": "", "LambdaSaveSecondaryReasonARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveSecondaryReason", "MaxTotalMissedAbsenceDays": "5", "OptionToRepeat": "To repeat this list, press star.", "PromptSecondaryReasonMenuOptionsSSML": "<speak><amazon:domain name='conversational'>For Self press 1,For Family Member press 2 To repeat this list, press star.</amazon:domain></speak>", "LambdaAuthenticateEmployeeARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-AuthenticateEmployee", "IsPrevAbsWithinAADays": "false", "AA": "2", "IsEnterEmployeePhoneNumberRequired": "true", "LastAbsenceDate": "0", "LambdaSetReturnToWorkARN": "", "LambdaValidateReturnToWorkDateARN": "", "MaxRetryMenu1": "3", "MaxRetryMenu2": "", "IsEmployeeValid": "true", "Marker1": "test", "Marker": "error on saving sub absence menu", "CallFlowClosingARN": "", "SelectedSecondaryReason": "1", "IsTotalAbsenceDaysValid": "true", "MaxRetryLambda2": "", "LambdaGetClientConfigARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetClientConfig-temppoc", "MaxRetryLambda1": "2", "PromptEnterTotalAbsenceDaysSSML1": "<speak><amazon:domain name='conversational'> Please remember that all absences must be reported. Enter the total number of missed absences you're reporting absent on this call. Kindly note that you can report a maximum of 60 absences per call. To repeat, press the * key.</amazon:domain></speak>", "Environment": "LAB", "CallFlowGetOpeningMsgV1ARN": "ec9c237c-1a16-4eed-a729-66061cdf0e53", "CallFlowEnterEmployeePhoneNumberARN": "", "PromptPrimaryReasonMenuOptionsSSML": "<speak><amazon:domain name='conversational'>Please enter your reason for absence Illness or Injury (not FMLA), press 1. STD Accident  and  Sickness or FMLA Absence, press 2. Personal Reasons, press 3. Bereavement, press 4. Jury Duty, press 5. Caring for or bonding with a new Child, press 6. Military Leave, press 7. Weather Conditions, press 8. To repeat this list, press star.</amazon:domain></speak>", "PromptSpeicalOpeningMsgSSML": "", "MaxAllowAbsenceDaysPast": "14", "LambdaValidateDoBARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateDoB", "PromptMaxInvalidEmployeeIdSSML": "<speak><amazon:domain name='conversational'>Sorry, your absence cannot be recorded without your correct Employee number Please call back later Goodbye.</amazon:domain></speak>", "DoBFormatType": "1", "DefaultTimeFormatType": "1", "CallFlowTransferToCSRARN": "", "SelectedPrimaryReason": "1", "PreviousAbsenceWithinAA": "2", "PromptIntroSecondaryReasonMenuSSML": "<speak><amazon:domain name='conversational'>Please select from one of the following options.</amazon:domain></speak>", "LambdaGetSecondaryReasonMenuARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetSecondaryReasonMenu", "MaxAllowAbsenceDaysFuture": "60", "PromptSpecialMsgSSML": "", "LambdaCheckAllAbsReportedARN": "", "CallFlowEnterAbsenceDateARN": "9d449bf4-7ec8-4bca-bd85-6dcaa03ba4db", "IsLambdaMaxErrorRetry": "false", "PromptInvalidEmployeeIdSSML1": "<speak><amazon:domain name='conversational'>The Employee ID you entered is</amazon:domain></speak>", "IsEmployeeIdValid": "true", "PromptInvalidEmployeeIdSSML2": "<speak><amazon:domain name='conversational'>This does not match with our records. Please try again</amazon:domain></speak>", "IsDoBValid": "true", "PromptAlertSSML": "", "LambdaValidateRTWShiftStartTimeARN": "", "IsMainReasonBereavment": "false", "CallFlowValidateEmployeeARN": "f5467b78-f173-46fc-83b0-06391bdfc027", "IsMenuMaxInvalidRetry": "false", "NumberOfOptions": "2", "IsAllowInteruppted": "false", "MutliLanguageId": "0", "LambdaSaveTotalAbsenceDaysARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveTotalAbsenceDays", "LambdaGetCarrierTransferNumberARN": "", "EmployeeId": "80081254", "PromptUnexpectedErrorSSML": "put ssml here", "DefaultLanguage": "English", "PromptIntroPrimaryReasonMenuSSML": "<speak><amazon:domain name='conversational'>Please select the reason for your absence from one of the following options. If you or your family member is hospitalized or you expect your absence to be greater than 3 days due to an illness or injury,  Please select option number 2.</amazon:domain></speak>", "CallFlowDynamicFlowV1ARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/contact-flow/78498ff6-24e1-433c-a281-0aed5c8cc131", "LambdaGetOpeningMsgV1ARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetOpeningMsgV1-temppoc", "PromptOpeningSSML": "<speak><amazon:domain name='conversational'>Thank you for calling the Goodyear Absence Reporting System. Please note that effective 1/1/2023, UNUM administers leave under the Family Medical Leave Act (FMLA) and time away from work due to Short Term Disability (STD) and Accident and Sickness. Please listen carefully as the options might have changed.</amazon:domain></speak>", "CallFlowEnterTotalAbsenceDaysARN": "548881cb-cde8-4fee-a655-2d5db7a3df5f", "LambdaSaveAbsenceDateARN": "", "CallFlowSubmitAbsenceARN": "", "IsSaveSuccess": "true", "ClientCode": "GOODYEAR", "CallFlowEnterRTWARN": "", "CallFlowTransferToExternalARN": "", "PromptTransferNumberGeneralErrorSSML": ""}, "Channel": "VOICE", "ContactId": "64e6c929-11f9-4b90-8b42-11d96cb8cc60", "CustomerEndpoint": {"Address": "+16782033318", "Type": "TELEPHONE_NUMBER"}, "CustomerId": null, "Description": null, "InitialContactId": "64e6c929-11f9-4b90-8b42-11d96cb8cc60", "InitiationMethod": "INBOUND", "InstanceARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330", "LanguageCode": "en-US", "MediaStreams": {"Customer": {"Audio": null}}, "Name": null, "PreviousContactId": "64e6c929-11f9-4b90-8b42-11d96cb8cc60", "Queue": null, "References": {}, "RelatedContactId": null, "SystemEndpoint": {"Address": "+16472438396", "Type": "TELEPHONE_NUMBER"}}, "Parameters": {"ClientName": "GOODYEAR", "SelectedSecondaryReason": "1", "ClientCode": "GOODYEAR"}}, "Name": "ContactFlowEvent"}