import { LocaleModel } from "@/models";

const InvalidSequences = [
    {
        str: ["<p>", "</p>", "&quot;"],
        repl: ""
    },
    {
        str: ["&amp;", "&"],
        repl: " and "
    },
    // {
    //     str:["/"],
    //     repl: " " 
    // },
    {
        str: ["+"],
        repl: " plus "
    },
    {
        str: ["*"],
        repl: " star "
    },
    {
        str: ["\n"],
        repl: " "
    }
];

const sanitizeText = (str: string) => {
    let toReturn = str;

    //if (str.includes('>') && str.includes('<')) {
    // toReturn = toReturn?.substring(toReturn.indexOf('>')+1, toReturn.lastIndexOf('<'));
    //}
    for (let entry of InvalidSequences) {
        for (let swap of entry.str) {
            toReturn = toReturn.replaceAll(swap, entry.repl);
        }
    }
    return toReturn;
}

export class PromptHelper {

    static wrapSSML(str: string[], domain: string = "conversational"): string {
        return PromptHelper.getPromptWithSpeakTag(domain, str.map(x => sanitizeText(x)).join(' '), true)
    }
    static wrapLocaleSSML(str: LocaleModel[], domain: string = "conversational"): string {
        return PromptHelper.getPromptWithSpeakTag(domain, str.map(x => sanitizeText(x.Description ?? '')).join(' '), true)
    }
    static getPromptWithSpeakTag(name: string, message: string, amazonDomain: boolean = true) {
        if (amazonDomain) {
            return `<speak><amazon:domain name='${name}'>${message}</amazon:domain></speak>`;
        }
        return `<speak>${message}</speak>`;
    }
    static filterAndWrapSSML(str?: LocaleModel[], selectedLang: string = 'en', domain: string = "conversational"): string {
        if (str != null) {
            let langSpecificObject = str.find(x => x.Locale?.toLowerCase() === selectedLang.toLowerCase())?.Description;
            return PromptHelper.getPromptWithSpeakTag(domain, sanitizeText(langSpecificObject ?? ''), true);
        }
        return '';
    }
    static getMergedPrompt(text: LocaleModel[], selectedLang: string,  fromMergeString: string, toMergeString: string, mergeType: string, addSsmlTag: boolean = true): any {
        const result1 = mergeType?.length > 0 ? PromptHelper.addInterpretTag(mergeType, fromMergeString, toMergeString) : '';
        let mergedLocalePrompt = text.map((x: LocaleModel) => ({
            Locale: x.Locale,
            Description: x.Description?.replaceAll(fromMergeString, result1)
        })
        )

        return addSsmlTag?PromptHelper.filterAndWrapSSML(mergedLocalePrompt, selectedLang): mergedLocalePrompt ;
    }
    getFormattedMessageWithSpeakTag(message: string[], strReplace: string = '', strTobeReplaced: string = '') {
        return `<speak>${message.join(' ').replaceAll(strReplace, strTobeReplaced)}</speak>`;
    }
    static addInterpretTag(interpretType: string, strToBeReplaced: string = '', strReplace: string = ''): string {

        if (interpretType === 'date')
            return `<say-as interpret-as='${interpretType}' format='mmdd'> ${strToBeReplaced.replaceAll(strToBeReplaced, strReplace)} </say-as> <break time='500ms'/>`
        if (interpretType === 'telephone')
            return `<say-as interpret-as='${interpretType}'> ${strToBeReplaced.replaceAll(strToBeReplaced, strReplace)} </say-as> <break time='500ms'/>`
        if (interpretType === 'digit')
            return `<say-as interpret-as='${interpretType}'> ${strToBeReplaced.replaceAll(strToBeReplaced, strReplace)} </say-as> <break time='500ms'/>`

        return '';
    }
    static addLanguageTag(language: string, strToBeReplaced: string = '', strReplace: string = ''): string {

        if (language === 'FR')
            return `<lang xml:laang="fr-FR"> ${strToBeReplaced.replace(strToBeReplaced, strReplace)} </lang>`
        if (language === 'ES')
            return `<lang xml:laang="es-SP"> ${strToBeReplaced} </lang>`


        return '';
    }

    static getFormattedMessage(message: string, strReplace: string = '', strTobeReplaced: string = '') {
        return message.replaceAll(strReplace, strTobeReplaced);
    }

    static  mapToNumLocale(index: number, locale: string = 'EN'): string{
        const frNums: Record<number, string> = {1: 'un', 2:'deux', 3: 'trois', 4: 'quatre', 5:'cinq', 6:'six', 7:'sept', 8:'huit', 9: 'neuf', 10: 'dix'};
        const spNums: Record<number, string> = {1: 'uno', 2:'dos', 3: 'tres', 4: 'cuatro', 5:'cinco', 6:'seis', 7:'siete', 8:'ocho', 9: 'neuve', 10: 'diez'};
        if(locale == 'FR')
            return frNums[index+1];
        else if (locale == 'ES')
            return spNums[index+1];
        return `${index +1}`;
    }
}