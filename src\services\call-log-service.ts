import { AbsenceIncident } from '@/models';
import { CallStageLogEntry, DroppedSessionModel } from '@/models/DroppedSessionModel';
import { BaseService, CacheClient } from '@/services'
import { ClaimStatus } from '@/util';
import { GetItemInput , ListTablesCommand} from '@aws-sdk/client-dynamodb';
import { GetItemCommandInput } from '@aws-sdk/client-dynamodb/dist-types/commands';
import {DynamoClient} from 'Mixins'

export default class CallLog extends DynamoClient(BaseService) { //FUTURE: THIS REALLY SHOULD USE THE CACHE INSTEAD
    
    get TableName() {
        return  process.env.ivrCallsTableName || 'ablarivr-call-session';
    }

    public async getCallDataAsync(contactId: string): Promise<any> {
        try {
            const params = {
                TableName: this.TableName,
                Key: { ContactId: contactId },
            };
            // //&console.log(`PARAMS FOR CALL: ${JSON.stringify(params)}`);
            this.Logger.log(`PERFORMANCE: in getcallDataAsync- data GET ${contactId}`);
            let call = await this.get(params);
            // //&console.warn(`Loaded Call Data from Database for ${contactId}`, call);
            this.Logger.log(`PERFORMANCE: in getcallDataAsync- data RETURNED ${JSON.stringify(call?.Item)}`)
            return call?.Item || {};
        } catch (err) {
            this.log.error(err);
            throw err;
        }
    }

    public async setCallDataAsync(contactId: string, callData: any, isAbsence: boolean = false) {
        try {
            this.Logger.log(`Saving call data for ${contactId}, tbale name: ${this.TableName}`, callData);
            let existentCallData = await this.getCallDataAsync(contactId);
            const isSaved = callData?.IsSaved || existentCallData?.IsSaved || "false";
            let _calldata = { ...existentCallData, ...callData };
            
            let data;
            if (isAbsence) {
                this.Logger.log('setcalldata with isAbsence = ', isAbsence);                
                existentCallData.Absences = callData.Absences;
                _calldata.Absences = callData.Absences;
                this.Logger.log('Actual CallData', _calldata);
                this.Logger.log('existent CallData', existentCallData);
                this.Logger.log('call data absence', callData.Absences);
                let updateParams = 
                {                    
                    TableName: this.TableName,
                    Key:{
                        ContactId: `${contactId}`
                    },
                    UpdateExpression: 'set Absences = :Absences',
                    ConditionExpression: 'ContactId = :contactId',
                    ExpressionAttributeValues: {
                        ':Absences': callData.Absences,
                        ':contactId': existentCallData.ContactId
                }
                }
                await this.update(updateParams).then( resolved => { return resolved?.$metadata.httpStatusCode }, rejected => {throw `Error updating ${contactId}, ${rejected}`}); 
            }
            else{
             const params = {
                TableName: this.TableName,
                Item: {
                    ...existentCallData,
                ContactId: contactId,
                IsSaved: isSaved,
                ..._calldata
                }
            };
            data = await this.put(params);  
        }   
            if(data)
                this.Logger.log('Call data saved');
            return data;
        } catch (err) {
            this.Logger.error('Call data save failed', err);
            throw err;
        }
    }

    /**
     * @param {string} contactId
     * @param {string[]} items
     * @param {string} arrayName
     */
    async appendItemsToArrayAsync(contactId: string, items: CallStageLogEntry[], arrayName: string) {
        try {
            const existingCallData = await this.getCallDataAsync(contactId);
            const arrayData = Array.isArray(existingCallData[arrayName]) ? Object.values(existingCallData[arrayName]) : []; 
            arrayData.push(...items);

            await this.setCallDataAsync(contactId, { [arrayName]: arrayData });
        } catch (err) {
            this.log.log('Stage Logging Error', err);
            throw err;
        }
    }

    public async logStageAsync(contactId: string, lambdaName: string, error: any, result : any = null) {
        try {
            const logEntry = new CallStageLogEntry(error, result, lambdaName);

            this.appendItemsToArrayAsync(contactId, [logEntry], 'CallStageLog');
            this.Logger.log('Stage data logged');
        } catch (err) {
            this.Logger.error('Logging stage error', err);
            throw err;
        }
    }

    public async getOpenedSessionsAsync() : Promise<DroppedSessionModel[]>{
        let x: CallStageLogEntry = {
            t: "hellO"
        } 
        let toReturn: DroppedSessionModel[] = [];
        try {
            const params = {
                TableName:  this.tableName || 'ablarivr-call-session',
                IndexName: 'IsSubmittedIdx',
                KeyConditionExpression: 'IsSubmitted = :isSubmittedVal',
                //FilterExpression : 'IsSubmitted  <> :isSubmittedVal',
                ExpressionAttributeValues: { ':isSubmittedVal': ClaimStatus.Created },
            };
            let result: any = [];
            await this.query(params).then(res => result = res?.Items); //FUTURE-> add to types?
            this.Logger.log(`Found ${result.length} opened sesssions`);

            if(result && Array.isArray(result))
                toReturn = this.MapToDroppedSessionModel(result);
            
            return toReturn;
        } catch (error) {
            this.log.error('Getting opened sessions error', error);
            throw error;
        }
    }
    private MapToDroppedSessionModel(result: any[]): DroppedSessionModel[]{
        let toReturn: DroppedSessionModel[] =[];
        
        result.forEach((record: any) => {
            let toRecord : DroppedSessionModel = new DroppedSessionModel();

            toRecord.ContactId = record.ContactId;
            toRecord.CallStageLog = record.CallStageLog.map((cl: any) =>({
                ErrorDesc : cl?.Error,
                LambdaName : cl?.LambdaName,
                TimeStamp: cl?.TimeStamp
            }))
            toRecord.ClientCode = record.ClientCode;
             record.Absences.forEach((abs: any) => {
                toRecord.AbsenceIncident = abs.AbsenceIncident
             });
             toRecord.IsSaved = record.IsSaved;
            console.log(toRecord);
            toReturn.push(toRecord);
        });
        //groupBy(toReturn, 'ClientCode')
        console.log(toReturn);
        return toReturn;
    }

    private groupBy(arr: any[], property: any) {
        return arr.reduce(function (memo, x) {
            if (!memo[x[property]]) { memo[x[property]] = []; }
            memo[x[property]].push(x);
            return memo;
        }, {});
    }

    public async deleteCallDataAsync(contactId: string) {
        try {
            const params = {
                TableName: this.TableName,
                Key: { ContactId: contactId },
            };
            // //&console.log(`PARAMS FOR CALL: ${JSON.stringify(params)}`);
            var data = await this.delete(params);
            // //&console.warn(`Loaded Call Data from Database for ${contactId}`, call);

            return data;
        } catch (err) {
            this.log.error(err);
            throw err;
        }
    }
}

 