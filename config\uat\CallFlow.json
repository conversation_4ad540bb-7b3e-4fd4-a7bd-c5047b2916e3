{"CallFlowDynamicFlowV1ARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/contact-flow/f0139a0d-ab48-4b2f-afa2-b7d19270ba2a", "CallFlowEnterNextAbsenceDateARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/c6a1ca45-832b-4d7a-9ab0-c88732a14cd2", "CallFlowEnterReasonARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/8fa719c4-a80a-41fe-bb41-96e6a83da8b2", "CallFlowSubmitAbsenceIfErrorARN": "", "CallFlowClosingARN": "", "CallFlowGetOpeningMsgV1ARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/ad14d142-26ba-4c4b-aad2-5cea3b7b120d", "CallFlowEnterEmployeePhoneNumberARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/f5b4c345-be2f-455d-b22e-b06cb0e1bc2f", "CallFlowTransferToCSRARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/cdbbedea-cb34-4289-8d89-1b339ce84c76", "CallFlowEnterAbsenceDateARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/af50c847-df27-476e-a21c-d0c02b1cef15", "CallFlowValidateEmployeeARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/8cf62809-118a-4032-85ff-5449aee6d934", "CallFlowEnterTotalAbsenceDaysARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/eac8bda1-ea15-47f3-85c4-a89a2a59b594", "CallFlowSubmitAbsenceARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/5b8bdd4d-fc51-4752-8d20-45ed965e6b9c", "CallFlowEnterRTWARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/8e0326f2-45ad-4d73-934e-b14c166a5aea", "CallFlowTransferToExternalARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/7d235f89-2cdb-4051-9aa2-0b98f6bc050c", "CallFlowGetOpenLeaveCaseARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/44a3d467-bd5f-483c-9f86-41857a77763d", "CallFlowSTDLeaveARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/47e09f6f-dade-4821-8add-091a2c703d67", "CallFlowLinkAbsenceInAbilitiARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/d6892b80-8566-49ad-b948-31a7112b563d", "CallFlowDynamicFlowV2ARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/contact-flow/3f14b51e-0563-494c-b06f-ce9bda72784e", "CallFlowEnterAbsenceDateV2ARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/8f8f517f-53e4-4f5d-987d-9347eedd0553", "CallFlowEnterNextAbsenceDateV2ARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/df5b38f7-78b0-4d64-b57f-21e79c6201d2", "CallFlowEnterDateRange1ARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/03f1a8c6-b03c-4eb2-aeaf-f4b81587d3d3", "CallFlowEnterDateRange2ARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/flow-module/7b7e1aae-a204-4f00-9a6e-91edd504486b", "CallFlowVoicemailMessageARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/contact-flow/2c065850-f12b-4d70-80a0-8c00ed795b3b", "LambdaValidateEmployeeIdARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateEmployeeId", "LambdaValidateEmployeePhoneNumberARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateEmployeePhoneNumber", "LambdaSubmitAbsenceARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SubmitAbsence", "LambdaGetPrimaryReasonMenuARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-GetPrimaryReasonMenu", "LambdaValidateAbsenceDateARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateAbsenceDate", "LambdaValidateShiftStartTimeARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateShiftStartTime", "LambdaValidateShiftEndTimeARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateShiftEndTime", "LambdaResolveConflictAbsenceARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ResolveConflictAbsence", "LambdaSaveSecondaryReasonARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SaveSecondaryReason", "LambdaAuthenticateEmployeeARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-AuthenticateEmployee", "LambdaSetReturnToWorkARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SetReturnToWork", "LambdaValidateReturnToWorkDateARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateReturnToWorkDate", "LambdaSaveReturnToWorkDateARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SaveReturnToWorkDate", "LambdaGetClientConfigARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-GetClientConfig", "LambdaValidateDoBARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateDoB", "LambdaGetSecondaryReasonMenuARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-GetSecondaryReasonMenu", "LambdaCheckAllAbsReportedARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-CheckAllAbsReported", "LambdaValidateRTWShiftStartTimeARN": "", "LambdaGetOpeningMsgV1ARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-GetOpeningMsgV1", "LambdaSaveTotalAbsenceDaysARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SaveTotalAbsenceDays", "LambdaGetCarrierTransferNumberARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-GetCarrierTransferNumber", "LambdaSaveAbsenceDateARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SaveAbsenceDate", "LambdaClearLastAddedAbsenceDateARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ClearLastAddedAbsenceDate", "LambdaDroppedSessionMonitorARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-DroppedSessionMonitor", "LambdaCheckOpenCasesARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-CheckOpenCases", "LambdaCheckEESTDARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-CheckEESTD", "LambdaSaveSTDResponseARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SaveSTDResponse", "LambdaSaveThirdLevelResponseARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SaveThirdLevelResponse", "LambdaLinkAbsenceInAbilitiARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-CheckCanBeLinked", "LambdaSaveLeaveResponseARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SaveLeaveResponse", "LambdaVMCallbackARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-VmCallback"}