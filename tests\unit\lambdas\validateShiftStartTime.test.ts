import { flowEvent } from "../mocks/mock-all";
import { validateshiftStartHandler } from '@/functions'
import { startDb, stopDb, createTables, deleteTables } from "jest-dynalite";
import {MockAll, TearDown} from '../mocks/mock-all'
import Sinon from "sinon";
jest.setTimeout(60000)

describe('Unit test for app handler',  () => {

    beforeAll(async () => {});
    beforeEach( async() => { 
        MockAll();  
        ////&console.log("In create");
    })

    afterEach ( async () => {
        ////&console.log("In delete");
        TearDown();
        await deleteTables();
        await createTables();
    })
    afterAll(async () => {
        ////&console.log("In stopdb");
        await stopDb();
    })
    it('verifies successful response', async () => {
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ShiftStartTime: '0930',
                AMorPM: 1
              }
          }
            };
        const result = await validateshiftStartHandler(flowObject)
        console.log(result);
        expect(result).toBeDefined();
        expect((result).IsShiftStartTimeValid).toBe(true);
    });

    it('check for failing result', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ShiftStartTime: '0930',
                AMorPM: 2
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await validateshiftStartHandler(flowObject)

        expect(result).toBeDefined();       
        expect((result).IsShiftStartTimeValid).toBe(true);

    });
    it('check for failing result', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ShiftStartTime: '1930',
                AMorPM: 2
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await validateshiftStartHandler(flowObject)

        expect(result).toBeDefined();       
        expect((result).IsShiftStartTimeValid).toBe(false);

    });
    it('check for failing result', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ShiftStartTime: '1930',
                AMorPM: 1
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await validateshiftStartHandler(flowObject)

        expect(result).toBeDefined();       
        expect((result).IsShiftStartTimeValid).toBe(false);

    });
    it('check for failing result', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ShiftStartTime: '2930',
                AMorPM: 1
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await validateshiftStartHandler(flowObject)

        expect(result).toBeDefined();       
        expect((result).IsShiftStartTimeValid).toBe(false);

    });
})
