{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug TypeScript in Node.js",
      "preLaunchTask": "npm: build",
      "program": "${workspaceFolder}/src/devserver/server.ts",
      "protocol": "inspector",
      "outFiles": ["${workspaceFolder}/build/**/*.js"],
      "sourceMaps": true,
      "smartStep": true,
      "internalConsoleOptions": "openOnSessionStart",
      "runtimeArgs": ["--inspect", "--nolazy", "-r", "ts-node/register/transpile-only", "-r", "tsconfig-paths/register"]
      //"args":["-r", "tsconfig-paths/register"]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Mocks in Node.js",
      "program": "${workspaceFolder}/tests/unit/mocks/mock-all.ts",
      "protocol": "inspector",
      "outFiles": ["${workspaceFolder}/build/**/*.js", "${workspaceFolder}/build/**/responses.json"],
      "sourceMaps": true,
      "smartStep": true,
      "internalConsoleOptions": "openOnSessionStart",
      "runtimeArgs": ["--inspect", "--nolazy", "-r", "ts-node/register/transpile-only", "-r", "tsconfig-paths/register"]
      //"args":["-r", "tsconfig-paths/register"]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "debug",
      "cwd": "${workspaceRoot}",
      "runtimeExecutable": "npm",
      "runtimeArgs": ["run-script", "aws-debug"],
      "envFile": "${workspaceFolder}/.env",
      "env": {
          "authApiUrl": "https://abiliti-api-auth.uat.morneaushepell.com/connect/token",
          "grantType": "client_credentials",
          "scopeConfig": "config",
          "scope": "connect",
          "client_id": "transform_api",
          "client_secret": "secret",
          "AWS_DYNAMODB_ENDPOINT": "http://localhost:8000",
          "cacheTableName":"ablarivr-data-cache",
          "AWS_REGION": "us-east-1"
      }
  }
  ]
}
