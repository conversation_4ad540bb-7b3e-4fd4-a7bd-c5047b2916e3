.PHONY: build-RuntimeDependenciesLayer build-lambda-common
.PHONY: build-authenticateEmployee

build-authenticateEmployee:
	$(MAKE) NAME=authenticateEmployee HANDLER=src/functions/authenticateEmployee/index.ts build-lambda-common

build-checkAllAbsenceReported:
	$(MAKE) NAME=checkAllAbsenceReported HANDLER=src/functions/checkAllAbsenceReported/index.ts build-lambda-common

build-checkUSHoliday:
	$(MAKE) NAME=checkUSHoliday HANDLER=src/functions/checkUSHoliday/index.ts build-lambda-common

build-getOpeningMsgV1:
	$(MAKE) NAME=getOpeningMsgV1 HANDLER=src/functions/getOpeningMsgV1/index.ts build-lambda-common

build-resolveConflictAbsence:
	$(MAKE) NAME=authenticateEmployee HANDLER=src/functions/resolveConflictAbsence/index.ts build-lambda-common

build-saveAbsenceDate:
	$(MAKE) NAME=saveAbsenceDate HANDLER=src/functions/saveAbsenceDate/index.ts build-lambda-common

build-saveTotalAbsenceDays:
	$(MAKE) NAME=saveTotalAbsenceDays HANDLER=src/functions/saveTotalAbsenceDays/index.ts build-lambda-common

build-setReturnToWork:
	$(MAKE) NAME=setReturnToWork HANDLER=src/functions/setReturnToWork/index.ts build-lambda-common

build-submitAbsence:
	$(MAKE) NAME=submitAbsence HANDLER=src/functions/submitAbsence/index.ts build-lambda-common

build-validateAbsenceDate:
	$(MAKE) NAME=validateAbsenceDate HANDLER=src/functions/validateAbsenceDate/index.ts build-lambda-common

build-validateDOB:
	$(MAKE) NAME=validateDOB HANDLER=src/functions/validateDOB/index.ts build-lambda-common

build-validateEmployeeId:
	$(MAKE) NAME=validateEmployeeId HANDLER=src/functions/validateEmployeeId/index.ts build-lambda-common

build-validateRTWShiftTime:
	$(MAKE) NAME=validateRTWShiftTime HANDLER=src/functions/validateRTWShiftTime/index.ts build-lambda-common

build-validateReturnToWorkDate:
	$(MAKE) NAME=validateReturnToWorkDate HANDLER=src/functions/validateReturnToWorkDate/index.ts build-lambda-common

build-validateShiftEndTime:
	$(MAKE) NAME=validateShiftEndTime HANDLER=src/functions/validateShiftEndTime/index.ts build-lambda-common

build-validateShiftStartTime:
	$(MAKE) NAME=validateShiftStartTime HANDLER=src/functions/validateShiftStartTime/index.ts build-lambda-common

build-getPrimaryReasonMenu:
	$(MAKE) NAME=getPrimaryReasonMenu HANDLER=src/functions/getPrimaryReasonMenu/index.ts build-lambda-common

build-getSecondaryReasonMenu:
	$(MAKE) NAME=getSecondaryReasonMenu HANDLER=src/functions/getSecondaryReasonMenu/index.ts build-lambda-common

build-saveSecondaryReason:
	$(MAKE) NAME=saveSecondaryReason HANDLER=src/functions/saveSecondaryReason/index.ts build-lambda-common

build-getClientConfig:
	$(MAKE) NAME=getClientConfig HANDLER=src/functions/getClientConfig/index.ts build-lambda-common

build-saveReturnToWorkDate:
	$(MAKE) NAME=saveReturnToWorkDate HANDLER=src/functions/saveReturnToWorkDate/index.ts build-lambda-common

build-clearLastAddedAbsenceDate:
	$(MAKE) NAME=clearLastAddedAbsence HANDLER=src/functions/clearLastAddedAbsenceDate/index.ts build-lambda-common

build-validateEmployeePhoneNumber:
	$(MAKE) NAME=validateEmployeePhoneNumber HANDLER=src/functions/validateEmployeePhoneNumber/index.ts build-lambda-common

build-submitAbsence:
	$(MAKE) NAME=submitAbsence HANDLER=src/functions/submitAbsence/index.ts build-lambda-common

build-getCarrierTransferNumber:
	$(MAKE) NAME=getCarrierTransferNumber HANDLER=src/functions/getCarrierTransferNumber/index.ts build-lambda-common

build-droppedSessionMonitor:
	$(MAKE) NAME=droppedSessionMonitor HANDLER=src/functions/droppedSessionMonitor/index.ts build-lambda-common

build-checkOpenCases:
	$(MAKE) NAME=checkOpenCases HANDLER=src/functions/checkOpenCases/index.ts build-lambda-common

build-saveThirdLevelResponse:
	$(MAKE) NAME=saveThirdLevelResponse HANDLER=src/functions/saveThirdLevelResponse/index.ts build-lambda-common
	
build-saveSTDResponse:
	$(MAKE) NAME=saveSTDResponse HANDLER=src/functions/saveSTDResponse/index.ts build-lambda-common

build-checkEEStd:
	$(MAKE) NAME=checkEEStd HANDLER=src/functions/checkEEStd/index.ts build-lambda-common

build-checkCanBeLinked:
	$(MAKE) NAME=checkCanBeLinked HANDLER=src/functions/checkCanBeLinked/index.ts build-lambda-common

build-saveLeaveResponse:
	$(MAKE) NAME=saveLeaveResponse HANDLER=src/functions/saveLeaveResponse/index.ts build-lambda-common
build-vmCallback:
	$(MAKE) NAME=vmCallback HANDLER=src/functions/vmCallback/index.ts build-lambda-common
# build-getPolicyBalance:
# 	$(MAKE) NAME=getPolicyBalance HANDLER=src/functions/getPolicyBalance/index.ts build-lambda-common

# build-checkFmlaBalanceEnabled:
# 	$(MAKE) NAME=checkFmlaBalanceEnabled HANDLER=src/functions/checkFmlaBalanceEnabled/index.ts build-lambda-common

build-lambda-common: #TODO add path to tsconfig as variable
	rm -rf build
	echo "{\"extends\": \"./tsconfig.json\", \"include\": [\"${HANDLER}\"] }" > ./tsconfig-only-handler.json
	npm run build-production -- --build ./tsconfig-only-handler.json && npm run alias
	cp -r build/* "$(ARTIFACTS_DIR)/" && rm -rf build

build-RuntimeDependenciesLayer:
	npm install
	mkdir -p "$(ARTIFACTS_DIR)/nodejs"
	cp package.json package-lock.json "$(ARTIFACTS_DIR)/nodejs/"
	npm install --production --prefix "$(ARTIFACTS_DIR)/nodejs/"
	rm "$(ARTIFACTS_DIR)/nodejs/package.json" # to avoid rebuilding when changes doesn't relate to dependencies