import { AuthClient, DynamoClient } from 'Mixins'
import {  CallLog, CacheClient, IHttpService } from '@/services'
import { AbsenceIncident, AbsenceDates, MapReasonText, ReasonText, PolicyBalanceModel } from '@/models'


export default class ManageApiService extends DynamoClient(CacheClient){
    AuthenticatedHttpClient?: IHttpService;
    public async getPolicyBalance(contactId: string, clientCode: string, empNumber: string): Promise<PolicyBalanceModel[]>{
        try{
            const mngApiEndpointBase = process.env.manageApiEndpoint;
            const apiEndpoint = mngApiEndpointBase + `policySummary?clientCode=${clientCode}&employeeNumber=${empNumber}`;            
            const response = await this.AuthenticatedHttpClient?.getAsync(apiEndpoint);
            this.Logger.log(
                `API Request: ${apiEndpoint}  parameters:`,
                clientCode,
                response?.data,
              );
            
            return  this.MapResponseToPolicyBalanceModel(response?.data);//this data should have incidentid or validation errors
            }
            catch (error: any) {
                this.Logger.log(error.message);
                throw Error(error);
            }
    }

    private  MapResponseToPolicyBalanceModel(apiResponse: any) : PolicyBalanceModel[]{
        let toReturn: PolicyBalanceModel[] = [];
        if(!apiResponse.items)
        {
            return [];
        }
        if(Array.isArray(apiResponse.items.policies) && apiResponse.items.length > 0)
        {
            let pol: any[] = apiResponse.items.policies as Array<any> ;
            pol.forEach((item: any) =>{
                 toReturn.push({
                    CalculationMethodDescription: item.calculationMethodDescription,
                    Determinations: item.determinations,
                    DisplayHours: item.displayHours,
                    CalculationMethod: item.calculationMethod,
                    Eligibility: item.eligibility,
                    EntitlementDescription: item.entitlementDescription,
                    EntitlementType: item.entitlementType,
                    Planned: item.planned,
                    PlannedHours: item.plannedHours,
                    PolicyCode: item.policyCode,
                    PolicyCodeLocale: item.policyCodeLocale.map((x: any) => ({
                        Locale: x?.locale,
                        Description: x?.description
                      })),
                    Remaining: item.remaining,
                    RemainingHours: item.remainingHours,
                    ResetDate: item.resetDate,
                    Total: item.total,
                    TotalHours: item.totalHours,
                    Unit: item.unit,
                    Used:  item.used,
                    UsedHours: item.usedHours
                 })
            })

        }
           
        return toReturn;
    }
}