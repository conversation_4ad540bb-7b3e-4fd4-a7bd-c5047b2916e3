{"Details": {"ContactData": {"Attributes": {"CallFlowClosingARN": "", "CallFlowDynamicFlowV1ARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/contact-flow/78498ff6-24e1-433c-a281-0aed5c8cc131", "CallFlowEnterAbsenceDateARN": "", "CallFlowEnterEmployeePhoneNumberARN": "", "CallFlowEnterRTWARN": "", "CallFlowEnterReasonARN": "", "CallFlowEnterTotalAbsenceDaysARN": "", "CallFlowGetOpeningMsgV1ARN": "ec9c237c-1a16-4eed-a729-66061cdf0e53", "CallFlowSubmitAbsenceARN": "", "CallFlowSubmitAbsenceIfErrorARN": "", "CallFlowTransferToCSRARN": "", "CallFlowTransferToExternalARN": "", "CallFlowValidateEmployeeARN": "f5467b78-f173-46fc-83b0-06391bdfc027", "DefaultDateFormatType": "1", "DefaultLanguage": "English", "DefaultTimeFormatType": "1", "EmployeeId": "00262843", "Environment": "LAB", "IsAllowInteruppted": "false", "IsEmployeeIdValid": "true", "IsEnterEmployeePhoneNumberRequired": "true", "IsLambdaMaxErrorRetry": "false", "IsMenuMaxInvalidRetry": "false", "LambdaAuthenticateEmployeeARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-AuthenticateEmployee", "LambdaCheckAllAbsReportedARN": "", "LambdaGetCarrierTransferNumberARN": "", "LambdaGetClientConfigARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetClientConfig-temppoc", "LambdaGetOpeningMsgV1ARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetOpeningMsgV1-temppoc", "LambdaGetPrimaryReasonMenuARN": "", "LambdaGetSecondaryReasonMenuARN": "", "LambdaSaveAbsenceDateARN": "", "LambdaSaveSecondaryReasonARN": "", "LambdaSaveTotalAbsenceDaysARN": "", "LambdaSetReturnToWorkARN": "", "LambdaSubmitAbsenceARN": "", "LambdaValidateAbsenceDateARN": "", "LambdaValidateDoBARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateDoB", "LambdaValidateEmployeeIdARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateEmployeeId", "LambdaValidateEmployeePhoneNumberARN": "", "LambdaValidateRTWShiftStartTimeARN": "", "LambdaValidateReturnToWorkDateARN": "", "LambdaValidateShiftEndTimeARN": "", "LambdaValidateShiftStartTimeARN": "", "Marker": "<PERSON><PERSON> After Playing 3rd prompt", "Marker1": "test", "MaxAllowAbsenceDaysFuture": "60", "MaxAllowAbsenceDaysPast": "14", "MaxRetryLambda1": "3", "MaxRetryLambda2": "3", "MaxRetryMenu1": "3", "MaxRetryMenu2": "", "MaxTotalMissedAbsenceDays": "5", "MutliLanguageId": "0", "PreviousAbsenceWithinAA": "2", "PromptEnterEmployeeIdSSML": "<speak><amazon:domain name='conversational'> Please enter your employee I D using your keypad followed by pound key. If you do not know your employee I D, please disconnect and contact your employer to report your absence.</amazon:domain></speak>", "PromptInvalidEmployeeIdSSML1": "<speak><amazon:domain name='conversational'>The Employee ID you entered is</amazon:domain></speak>", "PromptInvalidEmployeeIdSSML2": "<speak><amazon:domain name='conversational'>This does not match with our records. Please try again</amazon:domain></speak>", "PromptLanugageSelectionS3URI": "", "PromptMaxInvalidEmployeeIdSSML": "<speak><amazon:domain name='conversational'>Sorry, your absence cannot be recorded without your correct Employee number Please call back later Goodbye.</amazon:domain></speak>", "PromptOpeningSSML": "<speak><amazon:domain name='conversational'>Thank you for calling the Goodyear Absence Reporting System. Please note that effective 1/1/2023, UNUM administers leave under the Family Medical Leave Act (FMLA) and time away from work due to Short Term Disability (STD) and Accident and Sickness. Please listen carefully as the options might have changed.</amazon:domain></speak>", "PromptSpeicalOpeningMsgSSML": "", "PromptSubOpeningSSML1": "<speak><amazon:domain name='conversational'>Kindly note you can record your absences for 60 days in future OR within previous 2 days. Please note that you can report a maximum of 60 absences per call. You can also report your absences online via Abiliti Absence at goodyear.abilitiabsenceus.com. A confirmation number will be provided to you at the end of the call.</amazon:domain></speak>", "PromptTransferNumberGeneralErrorSSML": "", "PromptUnexpectedErrorSSML": "put ssml here"}, "Channel": "VOICE", "ContactId": "c64a329d-bbc3-465e-86fe-6efb284963b0", "CustomerEndpoint": {"Address": "+16782033318", "Type": "TELEPHONE_NUMBER"}, "CustomerId": null, "Description": null, "InitialContactId": "c64a329d-bbc3-465e-86fe-6efb284963b0", "InitiationMethod": "INBOUND", "InstanceARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330", "LanguageCode": "en-US", "MediaStreams": {"Customer": {"Audio": null}}, "Name": null, "PreviousContactId": "c64a329d-bbc3-465e-86fe-6efb284963b0", "Queue": null, "References": {}, "SystemEndpoint": {"Address": "+16472438396", "Type": "TELEPHONE_NUMBER"}}, "Parameters": {"DateOfBirth": "03091979", "DoBFormatType": "MMDDYYYY"}}, "Name": "ContactFlowEvent"}