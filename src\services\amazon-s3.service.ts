import {S3Client, GetObjectCommand} from '@aws-sdk/client-s3'
import { HttpClient } from './mixins';
import { BaseService } from './service';
import { simpleParser, ParsedMail } from 'mailparser';
import { Readable } from 'stream';
import { ParsedVoiceMail } from '@/models/VoiceMailModel';
const s3client = new S3Client({});

export default class AmazonS3Service extends HttpClient(BaseService) {
    public async GetEmailFromS3(bucketName: string, s3Key: string): Promise<ParsedVoiceMail>{
        let resultParsedEmail: ParsedVoiceMail = {
            Body: '',
            Subject: '',
            Attachment: []
        };
        const command = new GetObjectCommand({
            Bucket: bucketName,
               Key: s3Key
           })
           const s3EmailObject = await s3client.send(command);
           this.Logger.log('Raw email:' + s3EmailObject.Body);

           
        const streamToBuffer = async (stream: Readable): Promise<Buffer> => {
        const chunks: Buffer[] = [];
        for await (const chunk of stream) {
            chunks.push(Buffer.from(chunk));
        }
        return Buffer.concat(chunks);
        };
        
        const emailBuffer = await streamToBuffer(s3EmailObject.Body as Readable);
        
        // Parse the email using mailparser
        const parsedEmail: ParsedMail = await simpleParser(emailBuffer);
        
        this.Logger.log('Parse Email', parsedEmail);
        // Extract the body of the email     

        const emailBody = parsedEmail.html ?String(parsedEmail.html): ""; 
        this.Logger.log('Email body:', emailBody);
        const match = emailBody.match(/<contactId>(.*?)<\/contactId>/);
        const contactId = match ? match[1] : null;
        this.Logger.log("Extracted contactId: ", contactId??"");
        resultParsedEmail.Subject = parsedEmail.subject ?? "";
        resultParsedEmail.Body = contactId ?? "";
        // Process attachments if they exist
        if (parsedEmail.attachments.length > 0) {
            parsedEmail.attachments.forEach((attachment, index) => {
                this.Logger.log(`Attachment ${index + 1}:`, {
                    filename: attachment.filename,
                    contentType: attachment.contentType,
                    size: attachment.size,
                    content: attachment.content
                });
                
                resultParsedEmail.Attachment.push({
                    FileName: attachment.filename ?? "",
                    ContentType: attachment.contentType,
                    Content: attachment.content,
                    Size: attachment.size
                });
            });
        } else {
            this.Logger.log('No attachments found.');
        }
        
        return resultParsedEmail;
    }
}