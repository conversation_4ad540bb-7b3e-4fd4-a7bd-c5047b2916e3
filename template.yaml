AWSTemplateFormatVersion : '2010-09-09'
Transform: 
  - AWS::Serverless-2016-10-31 
        
Description: DEV Abiliti IVR Lambda Stack.
Parameters:  
  AbsenceApiEndpoint:
    Type: String   
  EeApiEndpoint:
    Type: String   
  ConfigApiEndpoint:
    Type: String   
  AuthApiEndpoint:
    Type: String   
  ScopeConfig:
    Type: String
  GrantType:
    Type: String
  Scope:
    Type: String
  ClientId:
    Type: String
  ClientSecret:
    Type: String   
  AwsDynamodbEndpoint:
    Type: String
  IvrCallsTableName:
    Type: String
  CacheTableName:
    Type: String
  ClientConfigTableName:
    Type: String
  ReasonMenuTableName:
    Type: String
  DefaultLanguage:
    Type: String
  SessionMonitorCallInterval:
    Type: Number
  LambdaExecutionRole:
    Type: String
  SecurityGroupId:
    Type: String
  SubnetId:
    Type: String
  InstanceId:
    Type: String
  Environment:
    Type: String
  RefreshLocalDBInMinutes:
    Type: String
  DocumentApiEndpoint:
    Type: String
  SesVMEmailId:
    Type: String
  VoiceEmailBucket:
    Type: String
     

Globals:
  Function:
    VpcConfig:
      SecurityGroupIds:
        -  !Ref SecurityGroupId #sg-066853a8514fbe24e
      SubnetIds:
         -  !Ref SubnetId #subnet-01d38b015e855905e
    Layers:
      - !Ref RuntimeDependenciesLayer
    Tags:
      LOB: adm
      Project: ablarivr
    Environment:
      Variables:
          absenceApiEndpoint: !Ref 'AbsenceApiEndpoint' 
          eeApiEndpoint: !Ref 'EeApiEndpoint' 
          configApiEndpoint: !Ref 'ConfigApiEndpoint' 
          authApiUrl: !Ref 'AuthApiEndpoint' 
          grantType: !Ref 'GrantType' 
          scopeConfig: !Ref 'ScopeConfig' 
          scope: !Ref 'Scope' 
          client_id: !Ref 'ClientId' 
          client_secret: !Ref 'ClientSecret' 
          AWSDYNAMODBENDPOINT: !Ref 'AwsDynamodbEndpoint' 
          defaultLanguage: !Ref 'DefaultLanguage' 
          ivrCallsTableName: !Ref 'IvrCallsTableName' 
          cacheTableName: !Ref 'CacheTableName' 
          clientConfigTableName: !Ref 'ClientConfigTableName' 
          reasonMenuTableName: !Ref 'ReasonMenuTableName' 
          environment: !Ref Environment
          refreshLocalDBInMinutes: !Ref RefreshLocalDBInMinutes
          documentApiEndpoint: !Ref 'DocumentApiEndpoint'

    Runtime: nodejs22.x
    MemorySize: 128
    Timeout: 100

Resources:
  RuntimeDependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Metadata:
      BuildMethod: makefile
    Properties:
      Description: Runtime dependencies for Lambdas
      ContentUri: ./
      CompatibleRuntimes:
        - nodejs22.x
      RetentionPolicy: Retain

  authenticateEmployeeLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-AuthenticateEmployee
      RetentionInDays: 365
  authenticateEmployee:
    Type: AWS::Serverless::Function
    DependsOn: authenticateEmployeeLogGroup
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-AuthenticateEmployee
      Handler: functions/authenticateEmployee.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}" #arn:aws:iam::************:role/ablarivr-lambda-execution-role
      
  PermissionForEventsToInvokeauthenticateEmployee:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "authenticateEmployee"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"      


  checkAllAbsenceReportedLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-CheckAllAbsReported
      RetentionInDays: 365
  checkAllAbsenceReported:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-CheckAllAbsReported
      Handler: functions/checkAllAbsenceReported.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokecheckAllAbsenceReported:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "checkAllAbsenceReported"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"     

  

  #checkUSHoliday:
  #  Type: AWS::Serverless::Function
  #  Metadata:
  #    BuildMethod: makefile
  #  Properties:
  #    FunctionName: 
  #    Handler: functions/checkUSHoliday.handler
  #    Runtime: nodejs22.x
  #    Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"

  getOpeningMsgV1LogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-GetOpeningMsgV1
      RetentionInDays: 365
  getOpeningMsgV1:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-GetOpeningMsgV1
      Handler: functions/getOpeningMsgV1.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokegetOpeningMsgV1:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "getOpeningMsgV1"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"


  resolveConflictAbsenceLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-ResolveConflictAbsence
      RetentionInDays: 365
  resolveConflictAbsence:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ResolveConflictAbsence
      Handler: functions/resolveConflictAbsence.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokeresolveConflictAbsence:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "resolveConflictAbsence"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  

  saveAbsenceDateLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-SaveAbsenceDate
      RetentionInDays: 365
  saveAbsenceDate:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SaveAbsenceDate
      Handler: functions/saveAbsenceDate.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokesaveAbsenceDate:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "saveAbsenceDate"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  

  saveTotalAbsenceDaysLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-SaveTotalAbsenceDays
      RetentionInDays: 365
  saveTotalAbsenceDays:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SaveTotalAbsenceDays
      Handler: functions/saveTotalAbsenceDays.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokesaveTotalAbsenceDays:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "saveTotalAbsenceDays"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  
  

  setReturnToWorkLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-SetReturnToWork
      RetentionInDays: 365
  setReturnToWork:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SetReturnToWork
      Handler: functions/setReturnToWork.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokesetReturnToWork:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "setReturnToWork"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  


  validateAbsenceDateLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-ValidateAbsenceDate
      RetentionInDays: 365
  validateAbsenceDate:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateAbsenceDate
      Handler: functions/validateAbsenceDate.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokevalidateAbsenceDate:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateAbsenceDate"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  


  validateDOBLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-ValidateDOB
      RetentionInDays: 365
  validateDOB:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateDoB
      Handler: functions/validateDOB.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokevalidateDOB:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateDOB"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  

  validateEmployeeIdLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-ValidateEmployeeId
      RetentionInDays: 365
  validateEmployeeId:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateEmployeeId
      Handler: functions/validateEmployeeId.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokevalidateEmployeeId:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateEmployeeId"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  

  # validateRTWShiftTime:
  #   Type: AWS::Serverless::Function
  #   Metadata:
  #     BuildMethod: makefile
  #   Properties:
  #     FunctionName:
  #     Handler: functions/validateRTWShiftTime.handler
  #     Runtime: nodejs22.x
  #     Role: arn:aws:iam::************:role/lifeworks-connect-lambda-execution-rol

  
  validateShiftEndTimeLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-ValidateShiftEndTime
      RetentionInDays: 365
  validateShiftEndTime:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateShiftEndTime
      Handler: functions/validateShiftEndTime.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokevalidatevalidateShiftEndTime:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateShiftEndTime"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  
  validateShiftStartTimeLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-ValidateShiftStartTime
      RetentionInDays: 365
  validateShiftStartTime:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateShiftStartTime
      Handler: functions/validateShiftStartTime.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokevalidatevalidateShiftStartTime:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateShiftStartTime"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  


  getPrimaryReasonMenuLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-GetPrimaryReasonMenu
      RetentionInDays: 365
  getPrimaryReasonMenu:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-GetPrimaryReasonMenu
      Handler: functions/getPrimaryReasonMenu.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
     
  PermissionForEventsToInvokevalidategetPrimaryReasonMenu:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "getPrimaryReasonMenu"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  


  getSecondaryReasonMenuLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-GetSecondaryReasonMenu
      RetentionInDays: 365
  getSecondaryReasonMenu:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-GetSecondaryReasonMenu
      Handler: functions/getSecondaryReasonMenu.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokevalidategetSecondaryReasonMenu:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "getSecondaryReasonMenu"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  

  saveSecondaryReasonLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-SaveSecondaryReason
      RetentionInDays: 365
  saveSecondaryReason:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SaveSecondaryReason
      Handler: functions/saveSecondaryReason.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokevalidatesaveSecondaryReason:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "saveSecondaryReason"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  

  getClientConfigLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-GetClientConfig
      RetentionInDays: 365
  getClientConfig:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-GetClientConfig
      Handler: src/functions/getClientConfig.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokevalidategetClientConfig:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "getClientConfig"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  


  validateReturnToWorkDateLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-ValidateReturnToWorkDate
      RetentionInDays: 365
  validateReturnToWorkDate:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateReturnToWorkDate
      Handler: functions/validateReturnToWorkDate.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokevalidateReturnToWorkDate:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateReturnToWorkDate"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  


  saveReturnToWorkDateLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-SaveReturnToWorkDate
      RetentionInDays: 365
  saveReturnToWorkDate:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SaveReturnToWorkDate
      Handler: functions/saveReturnToWorkDate.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokesaveReturnToWorkDate:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "saveReturnToWorkDate"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  


  validateEmployeePhoneNumberLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-ValidateEmployeePhoneNumber
      RetentionInDays: 365
  validateEmployeePhoneNumber:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ValidateEmployeePhoneNumber
      Handler: functions/validateEmployeePhoneNumber.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokevalidateEmployeePhoneNumber:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "validateEmployeePhoneNumber"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  


  submitAbsenceLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-SubmitAbsence
      RetentionInDays: 365
  submitAbsence:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SubmitAbsence
      Handler: functions/submitAbsence.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokesubmitAbsence:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "submitAbsence"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  

  getCarrierTransferNumberLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-GetCarrierTransferNumber
      RetentionInDays: 365
  getCarrierTransferNumber:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-GetCarrierTransferNumber
      Handler: functions/getCarrierTransferNumber.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokegetCarrierTransferNumber:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "getCarrierTransferNumber"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  clearLastAddedAbsenceDateLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-ClearLastAddedAbsenceDate
      RetentionInDays: 365
  clearLastAddedAbsenceDate:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-ClearLastAddedAbsenceDate
      Handler: functions/clearLastAddedAbsenceDate.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokeclearLastAddedAbsenceDate:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "clearLastAddedAbsenceDate"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"
  checkOpenCasesLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-CheckOpenCases
      RetentionInDays: 365
  checkOpenCases:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-CheckOpenCases
      Handler: functions/checkOpenCases.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"      
  PermissionForEventsToInvokecheckOpenCases:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "checkOpenCases"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"
      
  saveThirdLevelResponseLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-SaveThirdLevelResponse
      RetentionInDays: 365
  saveThirdLevelResponse:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SaveThirdLevelResponse
      Handler: functions/saveThirdLevelResponse.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsTosaveThirdLevelResponse:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "saveThirdLevelResponse"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"
  saveLeaveResponseLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-SaveLeaveResponse
      RetentionInDays: 365
  saveLeaveResponse:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SaveLeaveResponse
      Handler: functions/saveLeaveResponse.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"      
  PermissionForEventsToInvokesaveLeaveResponse:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "saveLeaveResponse"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"
  saveSTDResponseLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-SaveSTDResponse
      RetentionInDays: 365
  saveSTDResponse:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-SaveSTDResponse
      Handler: functions/saveSTDResponse.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsTosaveSTDResponse:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "saveSTDResponse"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"


  checkEEStdLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-CheckEESTD
      RetentionInDays: 365
  checkEEStd:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-CheckEESTD
      Handler: functions/checkEEStd.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsTocheckEEStd:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "checkEEStd"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"

  checkCanBeLinkedLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-CheckCanBeLinked
      RetentionInDays: 365
  checkCanBeLinked:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-CheckCanBeLinked
      Handler: functions/checkCanBeLinked.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsTocheckCanBeLinked:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "checkCanBeLinked"
      Action: lambda:InvokeFunction
      Principal: connect.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
      SourceAccount: !Ref "AWS::AccountId"
  
  # Log group for the vmCallback Lambda function
  vmCallbackLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-VmCallback
      RetentionInDays: 365

  # Lambda function for email processing
  vmCallback:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-VmCallback
      Handler: functions/vmCallback.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      Events:
        SnsEvent:
          Type: SNS
          Properties:
            Topic: !Ref SnsTopic
 # Permissions for SES to invoke the vmCallback Lambda
  PermissionForSesTovmCallback:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "vmCallback"
      Action: lambda:InvokeFunction
      Principal: ses.amazonaws.com
    # Permissions for SNS to invoke the vmCallback Lambda
  
  SnsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: ablarivr-snstopic-voiceEmail
      Tags: 
        - Key: LOB
          Value: adm
        - Key: Project
          Value: ablarivr
  #SNS topic has the correct subscription 
  SnsTopicSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      Protocol: lambda
      TopicArn: !Ref SnsTopic
      Endpoint: !GetAtt vmCallback.Arn
  Snspolicy:
    Type: AWS::SNS::TopicPolicy
    Properties:
      PolicyDocument:
        Id: SnsTopicPolicy
        Version: '2012-10-17'
        Statement:
        - Sid: AllowSESAccess
          Effect: Allow
          Principal:
            Service: ses.amazonaws.com
          Action: [SNS:Publish,SNS:Subscribe]
          Resource: !Ref SnsTopic #"arn:aws:sns:us-east-1:${AWS::AccountId}:ablarivr-snstopic-voiceEmail"
      Topics:
      - !Ref SnsTopic    
  PermissionForSnsTovmCallback:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "vmCallback"
      Action: lambda:InvokeFunction
      Principal: sns.amazonaws.com
      SourceArn: !Ref SnsTopic
  SesReceiptRuleSet:
    Type: AWS::SES::ReceiptRuleSet
    Properties:
      RuleSetName: "AbilitiIVR-VoiceEmail-RuleSet"
  SesReceiptRuleForS3:
    Type: AWS::SES::ReceiptRule
    Properties:
      RuleSetName: !Ref SesReceiptRuleSet
      Rule:
        Name: AbilitiIVR-StoreToS3
        Actions:
          - S3Action:
              BucketName:  !Ref VoiceEmailBucket #!If [CreateNewBucket, !Ref VoiceEmailBucket, !Ref ExistingBucketName]
              ObjectKeyPrefix: "VoiceEMail/"  # Store emails in this folder
          # - SNSAction:  # Add SNS Action
          #     Encoding: Base64
          #     TopicArn: !Ref SnsTopic
        Recipients:
          - !Ref SesVMEmailId  # Replace with the SES-verified email address
        Enabled: true
  

  
  # getPolicyBalanceLogGroup: 
  #   Type: AWS::Logs::LogGroup
  #   Properties:
  #     LogGroupName: /aws/lambda/ABLARIVR-GetPolicyBalance
  #     RetentionInDays: 365
  # getPolicyBalance:
  #   Type: AWS::Serverless::Function
  #   Metadata:
  #     BuildMethod: makefile
  #   Properties:
  #     FunctionName: ABLARIVR-GetPolicyBalance
  #     Handler: functions/getPolicyBalance.handler
  #     Runtime: nodejs22.x
  #     Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  # PermissionForEventsToInvokegetPolicyBalance:
  #   Type: AWS::Lambda::Permission
  #   Properties:
  #     FunctionName: !Ref "getPolicyBalance"
  #     Action: lambda:InvokeFunction
  #     Principal: connect.amazonaws.com
  #     SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
  #     SourceAccount: !Ref "AWS::AccountId"

  # checkFmlaBalanceEnabledLogGroup: 
  #   Type: AWS::Logs::LogGroup
  #   Properties:
  #     LogGroupName: /aws/lambda/ABLARIVR-CheckFmlaBalanceEnabled
  #     RetentionInDays: 365
  # checkFmlaBalanceEnabled:
  #   Type: AWS::Serverless::Function
  #   Metadata:
  #     BuildMethod: makefile
  #   Properties:
  #     FunctionName: ABLARIVR-CheckFmlaBalanceEnabled
  #     Handler: functions/checkFmlaBalanceEnabled.handler
  #     Runtime: nodejs22.x
  #     Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  # PermissionForEventsToInvokecheckFmlaBalanceEnabled:
  #   Type: AWS::Lambda::Permission
  #   Properties:
  #     FunctionName: !Ref "checkFmlaBalanceEnabled"
  #     Action: lambda:InvokeFunction
  #     Principal: connect.amazonaws.com
  #     SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}" #arn:aws:connect:us-east-1:************:instance/fc1f9343-a702-436d-88c9-e44a25c2d330
  #     SourceAccount: !Ref "AWS::AccountId"
 
  droppedSessionMonitorLogGroup: 
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/ABLARIVR-DroppedSessionMonitor
      RetentionInDays: 365 
  droppedSessionMonitor:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      FunctionName: ABLARIVR-DroppedSessionMonitor
      Handler: functions/droppedSessionMonitor.handler
      Runtime: nodejs22.x
      Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/${LambdaExecutionRole}"
      
  PermissionForEventsToInvokedroppedSessionMonitor:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref "droppedSessionMonitor"
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !Sub "arn:aws:connect:${AWS::Region}:${AWS::AccountId}:instance/${InstanceId}"
      SourceAccount: !Ref "AWS::AccountId"
  ScheduledRule:
    Type: AWS::Events::Rule
    Properties:
      Name: ablarivr-droppedSessionMonitorCallEvent
      ScheduleExpression: rate(30 minutes)
      State: 'ENABLED'
      Targets:
        - Arn: 
            Fn::Join:
              - ''
              - - 'arn:aws:lambda:'
                - !Ref AWS::Region
                - ':'
                - !Ref AWS::AccountId
                - ':function:'
                - !Ref "droppedSessionMonitor"
          Id: ABLARIVRScheduledFunction



