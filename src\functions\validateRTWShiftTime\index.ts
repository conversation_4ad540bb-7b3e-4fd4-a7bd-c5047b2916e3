import { ConnectContactFlowEvent } from 'aws-lambda'
import { AbsenceReportingService, CallLog, LocalConfiguration } from '@/services'
import { ValidationErrors, LogService, DateHelper } from '@/util'
import moment from 'moment'

const callService = new CallLog(); 
const localConfig = new LocalConfiguration();
const dateHelper = new DateHelper();

export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'validateRTWShiftStartTime';
    callService.Logger.log('Input data', event);
    let contactId;
    try {
        contactId = event.Details.ContactData.ContactId;
        const rtwShiftStartTime = event.Details.Parameters.RTWShiftStartTime;
        const dayPart = +event.Details.Parameters.AMorPM === 1 ? 'AM' : 'PM';
        const hour = parseInt(rtwShiftStartTime.substring(0, rtwShiftStartTime.length -2), 10);
        const min = parseInt(rtwShiftStartTime.substring(rtwShiftStartTime.length -2, rtwShiftStartTime.length), 10);
        const result = { IsSaveSuccess:false,
            IsRTWShiftStartTimeValid: false };

        const isValidFormat = dateHelper.is12HClockCorrect(hour, min, dayPart === 'AM');
        if (isValidFormat) {
            result.IsRTWShiftStartTimeValid = true;

            const callData = await callService.getCallDataAsync(contactId);
            if (callData.ReturnToWorkDate) {
                const inputTime = `${rtwShiftStartTime} ${dayPart}`;
                const convertedTo24HTime = moment(dateHelper.convertTo24HClock(inputTime), 'HH:mm');
                const returnToWorkDate = moment(callData.ReturnToWorkDate).set({
                    hour: convertedTo24HTime.get('hour'),
                    minute: convertedTo24HTime.get('minute'),
                });
                await callService.setCallDataAsync(contactId, {
                    ReturnToWorkDate: moment(returnToWorkDate).format('YYYY-MM-DD HH:mm:ss'),
                });
            }
        }
        result.IsSaveSuccess = true;
        await callService.logStageAsync(
            contactId,
            lambdaName,
            result.IsRTWShiftStartTimeValid
                ? null
                : { Validation: ValidationErrors.InvalidRTWShiftStartTime },
            result
        );
        callService.Logger.log('Result', result);
        return  result;
    } catch (err: any) {
        callService.Logger.log('Error occured:', err);
        const result = { IsSaveSuccess:false,
            IsRTWShiftStartTimeValid: false };
        return result;
    }
};
