{"ClientSysId": "1", "ClientCode": "GOODYEAR", "ClientName": "GOODYEAR", "CallFlowClosingARN": "", "CallFlowDynamicFlowV1ARN": "arn:aws:connect:us-east-1:117119924730:instance/bc4089af-751b-4a36-afb7-d8340c4227bb/contact-flow/f0139a0d-ab48-4b2f-afa2-b7d19270ba2a", "CallFlowEnterAbsenceDateARN": "af50c847-df27-476e-a21c-d0c02b1cef15", "CallFlowEnterEmployeePhoneNumberARN": "", "CallFlowEnterNextAbsenceDateARN": "c6a1ca45-832b-4d7a-9ab0-c88732a14cd2", "CallFlowEnterReasonARN": "8fa719c4-a80a-41fe-bb41-96e6a83da8b2", "CallFlowEnterRTWARN": "8e0326f2-45ad-4d73-934e-b14c166a5aea", "CallFlowEnterTotalAbsenceDaysARN": "eac8bda1-ea15-47f3-85c4-a89a2a59b594", "CallFlowGetOpeningMsgV1ARN": "ad14d142-26ba-4c4b-aad2-5cea3b7b120d", "CallFlowSubmitAbsenceARN": "5b8bdd4d-fc51-4752-8d20-45ed965e6b9c", "CallFlowSubmitAbsenceIfErrorARN": "", "CallFlowTransferToCSRARN": "cdbbedea-cb34-4289-8d89-1b339ce84c76", "CallFlowTransferToExternalARN": "7d235f89-2cdb-4051-9aa2-0b98f6bc050c", "CallFlowValidateEmployeeARN": "8cf62809-118a-4032-85ff-5449aee6d934", "LambdaValidateEmployeeIdARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateEmployeeId", "LambdaValidateEmployeePhoneNumberARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateEmployeePhoneNumber", "LambdaSubmitAbsenceARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SubmitAbsence", "LambdaGetPrimaryReasonMenuARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-GetPrimaryReasonMenu", "LambdaValidateAbsenceDateARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateAbsenceDate", "LambdaValidateShiftStartTimeARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateShiftStartTime", "LambdaValidateShiftEndTimeARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateShiftEndTime", "LambdaResolveConflictAbsenceARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ResolveConflictAbsence", "LambdaSaveSecondaryReasonARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SaveSecondaryReason", "LambdaAuthenticateEmployeeARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-AuthenticateEmployee", "LambdaSetReturnToWorkARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SetReturnToWork", "LambdaValidateReturnToWorkDateARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateReturnToWorkDate", "LambdaSaveReturnToWorkDateARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SaveReturnToWorkDate", "LambdaGetClientConfigARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-GetClientConfig", "LambdaValidateDoBARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ValidateDoB", "LambdaGetSecondaryReasonMenuARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-GetSecondaryReasonMenu", "LambdaCheckAllAbsReportedARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-CheckAllAbsReported", "LambdaValidateRTWShiftStartTimeARN": "", "LambdaGetOpeningMsgV1ARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-GetOpeningMsgV1", "LambdaSaveTotalAbsenceDaysARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SaveTotalAbsenceDays", "LambdaGetCarrierTransferNumberARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-GetCarrierTransferNumber", "LambdaSaveAbsenceDateARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-SaveAbsenceDate", "LambdaClearLastAddedAbsenceDateARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-ClearLastAddedAbsenceDate", "LambdaDroppedSessionMonitorARN": "arn:aws:lambda:us-east-1:117119924730:function:ABLARIVR-DroppedSessionMonitor", "DefaultDateFormatType": "1", "MaxTotalMissedAbsenceDays": "5", "IsEnterEmployeePhoneNumberRequired": "true", "MaxRetryMenu1": "3", "MaxRetryMenu2": "", "Marker1": "test", "Marker": "Add Value here", "MaxRetryLambda2": "3", "MaxRetryLambda1": "3", "Environment": "UAT", "MaxAllowAbsenceDaysPast": "14", "MaxValidAbsenceDays": "60", "DefaultTimeFormatType": "1", "PreviousAbsenceWithinAA": "2", "MaxAllowAbsenceDaysFuture": "60", "IsLambdaMaxErrorRetry": "false", "IsRTWTimeRequiredForIVR": "false", "DefaultScheduledStartTime": "0900", "DefaultScheduledEndTime": "1700", "IsAllowInteruppted": "false", "MutliLanguageId": "0", "DefaultLanguage": "EN", "PromptUnexpectedErrorSSML": "<speak><amazon:domain name='conversational'>I am sorry, due to an unexpected error I cannot assist you right now. You may try again later or Please contact your supervisor or HR team for further assistance. Goodbye.</amazon:domain></speak>", "PromptLanugageSelectionS3URI": "", "PromptSpecialOpeningMsgSSML": "", "PromptEnterTotalAbsenceDaysSSML1": "<speak><amazon:domain name='conversational'> Please remember that all absences must be reported. Enter the total number of missed absences you're reporting absent on this call. Kindly note that you can report a maximum of 60 absences per call, Only one absence is allowed for the same date. To repeat, press the * key.</amazon:domain></speak>", "PromptInvalidAbsenceDateSSML": "<speak><amazon:domain name='conversational'>The date you entered is not a valid date or it has already been entered.</amazon:domain></speak>", "PromptIsAMorPMSSML": "<speak><amazon:domain name='conversational'>For AM, press 1, For PM, press 2</amazon:domain></speak>", "PromptMaxRetrySSML": "<speak><amazon:domain name='conversational'>I am sorry, you have reached maximum number of retries.</amazon:domain></speak>", "PromptMaxInvalidEmployeeIdSSML": "<speak><amazon:domain name='conversational'>Sorry, your absence cannot be recorded without your correct Employee number Please call back later Goodbye.</amazon:domain></speak>", "PromptInvalidEmployeeIdSSML1": "<speak><amazon:domain name='conversational'>The Employee ID you entered is</amazon:domain></speak>", "PromptSubOpeningSSML1": "<speak><amazon:domain name='conversational'>Kindly note you can record your absences for 60 days in future OR within previous 2 days. Please note that you can report a maximum of 60 absences per call. You can also report your absences online via Abiliti Absence at good year absence.abiliti absence U S.com. A confirmation number will be provided to you at the end of the call.</amazon:domain></speak>", "PromptEnterEmployeeIdSSML": "<speak><amazon:domain name='conversational'> Please enter your employee I D using your keypad followed by pound key or the number sign. If you do not know your employee I D, please disconnect and contact your employer to report your absence.</amazon:domain></speak>", "PromptOpeningSSML": "<speak><amazon:domain name='conversational'>Thank you for calling the Goodyear Absence Reporting System. Please note that effective 1/1/2023, UNUM administers leave under the Family Medical Leave Act (FMLA) and time away from work due to Short Term Disability (STD) and Accident and Sickness. Please listen carefully as the options have recently changed.</amazon:domain></speak>", "PromptTransferNumberGeneralErrorSSML": "", "PromptInvalidEntryAndRetrySSML": "<speak><amazon:domain name='conversational'>Sorry, this is an invalid entry. Please try again.</amazon:domain></speak>", "PromptEnterDobSSML": "<speak><amazon:domain name='conversational'>To confirm your identification, please enter your Date of Birth using 2 digits for the Month, 2 digits for the Day and 4 digits for the Year. For example, February 3rd 1980 would be <say-as interpret-as='digit'>02031980</say-as></amazon:domain></speak>", "PromptIntroPrimaryReasonMenuSSML": "<speak><amazon:domain name='conversational'>Please select the reason for your absence from one of the following options. If you or your family member is hospitalized or you expect your absence to be greater than 3 days due to an illness or injury,  Please select option number 2.</amazon:domain></speak>", "PromptIntroSecondaryReasonMenuSSML": "<speak><amazon:domain name='conversational'>Please select from one of the following options.</amazon:domain></speak>", "PromptCarrierTransferMsgSSML": "<speak><amazon:domain name='conversational'> Thank you for reporting your absence. You will now be transferred to UNUM. Your absence will be reported to your manager and will be reviewed for compliance with local policies and procedures. Please remember that if you have any questions about applying for leave or about an open claim, please contact your manager or HR representative. </amazon:domain></speak>", "PromptCompletedAbsenceSSML": "<speak><amazon:domain name='conversational'> Thank you for reporting your absence. Your absence will be reported to your manager and will be reviewed for compliance with local policies and procedures. If you have any questions, please contact your manager or HR representative </amazon:domain></speak>", "PromptInvalidEmployeeIdSSML2": "<speak><amazon:domain name='conversational'>This does not match with our records. Please try again</amazon:domain></speak>", "PromptEnterAbsenceDateSSML": "<speak><amazon:domain name='conversational'> Please enter your absence date. Use two digits for the Month, two digits for the Day and 4 digits for the Year. For example, February 3rd would be 0 2 0 3 2 0 2 2. To repeat, press the star key.</amazon:domain></speak>", "PromptEnterStartShiftTimeSSML": "<speak><amazon:domain name='conversational'> Please enter the start time of your absence. Enter the hour and then the minutes. For example, to enter 7 30 AM, please enter 0 7 3 0. To enter 10 PM, please enter 10 00. To repeat, press the star key. </amazon:domain></speak>", "PromptEnterEndShiftTimeSSML": "<speak><amazon:domain name='conversational'>Please enter the end time of your absence. Enter the hour and then the minutes. For example, to enter five PM, please enter 0 5 0 0. To enter 10 PM, please enter 10 00. To repeat, press the star key. </amazon:domain></speak>", "PromptIsRTWKnownSSML": "<speak><amazon:domain name='conversational'>Do you know when will you return to work? if Yes, press 1, if No, press 2. </amazon:domain></speak>", "PromptEnterReturnToWorkSSML": "<speak><amazon:domain name='conversational'>Please use two digits for the Month, two digits for the Day and 4 digits for the Year. For example, August 2nd would be 0 8 0 2 2 0 2 2. To repeat, press the * key.</amazon:domain></speak>", "PromptProvideConfirmNumberSSML1": "<speak><amazon:domain name='conversational'> The confirmation number for your absence is <say-as interpret-as='telephone'> $.Attributes.ConfirmationNumber </say-as> <break time='500ms'/> To repeat your confirmation number, press 1. To acknowledge, press 2.</amazon:domain></speak>", "PromptTransferToAgentSSML": "<speak><amazon:domain name='conversational'>You are being transferred to a live agent to better assist you, please do not hang up-an agent will be on the line shortly. </amazon:domain></speak>", "PromptTransferToCarrierSSML": "<speak><amazon:domain name='conversational'>Please stay on the line as we will now be transferring you to your carrier. </amazon:domain></speak>", "PromptProvideConfirmNumberSSML2": "<speak><amazon:domain name='conversational'>To repeat your confirmation number, press 1.</amazon:domain></speak>", "PromptEnterEmployeePhoneNumberSSML": "<speak><amazon:domain name='conversational'>Enter Phone number where you can be reached. </amazon:domain></speak>", "PromptConfirmAbsenceDateSSML1": "<speak><amazon:domain name='conversational'>The absence is recorded for <say-as interpret-as='date' format='mmdd'> $.Attributes.ReportAbsenceDatePlayable </say-as> <break time='500ms'/> Press 1 if this is correct. Press 2 if this is incorrect.</amazon:domain></speak>", "PromptConfirmReturnToWorkSSML1": "<speak><amazon:domain name='conversational'> We have your expected return to work date as <say-as interpret-as='date' format='mmdd'> $.Attributes.ReturnDayToWorkPlayable </say-as> <break time='500ms'/> Press 1, if this is correct. Press 2, if this is incorrect </amazon:domain></speak>", "PromptFailTrasnferToCarrierSSML": "<speak><amazon:domain name='conversational'> We are unable to transfer your call at this time. Since you have reported an absence related to Short-Term Disability Accident and Sickness or FMLA, you are required to call UNUM at ************** or visit w w w.unum.com to provide additional information. </amazon:domain></speak>", "PromptInvalidReturnToWorkSSML": "<speak><amazon:domain name='conversational'>Your Return to Work Date must be after your last absence date. Press 1 to re-enter your Return to Work Date for a date that is after your last absence date. Press 2 to re-enter your Absent Date. To repeat, press the * key.</amazon:domain></speak>", "PromptIsReturnToWorkSSML1": "<speak><amazon:domain name='conversational'> Our records indicate that you have had a recent absence on <say-as interpret-as='date' format='mmdd'> $.Attributes.LastAbsenceDate </say-as>,  Press 1 if you have returned to work since this date. Or Press 2, if you have not returned to work </amazon:domain></speak>", "PromptTransferToDisconnectSSML": "<speak><amazon:domain name='conversational'>To better assist you, please call your supervisor or HR to report your absence. </amazon:domain></speak>", "PromptValidateIDandDobFailSSML": "<speak><amazon:domain name='conversational'>I am sorry the Employee ID and Date of Birth you entered do not match with our records. Please try again.</amazon:domain></speak>", "CarrierTransferNumber": "+18886210038", "TransferNumberGeneralError": "+15012355529", "sessionMonitorCallInterval": "30"}