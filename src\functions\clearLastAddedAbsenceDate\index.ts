'use strict';
import {ConnectContactFlowEvent} from 'aws-lambda';
import {CallLog, AbsenceReportingService} from '@/services';
import {DateFormats} from '@/util';

const callService = new CallLog();
const reportingService = new AbsenceReportingService();
reportingService.CallLog = callService;

export const handler = async(event: ConnectContactFlowEvent) =>{
    const lambdaName = 'clearLastAddedAbsenceDate';
    reportingService.Logger.log('Input data', event);
    let contactId;
    let  isSaveSuccess: boolean = false;
    try {
        contactId =  event.Details.ContactData.ContactId;
        let inputDate = event.Details.Parameters.ReportAbsenceDate;
        let dateFormat = DateFormats(event.Details.Parameters.AbsenceDateFormatType);
        
        let sessionData = await callService.getCallDataAsync(contactId);
        reportingService.Logger.log('Session stored Absence object', JSON.stringify(sessionData.Absences));
        let currentIncident = sessionData.Absences.pop();
        
        if(currentIncident && currentIncident.AbsenceIncident){

             currentIncident.AbsenceIncident.AbsenceDates.pop();
             sessionData.Absences.push(currentIncident);
        }

        if (await callService.setCallDataAsync(contactId, sessionData))
        isSaveSuccess = true;
        return {IsSaveSuccess: isSaveSuccess}
    }
   
    
    catch(error: any){
        reportingService.Logger.error('Error ', error);
       return {IsSaveSuccess: isSaveSuccess}
    }
}