import { CallflowConfig } from "@/models/ClientConfigModel";
import { BaseService } from "./service";
import {DynamoClient} from '@/services/mixins'

//FUTRURE: DECIDE IMPLEMENTATION (EXTEND SYNTAX ALLOWS INTRODUCTION OF PROPERTIES, DECORATOR DOES NOT)
/**
 * Services any lambda with 
 * *clientconfig - from AWS Connect dynamoDB
 * 
 */

export class CallFlowConfig extends DynamoClient(BaseService) {
    get TableName() {
        return  process.env.callConfigTableName || "ablarivr-callflow-config"
    }

    public async putCallFlowConfig(instance: string, callConfig: CallflowConfig){
        try {      
            var result = await this.delete({
              TableName: this.TableName,
              Key: { Instance: `${instance}`},
            });
            await this.put({
              TableName: this.TableName,
              Item: { Instance: `${instance}`, ...callConfig },
            });   

         } catch (err) {
             this.log.log(err);
             throw err;
         }
    }
    public async getCallConfigAsync(instance: string): Promise<any>{
        const params = {
            TableName: this.TableName,
            Key: { Instance: instance },
        };
        try{
            var data = await this.get(params);
            return data;
        }
        catch (err) {
            console.log(err);
            this.log.error(err);
            throw err;
        }
    }

}