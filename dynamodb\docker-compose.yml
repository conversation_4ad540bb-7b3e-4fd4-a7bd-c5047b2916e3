version: '3.8'

services:
  dynamo-db-local:
    image: "amazon/dynamodb-local:latest"
    container_name: dynamo-db-local
    ports:
      - "8000:8000"
    command: " -jar DynamoDBLocal.jar -sharedDb -dbPath ."
  
  
  aws-cli:
    image: local-aws-cli
    build:
      context: ./aws-cli
    volumes: 
      - ./aws-cli/create-tables.sh:/aws/create-tables.sh
    depends_on:
      - dynamo-db-local
    env_file: #Not necessary if ENV variables are loaded in container
      - .env