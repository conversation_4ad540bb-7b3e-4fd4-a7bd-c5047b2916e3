import { BaseService, IService, IHttpService, IAuthService, IDBService } from './service'
import { LocalConfiguration } from './configuration.service'
import CacheClient from './cache-service'
import CallLog from './call-log-service'
import EmployeeApiService from './employee-api-service'
import AbsenceReportingService from './absence-reporting.service'
import { PublicHoliday } from './public-holiday.service'
import AbsenceApiService from './absence-api.service'
import AmazonPinpointservice from './amazon-pinpoint.service'
import { ReasonMenuService } from './reasonmenu-menu-service'
import { ScopedHttpService } from './scoped-http-client'
import ManageApiService from './manage-api.service'
import { CallFlowConfig } from './callFlow-service'
import AmazonS3Service from './amazon-s3.service'
import DocumentApiService from './document-api.service'
export {
        BaseService, 
        LocalConfiguration, 
        IService, 
        IHttpService, 
        CacheClient, 
        IAuthService, 
        EmployeeApiService, 
        AbsenceReportingService, 
        CallLog,
        PublicHoliday,
        IDBService,
        AbsenceApiService,
        AmazonPinpointservice,
        ReasonMenuService,
        ScopedHttpService,
        ManageApiService,
        CallFlowConfig,
        AmazonS3Service,
        DocumentApiService
    }
