import { ConnectContactFlowEvent } from 'aws-lambda';
import { CallLog, AbsenceReportingService, AbsenceApiService, ScopedHttpService, LocalConfiguration } from '@/services';
import { ClientConfigWrapper, PageModel, PageSectionModel } from '@/models';
import { Constants } from '@/util';
import { SimpleObject } from '@/types';

const callService = new CallLog();
const absenceService = new AbsenceReportingService();
const localConfig = new LocalConfiguration();
const apiService = new AbsenceApiService();
absenceService.CallLog = callService;
export const handler = async (event: ConnectContactFlowEvent) => {
  const lambdaName = 'saveThirdLevelResponse';
  callService.Logger.log('Input data', event);
//change code to check if warning msg enabled in iVR config
  const clientCode = event.Details.Parameters.ClientCode;
  const contactId = event.Details.ContactData.ContactId;
  const selectedAnswer: number = parseInt(event.Details.Parameters.SelectedThirdLevelResponse,);
  callService.Logger.log(`Selected Warning msg response - ${selectedAnswer}`);
  const callData = await callService.getCallDataAsync(contactId);
  let thirdLvlAlert = '';
  localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
  let config: ClientConfigWrapper = await localConfig.getClientConfigAsync(callData.ClientCode);
  const selectedLang = callData.SelectedLang ?? 'EN';
  const currentIncident = (callData.Absences)?.peekLast();
  if (currentIncident && currentIncident.AbsenceIncident ) {
    const QName: string = callData.ThirdLevelMenu?.QuestionName;

    let thirdLvlResponse : SimpleObject= {} ;   
    thirdLvlResponse[QName] = callData.ThirdLevelMenu?.Options[selectedAnswer];
    currentIncident.AbsenceIncident.QuestionResponses = currentIncident.AbsenceIncident.QuestionResponses? {...currentIncident.AbsenceIncident.QuestionResponses, ...thirdLvlResponse} : {thirdLvlResponse};

    callService.Logger.log("Updating absence and saving with warning question responses");
    //sending update to get alert 
    if(config.clientConfig.SpecialMessageAlertEnabled)
    {
      let updatedIncident = await apiService.updateIncidentAsync(callData.ClientCode, contactId, currentIncident.AbsenceIncident );
      let companyQuestions: any = updatedIncident.QuestionReponse?.filter((question: PageModel) => question.PageId === Constants.COMPANY_QUESTION_PAGE_ID).peekLast()  ;
      let section: PageSectionModel = companyQuestions.Sections?.filter((sec: PageSectionModel) => sec.SectionId === Constants.COMPANY_QUESTION_PAGE_ID).peekLast();
      let warningQuestion = section.Questions?.filter((q) => q.Name === QName).peekLast();
      thirdLvlAlert = config.getPromptText(warningQuestion?.Alerts?.peekLast()?.AlertText, selectedLang);
      currentIncident.AbsenceIncident = { ...currentIncident.AbsenceIncident, ...updatedIncident };
    }
    callData.Absences.pop();
    callData.Absences.push(currentIncident);
    await callService.setCallDataAsync(contactId, {
      IsSaved: "true",
      Absences: callData.Absences 
    });
  }

return {IsSaveSuccess: true,
  PromptThirdLevelAlertSSML: thirdLvlAlert}

}