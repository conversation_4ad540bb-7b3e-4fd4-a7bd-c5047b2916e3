'use strict';
import moment from 'moment';
import { ConnectContactFlowEvent } from 'aws-lambda';
import { CallLog, LocalConfiguration, ScopedHttpService } from '@/services';
import {LogService, ValidationErrors, DateFormats} from '@/util';

const callService = new CallLog();
const localConfig = new LocalConfiguration();
const log = new LogService({ logToConsole: process.env.Debug });
export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'validateDoB';
    callService.Logger.log('Input data', event);
    let contactId;
    try {
        contactId = event.Details.ContactData.ContactId;
        const clientCode = event.Details.Parameters.ClientCode;
        let callData = await callService.getCallDataAsync(contactId);
        const locale = callData?.SelectedLang ?? "EN";
        localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
        const config = await localConfig.getClientConfigAsync(clientCode, locale);        
        const inputDate = event.Details.Parameters.DateOfBirth;
        const dtFormat = config.clientConfig.ClientDateFormat || DateFormats(event.Details.Parameters?.DoBFormatType) || 'MMDDYYYY';
        const IsDoBValid = moment(inputDate, dtFormat, true).isValid();
        
        await callService.logStageAsync(
            contactId,
            lambdaName,
            IsDoBValid ? null : {Validation: ValidationErrors.InvalidDoB},
            IsDoBValid,
        );
       
        return { IsDoBValid: IsDoBValid };
    } catch (error: any) {
        callService.Logger.error('Error ', error);
        return {
            message: error.message, 
            IsDoBValid: null}
    }
};