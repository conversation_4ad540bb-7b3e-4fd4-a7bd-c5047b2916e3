import { ConnectContactFlowEvent } from "aws-lambda";
import { CallLog, LocalConfiguration, ScopedHttpService } from '@/services'
import { PromptHelper } from "@/util";

const callService = new CallLog();
const localConfig = new LocalConfiguration();


export const handler = async(event: ConnectContactFlowEvent) => {

    const contactId = event.Details.ContactData.ContactId;
    const callData = await callService.getCallDataAsync(contactId);
    localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
    const config = await localConfig.getClientConfigAsync(callData.ClientCode);
    let promptCarrierTransferSSML = ''
    let carrierTransferNumber = '';
    if(config && callData){
        promptCarrierTransferSSML = config.getPromptText(config.clientConfig.CarrierTransferText, callData.SelectedLang) ?? "";
        carrierTransferNumber =  config.clientConfig.CarrierTransferNumber ?? "";
    }
    
   
    //get carrier number from config in future
    return {
        "PromptCarrierTransferSSML":PromptHelper.wrapSSML([`${promptCarrierTransferSSML}`]),
        "CarrierTransferNumber":carrierTransferNumber
        
    };   

}