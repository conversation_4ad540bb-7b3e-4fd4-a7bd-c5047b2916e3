# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

# Hardcoded values for now for LAB ENV usage.
# TODO:
#  - Variables for different environments
#  - Loop for template tasks
#  - Add testing step 

trigger:
    - develop
pool:
    vmImage: 'ubuntu-latest'

variables:
  # - group: ablarivr_QA
  - name: Region
    value: "us-east-1"
  - name: AwsBucket
    value: "aws-sam-cli-managed-default-samclisourcebucket-1v4nfxl2hcnyc"
  - name: StackName
    value: "ABLARIVR-Lambda-stack-dev"
  - name: AwsCredentials
    value: "lifeworks-connect-lab"
  # - name: DdStackName
  #   value: "ABLARIVR-Datadog-serverless-macro"
  # - name: DdSite
  #   value: "us3.datadoghq.com"
  # - name: DdTemplateUrl
  #   value: "https://datadog-cloudformation-template.s3.amazonaws.com/aws/serverless-macro/latest.yml"

  # - name: Dd<PERSON><PERSON><PERSON>ey
  #   value: "********************************"
  
  

stages:
  - stage: DeployQA
    displayName: Deploy to QA
    jobs:
      - job: BuildAndDeploy
        displayName: Build and Deploy
        variables: 
          - group: ablarivr_QA
          # - name: DdApiKey
          #   value: "********************************"
          # - name: DdStackName
          #   value: "ABLARIVR-Datadog-serverless-macro"
        steps:
          - checkout: self
          - task: S3Upload@1
            displayName: Create Deploy Bucket
            inputs:
              awsCredentials: $(AwsCredentials)
              bucketName: $(AwsBucket)
              regionName: $(Region)
              globExpressions: "undefined"
              createBucket: true

          - task: NodeTool@0
            inputs:
              versionSource: 'spec'
              versionSpec: '18.x'
              checkLatest: true
          
          # - task: AWSShellScript@1
          #   displayName: Package and deploy Datadog macro 
          #   inputs:
          #     awsCredentials: $(AwsCredentials)
          #     regionName: $(Region)
          #     scriptType: "inline"
          #     inlineScript: |
          #       curl -O $(DdTemplateUrl)
          #       sam deploy \
          #       --template-file latest.yml \
          #       --no-confirm-changeset \
          #       --no-fail-on-empty-changeset \
          #       --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
          #       --stack-name $(DdStackName) \
          #       --s3-bucket $(AwsBucket) \
          #       --s3-prefix $(DdStackName) \
          #       --parameter-overrides DdSite=$(DdSite) DdApiKey=$(DdApiKey) DdStackName=$(DdStackName) \


          - task: AWSShellScript@1
            displayName: Package
            env:
              workingDir: "$(System.DefaultWorkingDirectory)"
            inputs:
              awsCredentials: $(AwsCredentials)
              regionName: $(Region)
              scriptType: 'inline'
              inlineScript: |
                echo $workingDir &&
                sam build --debug \
                -s . \
                --template-file ./template.yaml
          
          - task: AWSShellScript@1
            displayName: Deploy Infrastructure
            inputs:
              awsCredentials: $(AwsCredentials)
              regionName: $(Region)
              scriptType: "inline"
              inlineScript: |                
                sam deploy \
                --template-file .aws-sam/build/template.yaml \
                --no-confirm-changeset \
                --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
                --stack-name $(StackName) \
                --s3-bucket $(AwsBucket) \
                --s3-prefix $(StackName) \
                --parameter-overrides ParameterKey=EeApiEndpoint,ParameterValue=$(EeApiEndpoint) ParameterKey=AbsenceApiEndpoint,ParameterValue=$(AbsenceApiEndpoint) ParameterKey=ConfigApiEndpoint,ParameterValue=$(ConfigApiEndpoint) ParameterKey=AuthApiEndpoint,ParameterValue=$(AuthApiEndpoint) ParameterKey=GrantType,ParameterValue=$(GrantType) ParameterKey=ScopeConfig,ParameterValue=$(ScopeConfig) ParameterKey=Scope,ParameterValue=$(Scope) ParameterKey=ClientId,ParameterValue=$(ClientId) ParameterKey=ClientSecret,ParameterValue=$(ClientSecret) ParameterKey=AwsDynamodbEndpoint,ParameterValue=$(AwsDynamodbEndpoint) ParameterKey=DefaultLanguage,ParameterValue=$(DefaultLanguage)  ParameterKey=IvrCallsTableName,ParameterValue=$(IvrCallsTableName)  ParameterKey=CacheTableName,ParameterValue=$(CacheTableName)  ParameterKey=ClientConfigTableName,ParameterValue=$(ClientConfigTableName) ParameterKey=ReasonMenuTableName,ParameterValue=$(ReasonMenuTableName) \

# --parameter-overrides ParameterKey=DdStackName,ParameterValue=$(DdStackName) ParameterKey=DdSite,ParameterValue=$(DdSite) ParameterKey=DdApiKey,ParameterValue=$(DdApiKey) ParameterKey=EeApiEndpoint,ParameterValue=$(EeApiEndpoint) ParameterKey=AbsenceApiEndpoint,ParameterValue=$(AbsenceApiEndpoint) ParameterKey=ConfigApiEndpoint,ParameterValue=$(ConfigApiEndpoint) ParameterKey=AuthApiEndpoint,ParameterValue=$(AuthApiEndpoint) ParameterKey=GrantType,ParameterValue=$(GrantType) ParameterKey=ScopeConfig,ParameterValue=$(ScopeConfig) ParameterKey=Scope,ParameterValue=$(Scope) ParameterKey=ClientId,ParameterValue=$(ClientId) ParameterKey=ClientSecret,ParameterValue=$(ClientSecret) ParameterKey=AwsDynamodbEndpoint,ParameterValue=$(AwsDynamodbEndpoint) ParameterKey=DefaultLanguage,ParameterValue=$(DefaultLanguage)  ParameterKey=IvrCallsTableName,ParameterValue=$(IvrCallsTableName)  ParameterKey=CacheTableName,ParameterValue=$(CacheTableName)  ParameterKey=ClientConfigTableName,ParameterValue=$(ClientConfigTableName) ParameterKey=ReasonMenuTableName,ParameterValue=$(ReasonMenuTableName) \

                
                
  # - stage: DeployUAT
  #   displayName: Deploy to UAT
  #   jobs:
  #     - job: BuildAndDeploy
  #       displayName: Build and Deploy
  #       variables: 
  #         - group: ablarivr_UAT
  #       steps:
  #         - task: S3Upload@1
  #           displayName: Create Deploy Bucket
  #           inputs:
  #             awsCredentials: $(AwsCredentials)
  #             bucketName: $(AwsBucket)
  #             regionName: $(Region)
  #             globExpressions: "undefined"
  #             createBucket: true

  #         - task: NodeTool@0
  #           inputs:
  #             versionSource: 'spec'
  #             versionSpec: '18.x'
  #             checkLatest: true
          
  #         - task: AWSShellScript@1
  #           displayName: Package and deploy Datadog macro 
  #           inputs:
  #             awsCredentials: $(AwsCredentials)
  #             regionName: $(Region)
  #             scriptType: "inline"
  #             inlineScript: |
  #               curl -O $(DdTemplateUrl)
  #               sam deploy \
  #               --template-file latest.yml \
  #               --no-confirm-changeset \
  #               --no-fail-on-empty-changeset \
  #               --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
  #               --stack-name $(DdStackName) \
  #               --s3-bucket $(AwsBucket) \
  #               --s3-prefix $(DdStackName) \
  #               --parameter-overrides DdSite=$(DdSite) DdApiKey=$(DdApiKey) DdStackName=$(DdStackName) \


  #         - task: AWSShellScript@1
  #           displayName: Package
  #           env:
  #             workingDir: "$(System.DefaultWorkingDirectory)"
  #           inputs:
  #             awsCredentials: $(AwsCredentials)
  #             regionName: $(Region)
  #             scriptType: 'inline'
  #             inlineScript: |
  #               echo $workingDir &&
  #               sam build --debug \
  #               -s . \
  #               --template-file ./template.yaml
          
  #         - task: AWSShellScript@1
  #           displayName: Deploy Infrastructure
  #           env:
  #             Dir: "$(Build.SourcesDirectory)"
  #           inputs:
  #             awsCredentials: $(AwsCredentials)
  #             regionName: $(Region)
  #             scriptType: "inline"
  #             inlineScript: |                
  #               sam deploy \
  #               --template-file .aws-sam/build/template.yaml \
  #               --no-confirm-changeset \
  #               --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
  #               --stack-name $(StackName) \
  #               --s3-bucket $(AwsBucket) \
  #               --s3-prefix $(StackName) \
  #               --parameter-overrides ParameterKey=DdSite,ParameterValue=$(DdSite) ParameterKey=DdApiKey,ParameterValue=$(DdApiKey) ParameterKey=eeApiEndpoint,ParameterValue=$(EeApiEndpoint) ParameterKey=absenceApiEndpoint,ParameterValue=$(AbsenceApiEndpoint) ParameterKey=configApiEndpoint,ParameterValue=$(ConfigApiEndpoint) ParameterKey=authApiUrl,ParameterValue=$(AuthApiEndpoint) ParameterKey=grantType,ParameterValue=$(GrantType) ParameterKey=scopeConfig,ParameterValue=$(ScopeConfig) ParameterKey=scope,ParameterValue=$(Scope) ParameterKey=clientid,ParameterValue=$(ClientId) ParameterKey=clientsecret,ParameterValue=$(ClientSecret) ParameterKey=AWSDYNAMODBENDPOINT,ParameterValue=$(AwsDynamodbEndpoint) ParameterKey=defaultLanguage,ParameterValue=$(DefaultLanguage)  ParameterKey=ivrCallsTableName,ParameterValue=$(IvrCallsTableName)  ParameterKey=cacheTableName,ParameterValue=$(CacheTableName)  ParameterKey=clientConfigTableName,ParameterValue=$(ClientConfigTableName) ParameterKey=reasonMenuTableName,ParameterValue=$(ReasonMenuTableName)   \
  #               --config-env uat

  # - stage: DeployPRD
  #   displayName: Deploy to PRD
  #   jobs:
  #     - job: BuildAndDeploy
  #       displayName: Build and Deploy
  #       variables: 
  #         - group: ablarivr_PRD
  #       steps:
  #         - task: S3Upload@1
  #           displayName: Create Deploy Bucket
  #           inputs:
  #             awsCredentials: $(AwsCredentials)
  #             bucketName: $(AwsBucket)
  #             regionName: $(Region)
  #             globExpressions: "undefined"
  #             createBucket: true

  #         - task: NodeTool@0
  #           inputs:
  #             versionSource: 'spec'
  #             versionSpec: '18.x'
  #             checkLatest: true
          
  #         - task: AWSShellScript@1
  #           displayName: Package and deploy Datadog macro 
  #           inputs:
  #             awsCredentials: $(AwsCredentials)
  #             regionName: $(Region)
  #             scriptType: "inline"
  #             inlineScript: |
  #               curl -O $(DdTemplateUrl)
  #               sam deploy \
  #               --template-file latest.yml \
  #               --no-confirm-changeset \
  #               --no-fail-on-empty-changeset \
  #               --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
  #               --stack-name $(DdStackName) \
  #               --s3-bucket $(AwsBucket) \
  #               --s3-prefix $(DdStackName) \
  #               --parameter-overrides DdSite=$(DdSite) DdApiKey=$(DdApiKey) DdStackName=$(DdStackName) \


  #         - task: AWSShellScript@1
  #           displayName: Package
  #           env:
  #             workingDir: "$(System.DefaultWorkingDirectory)"
  #           inputs:
  #             awsCredentials: $(AwsCredentials)
  #             regionName: $(Region)
  #             scriptType: 'inline'
  #             inlineScript: |
  #               echo $workingDir &&
  #               sam build --debug \
  #               -s . \
  #               --template-file ./template.yaml
          
  #         - task: AWSShellScript@1
  #           displayName: Deploy Infrastructure
  #           env:
  #             Dir: "$(Build.SourcesDirectory)"
  #           inputs:
  #             awsCredentials: $(AwsCredentials)
  #             regionName: $(Region)
  #             scriptType: "inline"
  #             inlineScript: |                
  #               sam deploy \
  #               --template-file .aws-sam/build/template.yaml \
  #               --no-confirm-changeset \
  #               --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
  #               --stack-name $(StackName) \
  #               --s3-bucket $(AwsBucket) \
  #               --s3-prefix $(StackName) \
  #               --parameter-overrides ParameterKey=DdSite,ParameterValue=$(DdSite) ParameterKey=DdApiKey,ParameterValue=$(DdApiKey) ParameterKey=eeApiEndpoint,ParameterValue=$(EeApiEndpoint) ParameterKey=absenceApiEndpoint,ParameterValue=$(AbsenceApiEndpoint) ParameterKey=configApiEndpoint,ParameterValue=$(ConfigApiEndpoint) ParameterKey=authApiUrl,ParameterValue=$(AuthApiEndpoint) ParameterKey=grantType,ParameterValue=$(GrantType) ParameterKey=scopeConfig,ParameterValue=$(ScopeConfig) ParameterKey=scope,ParameterValue=$(Scope) ParameterKey=clientid,ParameterValue=$(ClientId) ParameterKey=clientsecret,ParameterValue=$(ClientSecret) ParameterKey=AWSDYNAMODBENDPOINT,ParameterValue=$(AwsDynamodbEndpoint) ParameterKey=defaultLanguage,ParameterValue=$(DefaultLanguage)  ParameterKey=ivrCallsTableName,ParameterValue=$(IvrCallsTableName)  ParameterKey=cacheTableName,ParameterValue=$(CacheTableName)  ParameterKey=clientConfigTableName,ParameterValue=$(ClientConfigTableName) ParameterKey=reasonMenuTableName,ParameterValue=$(ReasonMenuTableName)   \
  #               --config-env prod
                
                              
  #       # 
      