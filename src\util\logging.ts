 import util from 'node:util'
 type CallbackFunctionVariadic = (...args: any[]) => any;
 export class LogService {

    private logOptions: any = { logToConsole: true };
    get isTest(): boolean { return process.env.JEST_WORKER_ID ? true : false }
    constructor(options?: any) {
       if(options && options != null)
        this.logOptions = options;
    }

    public isTrue(val: any): boolean {
        return val === true || val === 'true';
    }

    public log(...args: any[]): void {
        if (this.isTrue(this.logOptions?.logToConsole)) {
            this.applyLogLevel(console, console.log, args);
        }
    }

    public warn(...args: any[]) : void {
        if (this.isTrue(this.logOptions?.logToConsole)) {
            this.applyLogLevel(console, console.warn, args);
        }
    }

    public error(...args: any[]) : void {
        if (this.isTrue(this.logOptions?.logToConsole)) {
            this.applyLogLevel(console, console.error, args);
        }
    }

    public debug(...args:any[]): void {
        if (process.env.debug)
        console.warn('START DEBUG OUT::\n ', ...args,'\n::END DEBUG OUT');
    }

    private applyLogLevel(obj: any, lvl:CallbackFunctionVariadic, ...args: any[]) {
        if (!this.isTest) {
        lvl.apply(obj, [...args].map((a) => util.inspect(a, { depth: 6 })))   
        }
    }

}