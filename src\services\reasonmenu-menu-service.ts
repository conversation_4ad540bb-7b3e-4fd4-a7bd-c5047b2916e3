/**
 * serves:
 *  - *resonmenu lambdas
 */

import { ReasonEntry, ReasonFlow } from "@/models/AbsenceModel";
import { AnswerWrapper, MapReasonResponse, ReasonModel, ReasonWrapper } from "@/models/ReasonModel";
import { DynamoClient } from "./mixins";
import { BaseService, IHttpService } from "./service";
import moment from "moment";

export class ReasonMenuService extends DynamoClient(BaseService) {
  AuthenticatedHttpClient?: IHttpService; //TODO

  private ConfigEndpoint(clientCode: string, employeeNumber: string): string {
    
       return ( `${process.env.absenceApiEndpoint}absenceReportingConfig?clientCode=${clientCode}&employeeNumber=${employeeNumber}`);
    
  }

  get TableName(): string {
    return process.env.reasonMenuTableName || 'ablarivr-reasonmenu';
  }
  //TODO need auth & HTTP
  public async getReasonMenuAsync(clientCode: string/*, country: string, province: string*/, employeeNumber: string): Promise<ReasonWrapper> {
    let cmd = {
      TableName: this.TableName,
        // Key: { ClientName: `${clientCode}:/*${country}:${province}*/${employeeNumber}` },
        Key: { ClientName: `${clientCode}:${employeeNumber}` },
    };
    try {
      let reasonModel = await this.get(cmd);
      let key =  `${clientCode}:${employeeNumber}`;
      let ttl = this.getTTL(key);

      if (reasonModel?.Item) {
        if(moment(Date.now()).diff(moment(reasonModel.Item.UpdateTimeStamp), 'minutes') > 1 )
        {
            let reason = await this.fetchReasonMenu(clientCode, employeeNumber);
            await this.updateReasonConfigAsync(reason, employeeNumber, ttl, clientCode);
         return new ReasonWrapper(reason);
        }   
        return new ReasonWrapper(<ReasonModel>reasonModel?.Item); //TODO
      }
      let reason = await this.fetchReasonMenu(clientCode, employeeNumber);

      await this.put({
        TableName: this.TableName,
          Item: { ClientName: `${clientCode}:${employeeNumber}`, ...reason },
          TimeToLive: ttl,
      });
      return new ReasonWrapper(reason);
    } catch (err: any) {
      this.log.error(JSON.stringify(err)); //TODO
      throw err; //TODO
    }
  }

  private async fetchReasonMenu(clientCode: string, employeeNumber: string ): Promise<ReasonModel> {
    let toReturn: ReasonModel;
    this.Logger.log("PERFORMANCE: getreasonmenu BEFORE GET", clientCode);
    let res = await this.AuthenticatedHttpClient?.getAsync(
      `${this.ConfigEndpoint(clientCode, employeeNumber)}`,
    ); 
    this.Logger.log("PERFORMANCE: getreasonmenu AFTER GET", clientCode);
    toReturn = this.MapToReasonModel(res.data/*, country, province*/, employeeNumber);
    return toReturn;
  }

  public async storeReasonMenuAsync(clientCode: string, country:string, province:string, reasons: ReasonModel) {
    try {
      let params = {
        TableName: this.TableName,
          Item: { ClientName: `${clientCode}:${country}:${province}`, ...reasons },
      };
      await this.put(params);
    } catch (err: any) {
      this.log.error(JSON.stringify(err));
    }
  }
    public async updateReasonConfigAsync(model: ReasonModel, employeeNumber: string, ttl: number, clientCode?: string) {

    try {
      let updateParams = {
        
          //Item: { ClientName: `${clientCode}:${country}:${province}`, ...model },
          Item: { ClientName: `${clientCode}:${employeeNumber}`, ...model },
        TableName: this.TableName,
                //TODO
                Key:{
                    // ClientName: `${clientCode}:${country}:${province}`
                    ClientName: `${clientCode}:${employeeNumber}`
                },
          UpdateExpression: 'set RepeatDigit = :RepeatDigit, QuestionId = :QuestionId, QuestionName = :QuestionName, \
          EmployeeNumber = :EmployeeNumber,  QuestionDescription = :QuestionDescription, Manager = :Manager, Employee = :Employee, \
          FamilyMember = :FamilyMember, Answers = :Answers, UpdateTimeStamp = :UpdateTime, TimeToLive = :ttl',
                ExpressionAttributeValues: {
                    ':RepeatDigit' : model.RepeatDigit,
                    ':QuestionId': model.QuestionId,
                    ':QuestionName': model.QuestionName,
                    ':QuestionDescription': model.QuestionDescription,
                    ':Manager': model.Manager,
                    ':Employee': model.Employee,
                    ':FamilyMember': model.FamilyMember,
                    ':Answers': model.Answers,
                    ':UpdateTime': moment(Date.now()).toString(),

                    ':EmployeeNumber' : model.EmployeeNumber.toString(),
                    ':ttl' : ttl
                }
      };
      await this.update(updateParams).then( resolved => { return resolved?.$metadata.httpStatusCode }, rejected => {throw `Error updating ${clientCode}, ${rejected}`}); 
      
    } catch (err: any) {
      this.log.error(JSON.stringify(err));
    }
  } 

  public static advanceFlow(
    reasonFlow: ReasonFlow,
    selectedItem: AnswerWrapper,
    selectedOption: number,
    locale: string
  ): ReasonFlow {
    // Fetch the last primary reason entered, if it exists
    let currentReason: ReasonEntry =
      reasonFlow?.ReasonEntries[reasonFlow.Count - 1];

    // Traverse saved subanswers, if they exist, to get
    // the secondary or Nth level of the current answer flow
    while (currentReason && currentReason.Child) {
      currentReason = currentReason.Child;
    }

    // Construct the new reason entry
    let newReason: ReasonEntry = {
      Id: selectedItem.answerModel.AnswerId,
      Description: `${selectedItem?.ResponseText(locale)}`,
      IVROptionNumber: selectedOption,
      IsLeaf:
        reasonFlow.Count > 0 &&
        !currentReason.IsLeaf &&
        selectedItem.wrappedSubAnswers.length === 0,
      Child: null,
    };

    // Determine if the new entry is a primary reason by
    // checking if the answer flow is empty, or if the previous
    // flow has been completed
    if (reasonFlow.Count === 0 || currentReason.IsLeaf) {
      reasonFlow.ReasonEntries.push(newReason);
      reasonFlow.Count++;
      return reasonFlow;
    }

    // Ensure the new sub answer is not the same as its parent
    if (currentReason.Id === newReason.Id) {
      throw 'Cannot add response to Question with same Id';
    }

    // Append the subanswer and return the updated answer flow
    currentReason.Child = newReason;
    return reasonFlow;
  }

  private MapToReasonModel(reasonItem: any/*, country: string, province: string*/, employeeNumber: string): ReasonModel {
    let toreturn = MapReasonResponse(reasonItem/*, country, province*/, employeeNumber);
    return toreturn;
  }
}