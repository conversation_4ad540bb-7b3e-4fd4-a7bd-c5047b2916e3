'use strict';
import { CallLog, LocalConfiguration, ScopedHttpService} from '@/services'
import {LogService} from '@/util'
import { ConnectContactFlowEvent } from 'aws-lambda'

const log = new LogService({ logToConsole: process.env.Debug });
const callService = new CallLog();
const localConfig = new LocalConfiguration();


export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'saveTotalAbsenceDays';
    callService.Logger.log('Input data', event);
    let contactId;
    const result = {
        IsSaveSuccess: false,
        IsTotalAbsenceDaysValid: false
    };
    try {
       
        contactId = event.Details.ContactData.ContactId;
        const totalAbsenceDays = parseInt(event.Details.Parameters.TotalAbsenceDays, 10);
        const callData = await callService.getCallDataAsync(contactId);
        localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
        const config = await localConfig.getClientConfigAsync(callData.ClientCode);
        const maxAbsenceDays = +(config.clientConfig.MaxTotalMissedAbsenceDays || 0);
        if (Number.isNaN(totalAbsenceDays) || totalAbsenceDays > maxAbsenceDays || totalAbsenceDays <= 0)
        {
            return result;
        }
        result.IsTotalAbsenceDaysValid = true;
        await callService.setCallDataAsync(contactId, {
            IsSaved: "true",
            TotalAbsenceDays: totalAbsenceDays
        });
        result.IsSaveSuccess = true;
        
        return result;
    } catch (error: any) {
        callService.Logger.error('Error in SaveTotalAbsenceDays', JSON.stringify(error));        
        return result;
    }
};
