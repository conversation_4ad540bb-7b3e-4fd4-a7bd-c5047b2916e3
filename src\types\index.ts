import { ReasonWrapper } from '@/models/ReasonModel';
import {ReasonMenuService} from '@/services'

export type CallbackFunctionVariadic = (...args: any[]) => any;
export type CallbackFunction<T> = (...args: any[]) => T;
export type AsyncCallbackFunctionVariadic = (...args: any[]) => Promise<any>;
export type SimpleObject = {[item: string]: string};
export type SimpleMapFunc<T> = (args: ReasonWrapper) => T ;

export interface IPrototype { prototype: any; }