import { startDb, stopDb, createTables, deleteTables } from "jest-dynalite";
import { resolveConflictHandler } from '../../../src/functions'
import {DynamoDBClient, ListTablesCommand } from '@aws-sdk/client-dynamodb'
import {MockAll, TearDown, flowEvent} from '../mocks/mock-all'



 jest.setTimeout(50000)
describe('Unit test for app handler',  () => {

    beforeAll(async () => {});
    beforeEach( async() => { 
        MockAll();  
        ////&console.log("In create");
    })

    afterEach ( async () => {
        ////&console.log("In delete");
        //jest.useRealTimers();
        TearDown();
        await deleteTables();
        await createTables();
    })
    afterAll(async () => {
        ////&console.log("In stopdb");
        await stopDb();
    })


    it('verifies successful response', async () => { 
        let flowObject: any =  {
            Details : {
             ContactData: {
                Attributes: {},
                ContactId: '45a9d991-cbed-4b83-bab5-35d38cbf5156',
              },
              Parameters : {
                ResolveAction:''
              }
          }
            };     
           // jest.useFakeTimers()  ;
        const result = await resolveConflictHandler(flowObject);
        console.log(result);
        //expect(result).toBeDefined();
    });
})