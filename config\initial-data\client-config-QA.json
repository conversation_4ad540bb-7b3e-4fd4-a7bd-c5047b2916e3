{"ClientSysId": "1", "ClientCode": "GOODYEAR", "ClientName": "GOODYEAR", "CallFlowEnterNextAbsenceDateARN": "b170eaa3-e9bf-4381-bb9f-5ecc966a5c39", "CallFlowEnterReasonARN": "efc54cc2-50e0-466d-a5fe-07025c2f12ef", "CallFlowSubmitAbsenceIfErrorARN": "", "CallFlowClosingARN": "", "CallFlowGetOpeningMsgV1ARN": "ec9c237c-1a16-4eed-a729-66061cdf0e53", "CallFlowEnterEmployeePhoneNumberARN": "", "CallFlowTransferToCSRARN": "", "CallFlowEnterAbsenceDateARN": "9d449bf4-7ec8-4bca-bd85-6dcaa03ba4db", "CallFlowValidateEmployeeARN": "f5467b78-f173-46fc-83b0-06391bdfc027", "CallFlowDynamicFlowV1ARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/contact-flow/78498ff6-24e1-433c-a281-0aed5c8cc131", "CallFlowEnterTotalAbsenceDaysARN": "548881cb-cde8-4fee-a655-2d5db7a3df5f", "CallFlowSubmitAbsenceARN": "32d32b28-db2c-43e9-9305-61da411b22cb", "CallFlowEnterRTWARN": "fe309e1e-ec1f-4b1a-ace3-d9d8c980d6c5", "CallFlowTransferToExternalARN": "93ee0e01-7f59-4d2b-852c-53e5de28f589", "LambdaValidateEmployeeIdARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateEmployeeId", "LambdaValidateEmployeePhoneNumberARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateEmployeePhoneNumber", "LambdaSubmitAbsenceARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SubmitAbsence", "LambdaGetPrimaryReasonMenuARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetPrimaryReasonMenu", "LambdaValidateAbsenceDateARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateAbsenceDate", "LambdaValidateShiftStartTimeARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateShiftStartTime", "LambdaValidateShiftEndTimeARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateShiftEndTime", "LambdaResolveConflictAbsenceARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ResolveConflictAbsence", "LambdaSaveSecondaryReasonARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveSecondaryReason", "LambdaAuthenticateEmployeeARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-AuthenticateEmployee", "LambdaSetReturnToWorkARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SetReturnToWork", "LambdaValidateReturnToWorkDateARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateReturnToWorkDate", "LambdaSaveReturnToWorkDateARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveReturnToWorkDate", "LambdaGetClientConfigARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetClientConfig", "LambdaValidateDoBARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateDoB", "LambdaGetSecondaryReasonMenuARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetSecondaryReasonMenu", "LambdaCheckAllAbsReportedARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-CheckAllAbsReported", "LambdaValidateRTWShiftStartTimeARN": "", "LambdaGetOpeningMsgV1ARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetOpeningMsgV1", "LambdaSaveTotalAbsenceDaysARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveTotalAbsenceDays", "LambdaGetCarrierTransferNumberARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetCarrierTransferNumber", "LambdaSaveAbsenceDateARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-SaveAbsenceDate", "LambdaClearLastAddedAbsenceDateARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ClearLastAddedAbsenceDate", "LambdaDroppedSessionMonitorARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-DroppedSessionMonitor", "DefaultDateFormatType": "1", "MaxTotalMissedAbsenceDays": "5", "IsEnterEmployeePhoneNumberRequired": "true", "MaxRetryMenu1": "3", "MaxRetryMenu2": "", "Marker1": "test", "Marker": "Add Value here", "MaxRetryLambda2": "3", "MaxRetryLambda1": "3", "Environment": "LAB", "MaxAllowAbsenceDaysPast": "14", "MaxValidAbsenceDays": "60", "DefaultTimeFormatType": "1", "PreviousAbsenceWithinAA": "2", "MaxAllowAbsenceDaysFuture": "60", "IsLambdaMaxErrorRetry": "false", "IsRTWTimeRequiredForIVR": "false", "DefaultScheduledStartTime": "0900", "DefaultScheduledEndTime": "1700", "IsAllowInteruppted": "false", "MutliLanguageId": "0", "DefaultLanguage": "EN", "PromptUnexpectedErrorSSML": "<speak><amazon:domain name='conversational'>I am sorry, due to an unexpected error I cannot assist you right now. You may try again later or Please contact your supervisor or HR team for further assistance. Goodbye.</amazon:domain></speak>", "PromptLanugageSelectionS3URI": "", "PromptSpecialOpeningMsgSSML": "", "PromptEnterTotalAbsenceDaysSSML1": "<speak><amazon:domain name='conversational'> Please remember that all absences must be reported. Enter the total number of missed absences you're reporting absent on this call. Kindly note that you can report a maximum of 60 absences per call. To repeat, press the * key.</amazon:domain></speak>", "PromptInvalidAbsenceDateSSML": "<speak><amazon:domain name='conversational'>Sorry, this is an invalid entry. Please try again.</amazon:domain></speak>", "PromptIsAMorPMSSML": "<speak><amazon:domain name='conversational'>For AM, press 1, For PM, press 2</amazon:domain></speak>", "PromptMaxRetrySSML": "<speak><amazon:domain name='conversational'>I am sorry, you have reached maximum number of retries.</amazon:domain></speak>", "PromptMaxInvalidEmployeeIdSSML": "<speak><amazon:domain name='conversational'>Sorry, your absence cannot be recorded without your correct Employee number Please call back later Goodbye.</amazon:domain></speak>", "PromptInvalidEmployeeIdSSML1": "<speak><amazon:domain name='conversational'>The Employee ID you entered is</amazon:domain></speak>", "PromptSubOpeningSSML1": "<speak><amazon:domain name='conversational'>Kindly note you can record your absences for 60 days in future OR within previous 2 days. Please note that you can report a maximum of 60 absences per call. You can also report your absences online via Abiliti Absence at goodyearabsence.abilitiabsenceus.com. A confirmation number will be provided to you at the end of the call.</amazon:domain></speak>", "PromptEnterEmployeeIdSSML": "<speak><amazon:domain name='conversational'> Please enter your employee I D using your keypad followed by pound key. If you do not know your employee I D, please disconnect and contact your employer to report your absence.</amazon:domain></speak>", "PromptOpeningSSML": "<speak><amazon:domain name='conversational'>Thank you for calling the Goodyear Absence Reporting System. Please note that effective 1/1/2023, UNUM administers leave under the Family Medical Leave Act (FMLA) and time away from work due to Short Term Disability (STD) and Accident and Sickness. Please listen carefully as the options might have changed.</amazon:domain></speak>", "PromptTransferNumberGeneralErrorSSML": "", "PromptInvalidEntryAndRetrySSML": "<speak><amazon:domain name='conversational'>Sorry, this is an invalid entry. Please try again.</amazon:domain></speak>", "PromptEnterDobSSML": "<speak><amazon:domain name='conversational'>To confirm your identification, please enter your Date of Birth using 2 digits for the Month, 2 digits for the Day and 4 digits for the Year. For example, February 3rd 1980 would be <say-as interpret-as='digit'>02031980</say-as></amazon:domain></speak>", "PromptIntroPrimaryReasonMenuSSML": "<speak><amazon:domain name='conversational'>Please select the reason for your absence from one of the following options. If you or your family member is hospitalized or you expect your absence to be greater than 3 days due to an illness or injury,  Please select option number 2.</amazon:domain></speak>", "PromptIntroSecondaryReasonMenuSSML": "<speak><amazon:domain name='conversational'>Please select from one of the following options.</amazon:domain></speak>", "PromptCarrierTransferMsgSSML": "<speak><amazon:domain name='conversational'></amazon:domain>Thank you for reporting your absence. You will now be transferred to UNUM. Your absence will be reported to your manager and will be reviewed for compliance with local policies and procedures. Please remember that if you have any questions about applying for leave or about an open claim, please contact your manager or HR representative</speak>", "PromptCompletedAbsenceSSML": "<speak><amazon:domain name='conversational'></amazon:domain>Thank you for reporting your absence. Your absence will be reported to your manager and will be reviewed for compliance with local policies and procedures. If you have any questions, please contact your manager or HR representative</speak>", "PromptInvalidEmployeeIdSSML2": "<speak><amazon:domain name='conversational'>This does not match with our records. Please try again</amazon:domain></speak>", "PromptEnterAbsenceDateSSML": "<speak><amazon:domain name='conversational'> Please enter your absence date. Use two digits for the Month, two digits for the Day and 4 digits for the Year. For example, February 3rd would be 0 2 0 3 2 0 2 2. To repeat, press the star key.</amazon:domain></speak>", "PromptEnterStartShiftTimeSSML": "<speak><amazon:domain name='conversational'> Please enter the start time of your absence. Enter the hour and then the minutes. For example, to enter 7 30 AM, please enter 0 7 3 0. To enter 10 PM, please enter 10 00. To repeat, press the star key. </amazon:domain></speak>", "PromptEnterEndShiftTimeSSML": "<speak><amazon:domain name='conversational'>Please enter the end time of your absence. Enter the hour and then the minutes. For example, to enter five PM, please enter 0 5 0 0. To enter 10 PM, please enter 10 00. To repeat, press the star key. </amazon:domain></speak>", "PromptIsRTWKnownSSML": "<speak><amazon:domain name='conversational'>Do you know when will you return to work? if Yes, press 1, if No, press 2. </amazon:domain></speak>", "PromptEnterReturnToWorkSSML": "<speak><amazon:domain name='conversational'>Please use two digits for the Month, two digits for the Day and 4 digits for the Year. For example, August 2nd would be 0 8 0 2 2 0 2 2. To repeat, press the * key.</amazon:domain></speak>", "PromptProvideConfirmNumberSSML1": "<speak><amazon:domain name='conversational'>The confirmation number for your absence is</amazon:domain></speak>", "PromptTransferToAgentSSML": "<speak><amazon:domain name='conversational'>You are being transferred to a live agent to better assist you, please do not hang up-an agent will be on the line shortly. </amazon:domain></speak>", "PromptTransferToCarrierSSML": "<speak><amazon:domain name='conversational'>Please stay on the line as we will now be transferring you to your carrier. </amazon:domain></speak>", "PromptProvideConfirmNumberSSML2": "<speak><amazon:domain name='conversational'>To repeat your confirmation number, press 1.</amazon:domain></speak>", "PromptEnterEmployeePhoneNumberSSML": "<speak><amazon:domain name='conversational'>Enter Phone number where you can be reached. </amazon:domain></speak>", "CarrierTransferNumber": "12345678", "TransferNumberGeneralError": "+15012355529", "sessionMonitorCallInterval": "30"}