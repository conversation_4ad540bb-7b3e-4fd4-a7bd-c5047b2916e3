import { CallLog, LocalConfiguration, ManageApiService, ScopedHttpService } from "@/services";
import { ConnectContactFlowEvent } from "aws-lambda";

const manageService = new ManageApiService();
const localConfig = new LocalConfiguration();
const session = new CallLog();
export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'getPolicyBalance';
    localConfig.Logger.debug('Input data', event);
    
    let clientCode = event.Details.Parameters.ClientCode;
    let contactId = event.Details.ContactData.ContactId;
    
    manageService.AuthenticatedHttpClient = new ScopedHttpService(contactId);
    const sessionData = await session.getCallDataAsync(contactId);
    let empNumber: string = '';
    if(sessionData && sessionData.EmployeeInfo)
        empNumber = sessionData.EmployeeInfo.employeeNumber;
    let response = await manageService.getPolicyBalance(contactId, clientCode, empNumber);
    
}