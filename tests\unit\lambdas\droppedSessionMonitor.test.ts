import { flowEvent } from "../mocks/mock-all";
import { droppedSessionHandler } from '../../../src/functions'
// import { droppedSessionHandler } from '@/functions/';
import { startDb, stopDb, createTables, deleteTables } from "jest-dynalite";
import {DynamoDBClient, ListTablesCommand } from '@aws-sdk/client-dynamodb'
import {MockAll, TearDown} from '../mocks/mock-all'
import { DroppedSessionModel } from "../../../src/models";

jest.setTimeout(60000)
describe('Unit test for app handler',  () => {

    beforeAll(async () => {});
    beforeEach( async() => { 
        MockAll();  
        ////&console.log("In create");
    })

    afterEach ( async () => {
        ////&console.log("In delete");
        TearDown();
        await deleteTables();
        await createTables();
    })
    afterAll(async () => {
        ////&console.log("In stopdb");
        await stopDb();
    })

    it('verifies successful response', async () => {
        let event: any = {};
        const result = await droppedSessionHandler(event);
        console.log(result);
        expect(result).toBeDefined();
    })
})