import { BaseService } from '@/services'
import { DynamoClient } from 'Mixins'
import {AsyncCallbackFunctionVariadic, CallbackFunction} from '@/types'

export default class CacheClient extends DynamoClient(BaseService) {
    
    get TableName() {
        return  process.env.cacheTableName || "ablarivr-data-cache"
    }

    

    public async getCachedAsync (key: string, dataGeneratorAsync: AsyncCallbackFunctionVariadic, invalidate?: CallbackFunction<boolean>): Promise<any> {
        //&console.log(`Call to getcache from ${typeof(this)} with key ${key} on table ${process.env.cacheTableName || "ablarivr-data-cache"}`);
        const params = {
            TableName: this.TableName,
            Key: { CacheKey: key },
        };

         try { 
           // console.log('inside getCachedAsync');
            let ttl = this.getTTL(key);
            this.log.log('Retrieving data from cache...', params);
            let cachedData = await this.get(params);
            this.log.log(cachedData);
            
            if (cachedData?.Item?.data)
            {        
                if( invalidate && invalidate(cachedData.Item.data) ){
                    console.log('Cached data is expired, updating');
                    await this.updateCachedItem(key, ttl, dataGeneratorAsync);
                    return (await this.get(params))?.Item?.data;
             }                
            this.log.log(`Loaded from cache for key ${key}`);
            return cachedData.Item.data;
            }
            this.log.log(`Cache record for key ${key} not found. Generating...`);        
            
            await dataGeneratorAsync().then( async val => {
                const insertParams = {
                    TableName: process.env.cacheTableName || "ablarivr-data-cache",//this.TableName,//this.tableName,
                    //TODO
                    Item: {
                        CacheKey: key,
                        data:  val,
                        TimeToLive: ttl
                    },
                };
                await this.put(insertParams).then(async resolved => {cachedData = await this.get(params); }); //&console.log(`Great success, data is ${JSON.stringify(val)}`)}, rejected => this.Logger.error('Failed to insert into cache'));
                //this.log.log('Inserted generated object to cache', insertParams);
            });
            return cachedData?.Item?.data;
        } catch (err) {
            console.log(err);
            this.log.error(err);
            throw err;
        }
    }

    public async updateCachedItem(key: string, ttl: number, dataGeneratorAsync: AsyncCallbackFunctionVariadic): Promise<any> {
        await dataGeneratorAsync().then( async val => {
            const updateParams = {
                TableName: process.env.cacheTableName || "ablarivr-data-cache",//this.TableName,//this.tableName,
                //TODO
                Key:{
                    CacheKey: key
                },
                UpdateExpression: 'set #d = :data, TimeToLive = :ttl',
                ExpressionAttributeNames: {
                    '#d': 'data'
                },
                ExpressionAttributeValues: {
                    ':data' : val,
                    ':ttl' : ttl
                }
            };
        await this.update(updateParams).then( resolved => { return resolved?.$metadata.httpStatusCode }, rejected => {throw `Error updating ${key}, ${rejected}`}); 
    });
    }

    public async putCachedAsync (key: string, dataToCache: any)  {
        try {
            let ttl = this.getTTLDate();
            let params = {
                TableName: process.env.cacheTableName || "ablarivr-data-cache", //this.TableName,//this.tableName, //TODO!
                Item: {
                    CacheKey: key,
                    data: dataToCache,
                    TimeToLive: ttl,
                },
            };
            this.log.log('Inserting object to cache', params);
            return await this.put(params);
        } catch (err) {
            this.log.log(err);
            throw err;
        }
    }
    
}