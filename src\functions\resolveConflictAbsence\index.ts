import {AbsenceReportingService, CallLog, AbsenceApiService} from '@/services'
import {LogService, DateHelper} from '@/util'
import {AbsenceDates, AbsenceIncident} from '@/models'
import { ConnectContactFlowEvent } from 'aws-lambda'
import moment from 'moment';

const callService = new CallLog();
const reportingService = new AbsenceReportingService();
reportingService.CallLog = callService;

export const handler = async(event: ConnectContactFlowEvent) => {
    const contactId = event.Details.ContactData.ContactId;
    const action = event.Details.Parameters.ResolveAction;
    callService.Logger.log(`Input data `, event);
    const sessionData = await callService.getCallDataAsync(contactId);
    let  isSaveSuccess: boolean = true;
    //if(action === '1')
    // {
    //     let currentAbsence = (sessionData.Absences)?.pop();
    //     //The latest absence date reported in current session:
    //     let lastReport = currentAbsence.AbsenceIncident.AbsenceDates.pop(); 
    //     //absence date mareked as overlap
    //     let overLap =  currentAbsence.AbsenceIncident.AbsenceDates.find((a: any)=> a.IsOverlap);
    //     lastReport = {...lastReport,...overLap};
    //     //index of the remaining same date absence date
    //     let dupIdx : number = currentAbsence.AbsenceIncident.AbsenceDates.findIndex((dt: AbsenceDates) => 
    //             moment(dt.StartDate).startOf('day').isSame(moment(lastReport.StartDate).startOf('day')));
        
    //     let mergedAbsenceDate: AbsenceDates = {
    //         StartDate: lastReport.StartDate<currentAbsence.AbsenceIncident.AbsenceDates[dupIdx].StartDate? lastReport.StartDate : currentAbsence.AbsenceIncident.AbsenceDates[dupIdx].StartDate,
    //         EndDate: lastReport.EndDate>currentAbsence.AbsenceIncident.AbsenceDates[dupIdx].EndDate? lastReport.EndDate : currentAbsence.AbsenceIncident.AbsenceDates[dupIdx].EndDate,
    //         ShiftStartTime: lastReport.StartDate<currentAbsence.AbsenceIncident.AbsenceDates[dupIdx].StartDate? lastReport.StartDate : currentAbsence.AbsenceIncident.AbsenceDates[dupIdx].StartDate,
    //         ShiftEndTime: lastReport.EndDate>currentAbsence.AbsenceIncident.AbsenceDates[dupIdx].EndDate? lastReport.EndDate : currentAbsence.AbsenceIncident.AbsenceDates[dupIdx].EndDate,
    //         ShiftDuration: null,
    //         IsUpdated: true,
    //         ScheduledShiftEndTime: null,
    //         ScheduledShiftStartTime: null,
    //         IncidentId: lastReport.IncidentId,
    //         IsOverLap: false
    //     }
    //     if(dupIdx >= 0)
    //         currentAbsence.AbsenceIncident.AbsenceDates.splice(dupIdx, 1, mergedAbsenceDate);
    //     callService.Logger.log(`Absence Dates after merging ${JSON.stringify(currentAbsence.AbsenceIncident.AbsenceDates)}`);
    //     sessionData.TotalAbsenceDays =-1;
        
    //     sessionData.Absences.push(currentAbsence);
    //     if(await callService.setCallDataAsync(contactId, sessionData))
    //         isSaveSuccess = true;
    //     }
    
    
    //else if (action === '2')
    //{
        //reenter
        // const lastAbsence = sessionData.AbsenceDates.pop();
        // reportingService.Logger.log(`Absence Date: ${lastAbsence} removed to be reentered`); 
        // await callService.setCallDataAsync(contactId, sessionData);
    //}
   // if(action === '3')
    //{
        //do nothing for now
    //}
    return  {IsSaveSuccess: isSaveSuccess}
}
interface IMergeResult {
    IsMerged: boolean,
    MergedAbsenceDate: AbsenceDates 
}
// async function MergeAbsenceShift(overlapAbsences: AbsenceDates, reportedDates: AbsenceDates[]): Promise<IMergeResult> {
// let mergedAbsenceDate: AbsenceDates = {
//     ShiftStartTime:"2023-05-08 09:30:00",
//     StartDate:"2023-05-08 09:30:00",
//     ShiftDuration:null,
//     ScheduledShiftEndTime:null,
//     ScheduledShiftStartTime:null,
//     IsUpdated:false,
//     EndDate:"2023-05-08 12:30:00",
//     IncidentId:overlapAbsences.IncidentId,
//     ShiftEndTime:"2023-05-08 12:30:00"};
// let isMerged= false;
// const idx = reportedDates.findIndex(dt => moment(dt.StartDate).startOf('day').isSame(moment(overlapAbsences.StartDate).startOf('day')) );    
// const dupAbse = reportedDates[idx]   ;
// callService.Logger.log(`Merging overlap date with latest date`);
// //add shtftStartdate and shiftEnddate
// if(overlapAbsences.StartDate && overlapAbsences.EndDate && dupAbse && dupAbse.StartDate && dupAbse.EndDate){
//  if(overlapAbsences.StartDate <dupAbse.StartDate)
//     mergedAbsenceDate.StartDate = overlapAbsences.StartDate
// else mergedAbsenceDate.StartDate = dupAbse.StartDate;;
// if(overlapAbsences.EndDate < dupAbse.EndDate)
//     mergedAbsenceDate.EndDate = dupAbse.EndDate;
// else mergedAbsenceDate.EndDate = overlapAbsences.EndDate;
// isMerged = true;

// }
// callService.Logger.log(`Merged absence date is now ${mergedAbsenceDate.StartDate}, ${mergedAbsenceDate.EndDate}`);

// return{IsMerged:isMerged, MergedAbsenceDate: mergedAbsenceDate};
// }

