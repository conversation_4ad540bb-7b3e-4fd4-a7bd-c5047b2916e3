{"CallFlowDynamicFlowV1ARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/contact-flow/3f3297a4-8133-45a6-809f-a7fc6356f008", "CallFlowEnterNextAbsenceDateARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/c0d165b4-b156-4e36-addd-e56a54ad2d2c", "CallFlowEnterReasonARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/e5685427-933d-478b-952e-51082831e4c0", "CallFlowSubmitAbsenceIfErrorARN": "", "CallFlowClosingARN": "", "CallFlowGetOpeningMsgV1ARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/5b988e32-ab02-40be-80ca-c5bc5e84334f", "CallFlowEnterEmployeePhoneNumberARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/d945f173-0cb8-498f-9b0a-11f6e8607b5a", "CallFlowTransferToCSRARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/750fdd36-ef60-4494-be30-a18b30d063e1", "CallFlowEnterAbsenceDateARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/74d2195e-248f-4387-bd92-2461aaa9b6c1", "CallFlowValidateEmployeeARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/354d14c2-dfb1-426a-adc8-adc83c538e67", "CallFlowEnterTotalAbsenceDaysARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/11aa2b74-523a-475d-a271-d5e725cdae27", "CallFlowSubmitAbsenceARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/11bc8a51-4fd1-451a-a719-2639a39ac8d4", "CallFlowEnterRTWARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/23b75f81-6d68-47a2-bcbc-69d9bd2d754d", "CallFlowTransferToExternalARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/25dd243a-c876-4c80-b099-69a9942b0ed4", "CallFlowGetOpenLeaveCaseARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/8ac4cd25-cec2-4fb2-aa8c-d8c08f2f29a4", "CallFlowSTDLeaveARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/17f6cfc7-af86-44c2-9df4-80e596e38ca6", "CallFlowLinkAbsenceInAbilitiARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/b49a55ca-5eb8-41e1-9283-516a6b29f31f", "CallFlowDynamicFlowV2ARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/contact-flow/90fad6ab-9df4-445d-bced-43fc08aab805", "CallFlowEnterAbsenceDateV2ARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/b10af7af-db3e-42b3-a7fe-4528ea3e0148", "CallFlowEnterNextAbsenceDateV2ARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/ebcc29ea-9ea1-411d-9d7f-8eae727d54df", "CallFlowEnterDateRange1ARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/04fd8a69-9d6a-4c04-8b81-d625403e164f", "CallFlowEnterDateRange2ARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/flow-module/6391134b-5453-4a33-a0de-0b858326acb5", "CallFlowVoicemailMessageARN": "arn:aws:connect:us-east-1:740485909349:instance/fb164942-8e17-4c37-9089-b35b4207fdf3/contact-flow/8c22111f-6789-42ac-89a5-5108fc591bf7", "LambdaValidateEmployeeIdARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-ValidateEmployeeId", "LambdaValidateEmployeePhoneNumberARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-ValidateEmployeePhoneNumber", "LambdaSubmitAbsenceARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-SubmitAbsence", "LambdaGetPrimaryReasonMenuARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-GetPrimaryReasonMenu", "LambdaValidateAbsenceDateARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-ValidateAbsenceDate", "LambdaValidateShiftStartTimeARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-ValidateShiftStartTime", "LambdaValidateShiftEndTimeARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-ValidateShiftEndTime", "LambdaResolveConflictAbsenceARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-ResolveConflictAbsence", "LambdaSaveSecondaryReasonARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-SaveSecondaryReason", "LambdaAuthenticateEmployeeARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-AuthenticateEmployee", "LambdaSetReturnToWorkARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-SetReturnToWork", "LambdaValidateReturnToWorkDateARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-ValidateReturnToWorkDate", "LambdaSaveReturnToWorkDateARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-SaveReturnToWorkDate", "LambdaGetClientConfigARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-GetClientConfig", "LambdaValidateDoBARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-ValidateDoB", "LambdaGetSecondaryReasonMenuARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-GetSecondaryReasonMenu", "LambdaCheckAllAbsReportedARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-CheckAllAbsReported", "LambdaValidateRTWShiftStartTimeARN": "", "LambdaGetOpeningMsgV1ARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-GetOpeningMsgV1", "LambdaSaveTotalAbsenceDaysARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-SaveTotalAbsenceDays", "LambdaGetCarrierTransferNumberARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-GetCarrierTransferNumber", "LambdaSaveAbsenceDateARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-SaveAbsenceDate", "LambdaClearLastAddedAbsenceDateARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-ClearLastAddedAbsenceDate", "LambdaDroppedSessionMonitorARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-DroppedSessionMonitor", "LambdaCheckOpenCasesARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-CheckOpenCases", "LambdaCheckEESTDARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-CheckEESTD", "LambdaSaveSTDResponseARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-SaveSTDResponse", "LambdaSaveThirdLevelResponseARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-SaveThirdLevelResponse", "LambdaLinkAbsenceInAbilitiARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-CheckCanBeLinked", "LambdaSaveLeaveResponseARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-SaveLeaveResponse", "LambdaVMCallbackARN": "arn:aws:lambda:us-east-1:740485909349:function:ABLARIVR-VmCallback"}