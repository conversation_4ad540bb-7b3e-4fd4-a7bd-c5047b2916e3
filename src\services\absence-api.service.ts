import { AuthClient } from 'Mixins'
import { CacheClient } from '@/services'
import { AbsenceIncident, AbsenceDates, MapReasonText, ReasonText } from '@/models'
import { CancelAbsenceModel, PageModel, SaveAbsenceIncidentReponse, SubmitAbsenceResponse, CanBeALinkedIncident, ValidateAbsenceShiftResponse } from '@/models';
import {v4 as uuidv4} from 'uuid'


export default class AbsenceApiService extends AuthClient(CacheClient){
    public async createIncidentAsync(clientCode: string, contactId: string,  incident: AbsenceIncident): Promise<SaveAbsenceIncidentReponse> {
        try{
            const absApiEndpointBase = process.env.absenceApiEndpoint;
    
            const params = await this.prepareRequest(contactId, incident);
            const apiEndpoint = absApiEndpointBase + `?clientCode=${clientCode}&fetchLeaveQuestions=false`;
            this.Logger.log("PERFORMANCE: createIncidentAsync BEFORE post");
            const response = await this.AxiosClient?.post(apiEndpoint, params.payload, params.config);
            this.Logger.log("PERFORMANCE: createIncidentAsync After post");
            this.Logger.log(
                `API Request: ${apiEndpoint}  parameters:`,
                clientCode,
                response?.data,
              );
            
            return  this.MapResponseToSaveAbsenceIncident(response?.data);//this data should have incidentid or validation errors
            }
            catch (error: any) {
                this.Logger.log(error.message);
                if (error.response) {
                    // If server responded with a status code for a request
                    this.Logger.log(`Error in CreateIncident API call with status ${error.response.status}`);
                    if(!error.response.data?.success){
                        let validationErrors: any[] = error.response.data.validationErrors;
                        this.Logger.log(`Validation Errors returned in CreateIncident API call:  ${JSON.stringify(validationErrors)}`);
                        return this.MapResponseToSaveAbsenceIncident(error.response.data);
                    }
                    
                } else if (error.request) {
                    // Client made a request but response is not received
                    this.Logger.log('called', error.request);
                } else {
                    // Other case
                    this.Logger.log('Error', error.message);
                }
                throw Error(error);
            }
    };
    public async updateIncidentAsync(clientCode: string, contactId: string,  incident: AbsenceIncident): Promise<SaveAbsenceIncidentReponse> {
        
        try{
            const absApiEndpointBase = process.env.absenceApiEndpoint;
            const params = await this.prepareRequest(contactId, incident);
            this.Logger.log("DEBUG LOG: Absence incident BEFORE update", JSON.stringify(incident));
            const apiEndpoint = absApiEndpointBase + `${incident.AbsenceIncidentId}?fetchLeaveQuestions=false`;
            this.Logger.log("PERFORMANCE: updateIncidentAsync BEFORE post", incident.AbsenceIncidentId);
           const response = await this.AxiosClient?.put(apiEndpoint, params.payload, params.config);
           
            this.Logger.log("PERFORMANCE: updateIncidentAsync AFTER post", incident.AbsenceIncidentId);
            this.Logger.log(
                `API Request: ${apiEndpoint}  parameters:`,
                clientCode,
                response?.data,
              );
              this.Logger.log("PERFORMANCE: Inside Update Absence incident before mapping", );
            return  this.MapResponseToSaveAbsenceIncident(response?.data);//this data should have incidentid or validation errors

            
            }
            catch (error: any) {
                if (error.response) {
                    // If server responded with a status code for a request
                    this.Logger.log(`Error in UpdateIncident API call with status ${error.response.status}`);
                    if(!error.response.data?.success){
                        let validationErrors: any[] = error.response.data.validationErrors;
                        this.Logger.log(`Validation Errors returned in UpdateIncident API call:  ${JSON.stringify(validationErrors)}`);
                        return this.MapResponseToSaveAbsenceIncident(error.response.data);
                    }
                } else if (error.request) {
                    // Client made a request but response is not received
                    this.Logger.log('called', error.request);
                } else {
                    // Other case
                    this.Logger.log('Error', error.message);
                }
                throw Error(error);
            }
    };
    public async submitIncidentAsync(clientCode: string, contactId: string,  incident: AbsenceIncident | null): Promise<SubmitAbsenceResponse> {
        try{
            const absApiEndpointBase = process.env.absenceApiEndpoint;
            const params = await this.prepareRequest(contactId, incident);
                
            const apiEndpoint = absApiEndpointBase + `${incident?.AbsenceIncidentId}/submit`;
            this.Logger.log("PERFORMANCE: submitIncidentAsync BEFORE post", incident?.AbsenceIncidentId);
            const response = await this.AxiosClient?.put(apiEndpoint, params.payload, params.config);
            this.Logger.log("PERFORMANCE: submitIncidentAsync AFTER post", incident?.AbsenceIncidentId);
            this.Logger.log(
                `API Request: ${apiEndpoint}  parameters:`,
                clientCode,
                response?.data,
              );   
                
            return  this.MapResponseToSubmitAbsenceIncident(response?.data);//this data should have incidentid or validation errors
               
            }
            catch (error: any) {
                if (error.response) {
                    // If server responded with a status code for a request
                    this.Logger.log(`Error in SubmitIncident API call with status ${error.response.status}`);
                    if(!error.response.data?.success){
                        let validationErrors: any[] = error.response.data.validationErrors;
                        this.Logger.log(`Error returned in SubmitIncident API call:  ${JSON.stringify(error.response.data)}`);
                        if(validationErrors){
                        this.Logger.log(`Validation Errors returned in SubmitIncident API call:  ${JSON.stringify(validationErrors)}`);
                        return this.MapResponseToSubmitAbsenceIncident(error.response.data);
                        }
                    }
                } else if (error.request) {
                    // Client made a request but response is not received
                    this.Logger.log('called', error.request);
                } else {
                    // Other case
                    this.Logger.log('Error', error.message);
                }
                throw Error(error);
            }
    };
    public async cancelIncidentsAsync(incidentIds: number[], cancelRequest: CancelAbsenceModel): Promise<number[]> {
        try{
            const absApiEndpointBase = process.env.absenceApiEndpoint;
            const cancelAuthId = uuidv4();
            this.Logger.log(`Cancel auth token UUID ${cancelAuthId}`);
            const params = await this.prepareRequest(cancelAuthId, {incidentIds, cancelRequest});
            const apiEndpoint = absApiEndpointBase + `cancelIncidents?`;
            const response = await this.AxiosClient?.put(apiEndpoint, params.payload, params.config);
            this.Logger.log(
                `API Request: ${apiEndpoint}  parameters:`,
                JSON.stringify(incidentIds),
                response?.data,
              );   
            this.Logger.log(`CancelIncident API response:${response?.data} `);
            return  (response?.data.success);
               
            }
            catch (error: any) {
                if (error.response) {
                    // If server responded with a status code for a request
                    this.Logger.log(`Error in CancelIncident API call with status ${error.response.status}`);
                    
                } else if (error.request) {
                    // Client made a request but response is not received
                    this.Logger.log('called', error.request);
                } else {
                    // Other case
                    this.Logger.log('Error', error.message);
                }
                throw Error(error);
            }
    };

    public async canBeALinkedIncident(contactId: string, incident: AbsenceIncident): Promise<CanBeALinkedIncident>{
        try{
            this.Logger.log('inside canbealinkedincident -absenceapiservice');
            const absApiEndpointBase = process.env.absenceApiEndpoint;
            //const params = await this.prepareRequest(contactId);
            let token = await this.getToken(contactId);
            if (token.S) {
              token = token.S;
            }
          
            const config = {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
            const apiEndpoint = absApiEndpointBase + `${incident?.AbsenceIncidentId}/canBeALinkedIncident`;
            const response = await this.AxiosClient?.get(apiEndpoint, config);
            this.Logger.log('inside canbealinkedincident -absenceapiservice response', response);
            this.Logger.log('inside canbealinkedincident -absenceapiservice response', response.data);
            this.Logger.log(
                `API Request: ${apiEndpoint}  parameters:`,
               // incident.AbsenceIncidentId,
                response?.data,
              );   
                
            return  this.MapToCanBeALinkedIncident(response?.data);//this data should have incidentid or validation errors
               
            }
            catch (error: any) {
                if (error.response) {
                    // If server responded with a status code for a request
                    this.Logger.log(`Error in CanBeALinkedIncident API call with status ${error.response.status}`);
                    
                } else if (error.request) {
                    // Client made a request but response is not received
                    this.Logger.log('called', error.request);
                } else {
                    // Other case
                    this.Logger.log('Error', error.message);
                }
                throw Error(error);
            }
    }

    public async validateAbsenceShift(contactId: string,incidentId: string,  absenceDt: AbsenceDates): Promise<ValidateAbsenceShiftResponse>{
       
        try{
            const absApiEndpointBase = process.env.absenceApiEndpoint;
            const params = await this.prepareRequest(contactId, absenceDt);
                
            const apiEndpoint = absApiEndpointBase + `${incidentId}/validateshift`;
            const response = await this.AxiosClient?.put(apiEndpoint, params.payload, params.config);
            
            this.Logger.log(
                `API Request: ${apiEndpoint}  parameters:`,
                incidentId,
                response?.data,
              );   
                
            return  this.MapToAbsenceShiftValidationResult(response?.data);//this data should have incidentid or validation errors
               
            }
            catch (error: any) {
                if (error.response) {
                    // If server responded with a status code for a request
                    if(!error.response.data?.success){
                        let validationErrors: any[] = error.response.data.validationErrors;
                        this.Logger.log(`Error returned in validateAbsenceShift API call:  ${JSON.stringify(error.response.data)}`);
                        if(validationErrors){
                        this.Logger.log(`Validation Errors returned in validateAbsenceShift API call:  ${JSON.stringify(validationErrors)}`);
                        return this.MapToAbsenceShiftValidationResult(error.response.data);
                        }
                    }
                    
                } else if (error.request) {
                    // Client made a request but response is not received
                    this.Logger.log('called', error.request);
                } else {
                    // Other case
                    this.Logger.log('Error', error.message);
                }
                throw Error(error);
            }
    }
    MapToAbsenceShiftValidationResult(data: any) : ValidateAbsenceShiftResponse{
        let toReturn: ValidateAbsenceShiftResponse = {
            AbsenceDate: {}, Success: false, ValidationErrors: []
        }
       
        if(data)
        {
            toReturn.AbsenceDate = data.returnValue;
            toReturn.Success = data.success;
            let err: any[] = data.validationErrors as Array<any>;
            err.forEach((item: any) => {
                toReturn.ValidationErrors.push({
                    ErrorCode: item.errorCode,
                    ErrorDescription: item.errorDescription,
                    ErrorSeverity: item.errorSeverity,
                    ErrorProvider: item.errorProvider,
                    MetaData: item.metaData
                })
            });
        }
        return toReturn;
    }
    MapToCanBeALinkedIncident(data: any): CanBeALinkedIncident  {
        let toReturn: CanBeALinkedIncident = {CanBeLinked: false, IncidentToBeLinked: '', LatestDate: '',TotalNoOfDaysReportedExcludingCurrent: '' };
        if(data)
        {
            toReturn.CanBeLinked = data.canBeLinked;
            toReturn.IncidentToBeLinked = data.incidentToBeLinked;
            toReturn.LatestDate = data.latestDate;
            toReturn.TotalNoOfDaysReportedExcludingCurrent = data.totalNoOfDaysReportedExcludingCurrent;
        }
        return toReturn;
    }
    
    public MapResponseToSaveAbsenceIncident(apiResponse: any): SaveAbsenceIncidentReponse {
        this.Logger.log("PERFORMANCE: MapResponseToSaveAbsenceIncident BEFORE mapping");
        let toreturn: SaveAbsenceIncidentReponse = {AbsenceIncident: {},  ValidationErrors:[], QuestionReponse:[], AlertMessages:[]} 
        let responseIncident: any;
        
        if (apiResponse && (responseIncident = apiResponse.returnValue)) {     

               toreturn.AbsenceIncident = this.MapApiResponseToIncident(responseIncident.absenceIncidentModel);
                toreturn.AbsenceIncident.AbsenceType = responseIncident.absenceIncidentModel.absenceType ;
                toreturn.AbsenceIncident.AbsenceSource = "IVR";
        if(apiResponse.returnValue.absenceQuestionReturnModel){
            
            if (Array.isArray(apiResponse.returnValue.absenceQuestionReturnModel.reasonOfAbsenceAlerts)) {
                
                toreturn.AlertMessages = apiResponse.returnValue.absenceQuestionReturnModel.reasonOfAbsenceAlerts.map( (x:any) => {
                    let locales: ReasonText[] = [];
                    MapReasonText(x.alertText, locales);
                    return {
                        AlertId: x.alertId, 
                        AlertText:locales } 
                });
            }
         
            if(Array.isArray(apiResponse.returnValue.absenceQuestionReturnModel.questions)){                   
                let ques : any[] = apiResponse.returnValue.absenceQuestionReturnModel.questions as Array<any>;
                ques.forEach((item: any) => {                        
                    let pgModel: PageModel = new PageModel();
                    pgModel = this.deepMap(item, pgModel);
                    toreturn.QuestionReponse?.push(pgModel);
                })
        }
    }
        
        if (!apiResponse.success && apiResponse.validationErrors.length >0)
        {
            let err: any[] = apiResponse.validationErrors as Array<any>;
            err.forEach((item: any) => {
                toreturn.ValidationErrors.push({
                    ErrorCode: item.errorCode,
                    ErrorDescription: item.errorDescription,
                    ErrorSeverity: item.errorSeverity,
                    ErrorProvider: item.errorProvider,
                    ErrorParameters: item.errorParameters.map((x: any) =>({
                        PropertyName: x?.propertyName,
                        PropertyValue: x?.propertyValue
                    }))
                })
            });
        }
    }
    this.Logger.log("PERFORMANCE: MapResponseToSaveAbsenceIncident AFTER mapping");
        return toreturn;
      
    }

      private MapResponseToSubmitAbsenceIncident(apiResponse: any): SubmitAbsenceResponse {
        let toReturn : SubmitAbsenceResponse = {AbsenceIncident: {}, AbsenceClosingScriptModel: [], ValidationErrors: []};
        let responseIncident: any;
        if (apiResponse && (responseIncident = apiResponse.returnValue?.absenceIncidentModel)) {     
  
            toReturn.AbsenceIncident =  this.MapApiResponseToIncident(responseIncident);        
            if(apiResponse.returnValue?.closingScripts){ //TODO
                let locale =  [];
                let cs : any[] = apiResponse.returnValue.closingScripts as Array<any>;
                cs.forEach((item: any) => {
                    toReturn.AbsenceClosingScriptModel.push({
                        ClosingScriptId: item.closingScriptId,
                        
                        Text: item.text.map((x: any)=> ({
                            Locale: x?.locale,
                            Description: x?.description,
                            ShortDescription: x?.shortDescription
                        })),
                        RedirectUrl: item.redirectURL,
                        DisplayOptionType: item.displayOptionType
                    })
                });            
            }
        }
        if (!apiResponse.success && apiResponse.validationErrors?.length >0)
        {
            let err: any[] = apiResponse.validationErrors as Array<any>;
            err.forEach((item: any) => {
                toReturn.ValidationErrors.push({
                    ErrorCode: item.errorCode,
                    ErrorDescription: item.errorDescription,
                    ErrorSeverity: item.errorSeverity,
                    ErrorProvider: item.errorProvider,
                     ErrorParameters: item.errorParameters
                })
            });
        }
        return toReturn;
      }

      private MapApiResponseToIncident(responseIncident: any): AbsenceIncident {
        return {
            AbsenceIncidentId: responseIncident.absenceIncidentId,
            EmployeeNumber: responseIncident.employeeNumber ,
            ClientCode : responseIncident.clientCode ,
            FirstName: responseIncident.firstName ,
            LastName: responseIncident.lastName ,
            BestPhoneNumber: responseIncident.bestPhoneNumber ,
            ProvinceCode: responseIncident.provinceCode  ,
            ReturnToWorkDate: responseIncident.returnToWorkDate ,
            NextScheduledShift: responseIncident.nextScheduledShift ,
            RequestDate: responseIncident.requestDate ,
            PrimaryReason: responseIncident.primaryReason ,
            SecondaryReason: responseIncident.secondaryReason ,
            AbsenceType: responseIncident.absenceType ,
            ReportedBy: responseIncident.reportedBy  ,
            ReportingLocale: responseIncident.reportingLocale  ,
            AbsenceDates: responseIncident.absenceDates ? responseIncident.absenceDates.map((x: any) => ({
                ShiftStartTime: x?.shiftStartTime,
                StartDate: x?.shiftStartTime,
                ShiftDuration: x?.shiftDuration,
                ScheduledShiftEndTime: x?.scheduledShiftEndTime,
                ScheduledShiftStartTime: x?.scheduledShiftStartTime,
                IsOverLap: false,
                IsUpdated: false,
                EndDate: x?.shiftEndTime,
                IncidentId: responseIncident.absenceIncidentId,
                ShiftEndTime: x?.shiftEndTime
            })) : [] ,
            QuestionResponses: responseIncident.questionResponses ,
            LinkedIncident: responseIncident.linkedIncident ,
            ClaimStatus: responseIncident.claimStatus,
            MaxDaysAllowed: responseIncident.maxDaysAllowed,
            MaxReportableDays: responseIncident.maxReportableDays              
        }; 
      }
      public deepMap(obj:any, dest: any): any {
        let toReturn:any = {};
        let PageMapping = new Map();
        Object.keys(dest).forEach( x=> PageMapping.set(x.toLowerCase(), x));
        Object.entries(obj).forEach(el => {
            let prop = PageMapping.get(el[0].toLowerCase());
            if (prop) {
            if (Array.isArray(el[1])) {
                let newArr:any [] = [];
                el[1].forEach(x => {
                    if (typeof(x) === 'object'){
                        //TODO for now
                        newArr.push(this.deepMap(x, PageModel.getTypeDef(prop))); //TODO -> typesafe, implement type specifier in this scenario
                    }
                    else {
                        newArr.push(x);
                    }
                });
                toReturn[prop] = newArr;
            }
            else if (el[1] && typeof(el[1]) === 'object') {
                toReturn[prop] = this.deepMap(el[1], dest[prop]);
            }
            else {
                toReturn[prop] = el[1];
            }
            
        }
        });
        return toReturn;
      }
      

}