import { flowEvent } from "../mocks/mock-all";
import { saveAbsenceDateHandler } from '../../../src/functions'
import { startDb, stopDb, createTables, deleteTables } from "jest-dynalite";
import {DynamoDBClient, ListTablesCommand } from '@aws-sdk/client-dynamodb'
import {MockAll, TearDown} from '../mocks/mock-all'
import Sinon from "sinon";
jest.setTimeout(60000)
describe('Unit test for app handler',  () => {

    beforeAll(async () => { });
    beforeEach( async() => { 
        MockAll();  
        ////&console.log("In create");
    })

    afterEach ( async () => {
        ////&console.log("In delete");
        TearDown();
        await deleteTables();
        await createTables();
    })
    afterAll(async () => {
        ////&console.log("In stopdb");
        await stopDb();
    })
    it('verifies succesful response', async () => {
        let d = new Date();
        let mm = d.getMonth()+1;
        let dd = d.getDate();
        let yyyy = d.getFullYear();

        let dateString = `${mm<10? `0${mm}` : mm }${dd}${yyyy}`;
        
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                AbsenceDateFormatType: 'MMDDYYYY',
                ReportAbsenceDate: dateString
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await saveAbsenceDateHandler(flowObject)

        expect(result).toBeDefined();
        expect((result).IsSaveSuccess).toBe(true);
        expect((result).IsAbsDateWithinValidRange).toBe(true);
    });

    it('check for failing result', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                AbsenceDateFormatType: 'MMDDYYYY',
                ReportAbsenceDate: '20122023'
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await saveAbsenceDateHandler(flowObject)

        expect(result).toBeDefined();
        expect((result).IsSaveSuccess).toBe(false);
        expect((result).IsAbsDateWithinValidRange).toBe(false);
    });
})
