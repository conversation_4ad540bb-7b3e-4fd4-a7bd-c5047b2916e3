'use strict';

import moment from 'moment';
import {ConnectContactFlowEvent} from 'aws-lambda';
import {AbsenceReportingService, CallLog, LocalConfiguration, ScopedHttpService} from '@/services';
import { DateFormats} from '@/util';
import { AbsenceDates } from '@/models';

const callService = new CallLog();
const localConfig = new LocalConfiguration();
const reportingService = new AbsenceReportingService();
export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'saveAbsenceDate';
    callService.Logger.log('Input data', event);
    let contactId;
    let  result = {
        IsSaveSuccess: false
    };
    
    try 
    {
        contactId = event.Details.ContactData.ContactId;
        let dtFormat = event.Details.Parameters.AbsenceDateFormatType;
        let inputDate = event.Details.Parameters.ReportAbsenceDate;
        
        localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
        //TODO: PLACE IN COMMON METHOD
        const callData = await callService.getCallDataAsync(contactId);
        const currentIncident = (callData.Absences)?.peekLast();
        if (currentIncident) {
        const configuration = await localConfig.getClientConfigAsync(callData.ClientCode);
       const isDateRange = callData.IsDateRange;
        if (!configuration) {
            throw Error(`Configuration for ${callData.ClientCode} not loaded`);
        }

        const localMaxValidAbsenceDays = +(configuration.clientConfig.MaxAllowAbsenceDaysFuture || currentIncident.ReasonConfig?.MaxDaysAllowed || 0);
        const localMaxBeforeAbsenceDays = +(configuration.clientConfig.MaxAllowAbsenceDaysPast ||currentIncident.ReasonConfig?.ReportableDaysInPast || 0);
        const clientDateFormat = DateFormats(configuration?.clientConfig?.ClientDateFormat?? dtFormat);
        callService.Logger.log(`MaxValidAbsenceDays: ${localMaxValidAbsenceDays}; MaxBeforeAbsenceDays: ${localMaxBeforeAbsenceDays};`);

        let absenceDateParsed = moment(inputDate, clientDateFormat);
        if (!absenceDateParsed.isValid()) {
            return result;
        }
        
        callService.Logger.log(`Date is valid: ${absenceDateParsed}`);                
        const ScheduledStartTime = configuration?.clientConfig.DefaultWorkShift?.find(sft => sft.IsDefault)?.StartTimeHours;
        const ScheduledEndTime = configuration?.clientConfig.DefaultWorkShift?.find(sft => sft.IsDefault)?.EndTimeHours;
        if (currentIncident.AbsenceIncident ) 
        {
            if(isDateRange && Array.isArray(currentIncident.AbsenceIncident.AbsenceDates) && currentIncident.AbsenceIncident.AbsenceDates.length ==1 )
            {
                //check if lda >fda and add to 
                let fda = currentIncident.AbsenceIncident.AbsenceDates[0];
                
                if(fda != null || undefined){
                    let lda: AbsenceDates = {
                        IncidentId: currentIncident.AbsenceIncident?.AbsenceIncidentId,
                        ShiftStartTime: absenceDateParsed.format('YYYY-MM-DD HH:mm:ss'), 
                        ShiftEndTime: absenceDateParsed.format('YYYY-MM-DD HH:mm:ss'), 
                        ShiftDuration: null, //TODO: a bug in API's elastic search claims which cannot read this property. Has to be fixed there.
                        ScheduledShiftEndTime: absenceDateParsed.format('YYYY-MM-DD HH:mm:ss'),
                        ScheduledShiftStartTime: absenceDateParsed.format('YYYY-MM-DD HH:mm:ss'),
                        IsUpdated: false,
                        StartDate: absenceDateParsed.format('YYYY-MM-DD HH:mm:ss'),
                        EndDate: absenceDateParsed.format('YYYY-MM-DD HH:mm:ss'),
                        IsOverLap: false
                    };
                    // lda>fda
                    
                    //construct all dates 
                    var absenceDates = await AbsenceReportingService.updateAbsDatesInRange(fda, lda, ScheduledStartTime, ScheduledEndTime);
                    //check each date for duplicacy
                    //add and save all dates in AbsenceinCident                   
                   currentIncident.AbsenceIncident.AbsenceDates.pop();
                   absenceDates.forEach(dt => currentIncident.AbsenceIncident?.AbsenceDates.push(dt));
                    callData.Absences.pop();
                    callData.Absences.push(currentIncident);   
                    callService.Logger.log(`Absence pushed ${JSON.stringify(callData.Absences)}`) ;        
                    await callService.setCallDataAsync(contactId, {
                        IsSaved: "true",
                        Absences: callData.Absences
                    });
                }
                
            }
            else
            {
                let absenceDate: AbsenceDates = {
                    IncidentId: currentIncident.AbsenceIncident?.AbsenceIncidentId,
                    ShiftStartTime: absenceDateParsed.format('YYYY-MM-DD HH:mm:ss'), 
                    ShiftEndTime: absenceDateParsed.format('YYYY-MM-DD HH:mm:ss'), 
                    ShiftDuration: null, //TODO: a bug in API's elastic search claims which cannot read this property. Has to be fixed there.
                    ScheduledShiftEndTime: absenceDateParsed.format('YYYY-MM-DD HH:mm:ss'),
                    ScheduledShiftStartTime: absenceDateParsed.format('YYYY-MM-DD HH:mm:ss'),
                    IsUpdated: false,
                    StartDate: absenceDateParsed.format('YYYY-MM-DD HH:mm:ss'),
                    EndDate: absenceDateParsed.format('YYYY-MM-DD HH:mm:ss'),
                    IsOverLap: false
                };
                
                await AbsenceReportingService.updateAbsenceDate(absenceDate, {ScheduledEndTime});
                await AbsenceReportingService.updateAbsenceDate(absenceDate, {ScheduledStartTime});
                callService.Logger.log(`saving absence dates ${JSON.stringify(absenceDate)}`);
                ////************ fix this to not allow absence dates from previous */
                let overlap = await reportingService.checkOverlappingAbsences(
                    contactId,
                    absenceDate,
                );
                if(overlap.length >0)
                    absenceDate.IsOverLap = true;
                if(!absenceDate.IsOverLap)
                    currentIncident.AbsenceIncident?.AbsenceDates ? currentIncident.AbsenceIncident?.AbsenceDates.push(absenceDate) : currentIncident.AbsenceIncident.AbsenceDates = [absenceDate];
                
                callService.Logger.log(`Absence date overlap? ${absenceDate.IsOverLap}`);
                callData.Absences.pop();
                callData.Absences.push(currentIncident);   
                callService.Logger.log(`Absence pushed ${JSON.stringify(callData.Absences)}`) ;        
                await callService.setCallDataAsync(contactId, {
                    IsSaved: "true",
                    Absences: callData.Absences
                });
           
            }
            result.IsSaveSuccess = true;
        }
        callService.Logger.log('Result:', result);
        
    }
    else{
        callData.Absences.pop();
        callData.Absences.push(currentIncident);   
        callService.Logger.log(`Absence pushed back ${JSON.stringify(callData.Absences)}`) ;  
    }      
    await callService.setCallDataAsync(contactId, {
        IsSaved: "true",
        Absences: callData.Absences
        });
    return result;
    } catch (error: any) {
        reportingService.Logger.error('Error ', error);
        
        return result;
    }
};



