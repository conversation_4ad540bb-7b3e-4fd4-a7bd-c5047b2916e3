import {CacheClient, IAuthService, IHttpService} from '@/services'
import axios, {Axios, AxiosHeaders, AxiosStatic} from 'axios'
import {AnyConstructor, HttpClient} from 'Mixins'
import moment from 'moment'
/**
 * The auth mechanism is a cache client, that also has an HTTP service, and implements the IAuthService interface
 * @param target 
 * @returns 
 */

export const AuthClient  = <T extends AnyConstructor<CacheClient>>(target: T)=> { //FUTURE
    return class
      extends HttpClient(target)
      implements IAuthService, IHttpService
    {
      private authEndpoint: string = process?.env?.authApiUrl || '';
      public ContactId: string = '';
      
      constructor(...args: any[]) {
        super(...arguments);
      }

      protected parseJwt = (token: string) => {
        return JSON.parse(
          Buffer.from(token.split('.')[1], 'base64').toString(),
        );
      };

      protected isTokenExpired = (token: string): boolean => {
        this.log.debug('inside checkauth expiry');
        const parsed = this.parseJwt(token);
        const d = new Date(0);
        d.setUTCSeconds(parsed.exp);
        const now = moment(new Date());
        this.log.debug(`$UTC date: ${d} Moment time: ${now} Actual time: ${moment.tz(d, 'America/Toronto' )},  UTC time: ${moment.utc(parsed.exp)}`);
        return moment.tz(d, 'America/Toronto').isBefore(now)
      };

      public async fetchToken(): Promise<any> {
        console.log('inside fetch token');
        const response = await axios.post(
          this.authEndpoint,
          //FUTURE: AuthDTO
          {
            grant_type: process.env.grantType,
            client_id: process.env.client_id,
            client_secret: process.env.client_secret,
            scope: process.env.scope,
          },
          {
            headers: {
              'Content-Type':
                'application/x-www-form-urlencoded; charset=UTF-8',
            },
          },
        );
        let result = (response && response?.data?.access_token) || null;
        return result;
      }

      public async getToken(contactId: string) {
        const cacheKey = `authCache_${contactId}`;
        
        let accessToken: any = '';
        
        //first try getting token from dyn db
        try {
          accessToken = await this.getCachedAsync(
            cacheKey,
            () => this.fetchToken(),
            (val: string) => this.isTokenExpired(val),
          );
        } catch (err) {
          this.log.error(err);
          throw err;
        }
        return accessToken;
      }

      public async prepareRequest(contactId: string, payload?: any) {
        const token = await this.getToken(contactId);         
         let data = '';
         const config = {
                 headers: {
                     Authorization: `Bearer ${token}`,
                     'Content-Type': 'application/json'
                 },
                 params: {}
             }; 
          const qParams = new URLSearchParams();
          if(payload){
          if( payload.incidentIds)
          {
            payload.incidentIds.forEach((ids: number) => {
              qParams.append('incidentIds', `${ids}`)
            })
            config.params = qParams;
            data = payload.cancelRequest? JSON.stringify(payload.cancelRequest): data;
          }           
          else
            data = JSON.stringify(payload);
        }
        return {
          payload: data,
          config: config
        }       
    }
  };
}
  