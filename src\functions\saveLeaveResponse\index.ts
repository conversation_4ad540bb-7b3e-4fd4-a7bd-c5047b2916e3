import { ConnectContactFlowEvent } from 'aws-lambda';
import { CallLog, LocalConfiguration, AbsenceApiService, ScopedHttpService } from '@/services';
import { Constants, PromptHelper, RefAbsenceType } from '@/util';
import { DropDownOption, PageModel, PageSectionModel, ClientConfigWrapper, LocaleModel } from '@/models';

const callService = new CallLog();
const localConfig = new LocalConfiguration();
const absenceApi = new AbsenceApiService();

export const handler = async (event: ConnectContactFlowEvent) => {
    let res =
    {
        IsAbsenceTreatmentChk: false,
        PromptAbsenceTreatmentSSML: ''//#
    }
    callService.Logger.log('Input data', event);
    let contactId = event.Details.ContactData.ContactId;
   
    let associateClaim = event.Details.Parameters.AssociateClaim;
    let selectedClaimOption = event.Details.Parameters.SelectedClaimOption;
    let newLeaveClaimOption = +(event.Details.Parameters.NewLeaveClaimOption)
    //let selectedAbsenceTreatmentOption = +(event.Details.Parameters.SelectedAbsenceTreatmentOption);
    const callData = await callService.getCallDataAsync(contactId);
    let currentAbsence = (callData.Absences)?.pop();
    let selectedLang = callData.SelectedLang ?? 'en';
    localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
    const configuration = await localConfig.getClientConfigAsync(callData.ClientCode);
    if(currentAbsence && currentAbsence.AbsenceIncident){
    //*********the question responses are hard-coded intentionally. otherwise while sending api post request it send gibberish in the request */
    if(associateClaim != undefined && associateClaim === 'true' && selectedClaimOption != undefined){
        let selectedOpenClaim = currentAbsence.OpenCases[+selectedClaimOption -1];
        let selectedClaimId = selectedOpenClaim.ClaimId;
        let associateResponse = {"CQ_ASK_FOR_EXISTING_LEAVE_CASE": selectedClaimId};
        currentAbsence.AbsenceIncident.QuestionResponses = currentAbsence.AbsenceIncident.QuestionResponses ? { ...currentAbsence.AbsenceIncident.QuestionResponses, ...associateResponse } : { associateResponse };
        let applyLeaveReferral = { "CQ_APPLY_FOR_LEAVE_REFERRAL": "false" };
        currentAbsence.AbsenceIncident.QuestionResponses = currentAbsence.AbsenceIncident.QuestionResponses ? { ...currentAbsence.AbsenceIncident.QuestionResponses, ...applyLeaveReferral } : { applyLeaveReferral };
    }
    else if(newLeaveClaimOption != undefined){
        let applyLeaveReferral = { "CQ_APPLY_FOR_LEAVE_REFERRAL": newLeaveClaimOption === 1 ? "true" : "false" };
        currentAbsence.AbsenceIncident.QuestionResponses = currentAbsence.AbsenceIncident.QuestionResponses ? { ...currentAbsence.AbsenceIncident.QuestionResponses, ...applyLeaveReferral } : { applyLeaveReferral };
        let associateResponse = {"CQ_ASK_FOR_EXISTING_LEAVE_CASE": "0"}
        currentAbsence.AbsenceIncident.QuestionResponses = currentAbsence.AbsenceIncident.QuestionResponses ? { ...currentAbsence.AbsenceIncident.QuestionResponses, ...associateResponse } : { associateResponse };
        
    }            
    if(currentAbsence.AbsenceIncident.AbsenceType == RefAbsenceType.Intermittent)
    {
        let stdReferralResponse = { "CQ_APPLY_FOR_STD_REFERRAL" : "false"} ;
        currentAbsence.AbsenceIncident.QuestionResponses = currentAbsence.AbsenceIncident.QuestionResponses? {...currentAbsence.AbsenceIncident.QuestionResponses, ...stdReferralResponse} : {stdReferralResponse};
    }
    
        //1. send absence dates to API 
        let updatedIncident = await absenceApi.updateIncidentAsync(callData.ClientCode, contactId, currentAbsence.AbsenceIncident );
        if(updatedIncident.AbsenceIncident != null){
            currentAbsence = {...currentAbsence, ...updatedIncident};            
        }
        callData.Absences.pop();
        callData.Absences.push(currentAbsence);
        await callService.setCallDataAsync(contactId, {
            IsSaved: "true",
            Absences: callData.Absences
        });
        }
        let classifyAbsQText = '';
        let isLeaveEnabled = configuration?.clientConfig.EnableLeaveCreation ?? false;
        if(isLeaveEnabled){//***check this value type */
            callService.Logger.log('Leave creation enabled');
             // Is your absence for treatment or appointment or flare-up?
            //For Treatment Or Appointment, Press 1, For Flare-up, Press 2
            let fixedQuestions: any = currentAbsence.QuestionReponse?.filter((page: PageModel) => page.PageId === Constants.LEAVE_QUESTION_PAGE_ID).peekLast()  ;
            let lvSection: PageSectionModel = fixedQuestions?.Sections?.filter((sec: PageSectionModel) => sec.SectionId === Constants.LEAVE_QUESTION_PAGE_ID).peekLast();
            
            let classifyAbsQ = lvSection?.Questions?.filter((q) => q.Name === `${Constants.CompanySpecificQuestions_Prefix}${Constants.AskForClassifyAbsenceQuestion}`).peekLast();
            classifyAbsQText = classifyAbsQ != undefined? (classifyAbsQ.Label.length > 0 ? configuration.getPromptText(classifyAbsQ.Label, selectedLang, false):''): '';

            let classifyAbsQOptlabel = [];
            classifyAbsQOptlabel.push(classifyAbsQText);
            if(classifyAbsQ != undefined && Array.isArray(classifyAbsQ.Options))
            {
                callService.Logger.log('Api returned classify absence Q');
                let optionTexts = classifyAbsQ.Options?.map((o: DropDownOption) => (o.Text));
                let optionValues = classifyAbsQ.Options?.map((o: DropDownOption) => (o.Value) );
                callService.Logger.log('Options found:', JSON.stringify(optionValues) );
                classifyAbsQOptlabel.push( buildAbseClassificationMenu(optionTexts, configuration, configuration?.clientConfig.AbsenceClassificationOptText ?? [], selectedLang));
                
                res.PromptAbsenceTreatmentSSML = PromptHelper.wrapSSML(classifyAbsQOptlabel);
                callService.Logger.log('absence classification prompt is:', res.PromptAbsenceTreatmentSSML);
                res.IsAbsenceTreatmentChk = true;
            }
        }
        callService.Logger.log('Result', res);
        return res;

}
function buildAbseClassificationMenu(options:LocaleModel[][], configuration: ClientConfigWrapper, optText: LocaleModel[], selectedLang: string):string
{
    let resPrompt = '';
    const res: string[] = [];
    let selectedLangOptText = configuration.getPromptText(optText, selectedLang,false);
    let filteredOption = options.map(optionGroup => 
        optionGroup.filter(option => option.Locale?.toLocaleLowerCase() === selectedLang.toLocaleLowerCase())
    )
    if(filteredOption != undefined)
        filteredOption.forEach((x,idx) => {
        let optLabel = configuration.getPromptText(x, selectedLang, false); //Flare-up
        res.push(selectedLangOptText.replace('{Option}', optLabel).replace('{number}', (idx+1).toString()));
    });
    resPrompt = res.join(". ");
    return resPrompt;

}
