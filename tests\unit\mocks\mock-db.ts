export type GetItemOutput = GetDbResult;
import AWS, { AWSError, Request } from 'aws-sdk';
import { DocumentClient } from 'aws-sdk/clients/dynamodb'

export default class DocumentClientMock extends DocumentClient{

    private tables: Record<string, DbTable> = {}
    
    public addTable(tableName: string, tableKey: any) {
        this.tables[tableName] = {PrimaryKey : tableKey};
    }
    
    public get(params: GetDbParams): GetDbResult {
        let table = this.tables[params.TableName];
        let toReturn:any = null;
        try {
            Object.keys(params.Key.entries).forEach( x => {
                if (x[0] == table.PrimaryKey && toReturn == null) {
                    toReturn = table[x[1]];
                }
            })
        }
        catch (err: any) {
            //&console.log("Item not found");
        }
        return new GetDbResult(toReturn);
    }

public put (params: PutDbParams): PutDbResult {
    try {
    let table = this.tables[params.TableName];
    let key = table.PrimaryKey;
    Object.entries(params.Item).forEach(x => {
        if (x[0]===key) {
            table[x[1]] = params.Item;
            return new PutDbResult(params.Item);
        }
    })

    }
    catch (err: any) {
        //&console.log("Error putting item")
    }
    return new PutDbResult(null);
}

}


class GetDbResult extends Request<GetDbResult, AWSError> implements ItemOutput  {
    Item?: any;
   
    constructor(res: any) {
        super(new AWS.Connect({
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_KEY,
         }), 'none', null);
        this.Item = res;
    }
    async promise(): Promise<any> {
        return Promise.resolve(this.Item);
    }
}

class PutDbResult extends  Request<PutDbResult, AWSError> implements ItemOutput {
    Attributes?: any;

    constructor(res: any) {
        super(new AWS.Connect({
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_KEY,
         }), 'none', null);
        this.Attributes = res;
    }
    async promise(): Promise<any> {
        return Promise.resolve(this?.Attributes);
    }
}

interface GetDbParams {
    TableName: string;
    Key: Map<string, string>;
}
type PutItemInputAttributeMap = {[key: string]: any};

interface PutDbParams {
    TableName: string;
    Item: PutItemInputAttributeMap;
}

interface DbTable {
    PrimaryKey: string;
    [Key: string]: any;
}


interface ItemOutput {
    ConsumedCapacity?: any;

    promise(): Promise<any>;
  }