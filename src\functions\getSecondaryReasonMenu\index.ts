import {
  ReasonMenuService,
  ScopedHttpService,
  CallLog,
  LocalConfiguration
} from '@/services';

import { ConnectContactFlowEvent } from 'aws-lambda';
import {
  AnswerWrapper,

  ReasonWrapper,
} from '@/models/ReasonModel';
import { ReasonFlow } from '@/models/AbsenceModel';
import { PromptHelper } from '@/util';
import { EmployeeData, EmployeeDataDetails } from '@/models';
import { GetCountry, GetProvinceCode } from '@/models/EmployeeData';

const reasonConfig = new ReasonMenuService();
const callService = new CallLog();
const localConfig = new LocalConfiguration();

export const handler = async (event: ConnectContactFlowEvent) => {
  const lambdaName = 'getSecondaryReasonMenu';
  reasonConfig.Logger.log('Input data', event);
  let returnSSML: string[] = [];
  const clientCode = event.Details.Parameters.ClientCode;
  const contactId = event.Details.ContactData.ContactId;
  
  const selectedOption: number = parseInt(event.Details.Parameters.SelectedPrimaryReason,);

  reasonConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
  localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
  let callData = await callService.getCallDataAsync(contactId);
  var empData: any = callData.EmployeeInfo;

  let employeeNumber: string = '';
  if (callData && callData.EmployeeInfo)
  {
    employeeNumber = callData.EmployeeInfo.employeeNumber;
  }

  let primaryReasons: ReasonWrapper = await reasonConfig.getReasonMenuAsync(clientCode, employeeNumber);

  const selectedItem = primaryReasons.WrappedAnswers[selectedOption - 1];

  const locale = callData?.SelectedLang ?? "EN";
  let reasonFlow: ReasonFlow = callData?.Reasons ?? {
    ReasonEntries: [],
    Count: 0,
  };
  reasonFlow = reasonFlow.Count == 0 ? ReasonMenuService.advanceFlow(
    reasonFlow,
    selectedItem,
    selectedOption,
    locale
  ): reasonFlow;

  const config = await localConfig.getClientConfigAsync(clientCode, locale);
  
  await callService.setCallDataAsync(contactId, {
    Reasons: reasonFlow,
    IsSaved: "true",
  });
  let isTransfer = false;
  var numberOfOptions = selectedItem.wrappedSubAnswers.length;
  if(numberOfOptions === 1)
  {
    const selectedSecondaryOption = 1;
    let selectedItem = primaryReasons.traverseReasons(reasonFlow, selectedSecondaryOption);
  
    reasonFlow = ReasonMenuService.advanceFlow(
      reasonFlow,
      selectedItem,
      selectedOption,
      locale
    );
    isTransfer = selectedItem.answerModel.Ar3IvrReasonSettings?.IsTransfer ?? false; 
    await callService.setCallDataAsync(contactId, {
      Reasons: reasonFlow,
      IsSaved: "true",
    });
  }

  var configOptionText = config.getPromptText(config.clientConfig.SecondaryReasonMenuOptionText, locale, false) ?? '';
  var optionText = primaryReasons.getSecondaryReasonOptionsText(locale, configOptionText, selectedItem);
  var configRepeatTxt = config.getPromptText(config.clientConfig.SrOptionRepeatText, locale, false) ?? '';
  var repeatTxt = primaryReasons.getRepeatText(locale, configRepeatTxt, config.clientConfig.PRRepeatDigit ?? '');
  optionText.push(repeatTxt);
  isTransfer = selectedItem.answerModel.Ar3IvrReasonSettings?.IsTransfer ?? false; 
  let byPassOption = selectedItem.wrappedSubAnswers.length > 0 ? (selectedItem.wrappedSubAnswers.findIndex((x) => x.answerModel?.Ar3IvrReasonSettings?.IsSpecialMenu) > 0
      ? selectedItem.wrappedSubAnswers.findIndex((x) => x.answerModel?.Ar3IvrReasonSettings?.IsSpecialMenu) +1 : "nobypass"): selectedOption;
  //let byPassOption = selectedItem.answerModel.Ar3IvrReasonSettings?.IsSpecialMenu ? selectedOption: "nobypass";

  return {
    IsSaveSuccess: true,
    PromptSecondaryReasonMenuOptionsSSML: numberOfOptions === 1? '': PromptHelper.wrapSSML(optionText),
    NumberOfOptions: selectedItem.wrappedSubAnswers.length,
    OptionToRepeat:  config.clientConfig.SRRepeatDigit ?? "*",
    IsExternalTransfer: isTransfer, //TODO
    IntroSecondaryMenuBypassOption: byPassOption,
    IsMainReasonBereavment: selectedItem.isBereavement, // <--TODO ths really needs to be configured properly on the DB/Config side...
    PromptSpecialMsgSSML: config.getPromptText(config.clientConfig.SecondaryReasonSpecialText, locale, false) ?? '',
    PromptIntroSecondaryReasonMenuSSML: numberOfOptions === 1? '': config.getPromptText(config.clientConfig.SecondaryReasonIntroText, locale),
    PromptAlertSSML: '', //TODO
    IsPlayClaimsDetail: ((config.clientConfig.EnableLeaveCreation ?? false) && (selectedItem.answerModel.AnswerCode != null || undefined)) ? true: false
  };
};
