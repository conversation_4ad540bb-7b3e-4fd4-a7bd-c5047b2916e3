import { CacheClient} from '@/services';
import { AuthClient } from 'Mixins';

const ScopedAuthCache = new Map<string, string>();

export class ScopedHttpService extends AuthClient(CacheClient) {
  ContactId: string;
  constructor(ContactId: string) {
    super(...arguments);
    this.ContactId = ContactId;
    this.AxiosClient.interceptors.request.use(async (config) => {
      config.headers.Authorization = `Bearer ${await this.getInMemToken(
        this.ContactId,
      )}`;
      config.headers['Content-Type'] = 'application/json'; //TODO: Make callback
      return config;
    });
  }

  private async getInMemToken(cacheKey: string): Promise<string | null> {
    let inMemToken: string;
    if (ScopedAuthCache.has(cacheKey)) {
      inMemToken = ScopedAuthCache.get(cacheKey) || '';
      try {
        if (!this.isTokenExpired(inMemToken)) {
          return inMemToken;
        }
      } catch (error) {
        this.log.warn(`Token for ${this.ContactId} is invalid`);
      }
    }
    inMemToken = await this.getToken(this.ContactId);
    ScopedAuthCache.set(this.ContactId, inMemToken);
    return inMemToken;
  }
}
