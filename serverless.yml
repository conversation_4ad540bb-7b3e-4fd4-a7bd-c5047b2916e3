service: abiliti-absenceapi-ivrlambdas
# app and org for use with dashboard.serverless.com
#app: your-app-name
#org: your-org-name

# You can pin your service to only deploy with a specific Serverless version
# Check out our docs for more details
frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  timeout: 600

# you can overwrite defaults here
#  stage: dev
#  region: us-east-1


functions:
  authenticateEmployee:
    handler: build/functions/authenticateEmployee/index.handler
    events:
      - httpApi:
          path: /authenticateEmployee
          method: get
          
          

plugins:
    - serverless-offline

custom:
  serverless-offline:
    noPrependStageInUrl: true
