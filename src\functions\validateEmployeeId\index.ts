import { ConnectContactFlowEvent } from 'aws-lambda'
import { AbsenceReportingService, CallLog, LocalConfiguration, ScopedHttpService } from '@/services'
import {  ValidationErrors } from '@/util'


const reportingService = new AbsenceReportingService(); // FUTURE - don't instantiate like this
const callService = new CallLog(); // FUTURE - don't instantiate like this
const localConfig = new LocalConfiguration();

export const handler = async (event: ConnectContactFlowEvent) => {
    const lambdaName = 'validateEmployeeId';
    localConfig.Logger.log('Input data', event);
    let contactId: string='';
    try {

        contactId = event.Details.ContactData.ContactId;
        const clientCode = event.Details.Parameters.ClientCode;
        const employeeNumber = event.Details.Parameters.EmployeeId;
        const isValidEmpId = await reportingService.validateEmployeeIdAsync(contactId,clientCode,employeeNumber);
        localConfig.AuthenticatedHttpClient = new ScopedHttpService(contactId);
        const config = await localConfig.getClientConfigAsync(clientCode);
        let session = await callService.getCallDataAsync(contactId);
        let selectedLang = session.SelectedLang ?? 'en';
        localConfig.Logger.log('selectedLang', selectedLang);
        let enterDobText = config.getPromptText(config.clientConfig.EnterDobText ?? [], selectedLang, true);
        localConfig.Logger.log('enterDobTextPrompt', enterDobText);
        await callService.logStageAsync(
            contactId, 
            lambdaName, 
            isValidEmpId? null: { Validation: ValidationErrors.InvalidEmployeeId },
            isValidEmpId);
        
        localConfig.Logger.log('isValidEmpId', isValidEmpId);    
        return {
            IsEmployeeIdValid: isValidEmpId,
            PromptEnterDobSSML: enterDobText
   
        };
    }
    catch(error: any){
        reportingService.Logger.error('Error ', error);
        return {message: error.message, 
            IsEmployeeIdValid: false};
    }
}
