export const Constants = {
    LEAVE_QUESTION_PAGE_ID: "1P",
    COMPANY_QUESTION_PAGE_ID: "1C",
    FIXED_QUESTION_PAGE_ID: "1F",
    FIXED_TRIGGER_QUESTION_PAGE_ID: "1FA",
    CompanySpecificQuestions_Prefix: "CQ_",
    LeaveQuestions_Prefix: "PS_",
    OfficeAddressType: 1,
    HomeAddressType: 2,
    MailAddressType: 3,
    ApplyForStdReferral: "APPLY_FOR_STD_REFERRAL",
    ApplyForLeaveReferral: "APPLY_FOR_LEAVE_REFERRAL",
    AskForExistingLeaveCase: "ASK_FOR_EXISTING_LEAVE_CASE",
    IncidentTypeLeaveCss: "INCIDENT_GENERIC_LEAVE",
    IncidentTypeArCss: "INCIDENT_MYABILITI_AR",
    AskForClassifyAbsenceQuestion: "ASK_FOR_CLASSIFY_ABSENCE_QUESTION",
    FmlaPolicyCode: "fmla",
    MERGECODE_TOTAL_ABSENCEDAYS: "TotalAbsenceDays",
    MERGECODE_MAX_FUTUREDAYS: "MaxFutureDays",
    MERGECODE_MAX_PASTDAYS: "MaxPastDays",
    MERGECODE_LAST_DATE_OF_ABSENCE: "LastAbsenceDate",
    Error_Code_Absence_Shift_Start: "absence_incident-overlap_shift_start",
    Error_Code_Absence_Shift_End: "absence_incident-overlap_shift_end",
    Error_Code_Absence_WholeShift: "absence_incident-shift_overlap",
    LocaleString_None   : "",
    LocaleString_English: "en",
    LocaleString_Spanish: "es",
    LocaleString_French : "fr",
    MultiLanguage_EN    : "0",
    MultiLanguage_EN_ES : "1",
    MultiLanguage_EN_FR : "2",
    MultiLanguage_Future: "3",
    StarKey_EN : "star",
    StarKey_ES : "asterisco",
    StarKey_FR : "l’étoile",
    PoundKey_EN : "pound",
    PoundKey_ES : "tecla de numeral",
    PoundKey_FR : "carré",
    DollarKey_EN : "dollar",
    DollarKey_ES : "dólar",
    DollarKey_FR : "dollar",
}
export const ClaimStatus = {
Created : "Created",
Updated: "Updated",
Submitted: "Submitted",
//the following are used as return status of claims  to call flow fro lambda
InProgress: "INPROGRESS",
Success: "Success"
}
export const RefAbsenceType ={
    Continuous:"continuous" ,
    Intermittent: "intermittent"

}

export const AbilitiClaimStatus = {
    Cancelled : "C",
    Submitted: "S",
    InProgress: "I"
    }
export enum DocumentManagementEntityTypes{
    MyAbilitiAbsence,
    Case,
    Referral,
    General,
    MyAbilitiAbsenceVM
}