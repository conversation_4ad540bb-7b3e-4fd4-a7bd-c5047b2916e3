import { flowEvent } from "../mocks/mock-all";
import { validateshiftEndHandler } from '@/functions'
import { startDb, stopDb, createTables, deleteTables } from "jest-dynalite";
import {MockAll, TearDown} from '../mocks/mock-all'
import Sinon from "sinon";
jest.setTimeout(60000)

describe('Unit test for app handler',  () => {

    beforeAll(async () => {});
    beforeEach( async() => { 
        MockAll();  
        ////&console.log("In create");
    })

    afterEach ( async () => {
        ////&console.log("In delete");
        TearDown();
        await deleteTables();
        await createTables();
    })
    afterAll(async () => {
        ////&console.log("In stopdb");
        await stopDb();
    })
    it('verifies successful response', async () => {
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ShiftEndTime: '0530',
                AMorPM: 2
              }
          }
            };
        const result = await validateshiftEndHandler(flowObject)
        console.log(result);
        expect(result).toBeDefined();
        expect((result).IsShiftEndTimeValid).toBe(true);
        expect((result).ShiftTimeNotValidReason).toBe('');
       
        
        // expect(result.message).toEqual(null);
    });

    it('check for failing result', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ShiftEndTime: '1430',
                AMorPM: 2
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await validateshiftEndHandler(flowObject)

        expect(result).toBeDefined();       
        expect((result).IsShiftEndTimeValid).toBe(false);
        expect((result).ShiftTimeNotValidReason).toBeDefined();

    });
    it('check for failing result', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ShiftEndTime: '1930',
                AMorPM: 2
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await validateshiftEndHandler(flowObject)

        expect(result).toBeDefined();       
        expect((result).IsShiftEndTimeValid).toBe(false);
        expect((result).ShiftTimeNotValidReason).toBeDefined();

    });
    it('check for failing result', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ShiftEndTime: '1930',
                AMorPM: 1
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await validateshiftEndHandler(flowObject)

        expect(result).toBeDefined();       
        expect((result).IsShiftEndTimeValid).toBe(false);
        expect((result).ShiftTimeNotValidReason).toBeDefined();

    });
    it('check for failing result', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ShiftEndTime: '2930',
                AMorPM: 1
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await validateshiftEndHandler(flowObject)

        expect(result).toBeDefined();       
        expect((result).IsShiftEndTimeValid).toBe(false);
        expect((result).ShiftTimeNotValidReason).toBeDefined();
    });
})
