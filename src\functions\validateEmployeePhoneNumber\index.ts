import {CallLog, AmazonPinpointservice} from '@/services'
import {LogService} from '@/util'
import { ConnectContactFlowEvent } from 'aws-lambda'

const callService = new CallLog();
const pinpoint = new AmazonPinpointservice();


export const handler = async(event: ConnectContactFlowEvent) => {

    const lambdaName = 'validateEmployeePhoneNumber';
    callService.Logger.log('Input data', event);
    let contactId;
    try {
        
        contactId = event.Details.ContactData.ContactId;
        const empPhoneNum = event.Details.Parameters.EmployeePhoneNumber;
        const callData = await callService.getCallDataAsync(contactId);
        let currentAbsence = (callData.Absences)?.peekLast();
        const result = await pinpoint.isEmployeePhoneValid(empPhoneNum);
        //const result = empPhoneNum.length == 12;
        callService.Logger.log('call data', callData.Absences );
        if (currentAbsence.AbsenceIncident && result && result?.PhoneType !== 'INVALID' )
        {
            
            currentAbsence.AbsenceIncident.BestPhoneNumber = result?.CleansedPhoneNumberNational
            let bestPhoneNumber = { "CQ_BEST_PHONE_NUMBER_TO_BE_REACHED": result?.CleansedPhoneNumberNational  };
            currentAbsence.AbsenceIncident.QuestionResponses = currentAbsence.AbsenceIncident.QuestionResponses ? { ...currentAbsence.AbsenceIncident.QuestionResponses, ...bestPhoneNumber } : { bestPhoneNumber };       
            callData.Absences.pop();
            callData.Absences.push(currentAbsence);  
            callService.Logger.log('validateEmployeePhoneNumber result', JSON.stringify(result) ); 
        
            await callService.setCallDataAsync(contactId, {
            IsSaved: "true",
            Absences: callData.Absences
            });
        }
        
        return{
            IsSaveSuccess: result != null || undefined ? true: false,
            IsEmployeePhoneNumberValid: result != null || undefined? result?.PhoneType !== 'INVALID': false,
            EmployeePhoneNumberPlayable: result?.CleansedPhoneNumberNational,
            EmployeePhoneNumberE164: result?.CleansedPhoneNumberE164
        }
    }
    catch (error: any) {
        callService.Logger.error('Error ', error);
        return {
            error: error.message,
            result: null,
        };
    }

}