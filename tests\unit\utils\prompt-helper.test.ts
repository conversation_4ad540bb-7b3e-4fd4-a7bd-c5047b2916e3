import {PromptHelper} from '../../../src/util'
import { LocaleModel } from "../../../src/models";
import  moment, { Moment } from 'moment';


let helper = new PromptHelper();

it('verifies successful response', async () => {
    let locale1Prompt = "<speak><amazon:domain name='conversational'>hello test</amazon:domain></speak>"
    let domain = 'conversational'
    let locale1 = ['hello test']
    const result = PromptHelper.wrapSSML(locale1, domain);
    //&console.log(result);
    expect(result).toBeDefined();
    
    expect((result)).toBe(locale1Prompt);
   
});

it('verifies successful response', async () => {
    let absenceDate: Moment = moment('08292023', 'MMDDYYYY').startOf('day');
    let absenceDatePlayable = absenceDate.format('YYYY-MM-DD');
    const result = PromptHelper.addInterpretTag("date", 'en', '<AbsenceDatePlayable>');
    console.log(result);
    expect(result).toBeDefined();
    
    //expect((result)).toBe(locale1Prompt);
   
   
});


it('verifies successful response', async () => {
    let absenceDate: Moment = moment('08292023', 'MMDDYYYY').startOf('day');
    let absenceDatePlayable = absenceDate.format('YYYY-MM-DD');
    let expected = "<speak><amazon:domain name='conversational'>Confirm Absence Date 1 Text <say-as interpret-as='date format='mmdd'> 2023-08-29 </say-as> <break time='500ms'/> </amazon:domain></speak>"
    let locale1Prompt: LocaleModel[] = [
        {
            Locale: "EN",
            Description: "Confirm Absence Date 1 Text <dateString> "
        },
        {
            Locale: "FR",
            Description: null
        }
    ];
    const result1 = PromptHelper.addInterpretTag("date", 'en', absenceDatePlayable);
    let confirmAbsencePrompt = locale1Prompt.map((x: LocaleModel) =>({ 
        Locale: x.Locale,
        Description: x.Description?.replace('<dateString>', result1)})
        )
    
    const result2 = PromptHelper.filterAndWrapSSML(confirmAbsencePrompt)
    console.log(result2);
    expect(result1).toBeDefined();
    
    expect((result2)).toEqual((expected));
    
   
});