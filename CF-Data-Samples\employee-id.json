{"Details": {"ContactData": {"Attributes": {"PromptSubOpeningSSML1": "<speak><amazon:domain name='conversational'>Kindly note you can record your absences for 60 days in future OR within previous 2 days. Please note that you can report a maximum of 60 absences per call. You can also report your absences online via Abiliti Absence at goodyear.abilitiabsenceus.com. A confirmation number will be provided to you at the end of the call.</amazon:domain></speak>", "DefaultDateFormatType": "1", "LambdaValidateEmployeeIdARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateEmployeeId", "PromptLanugageSelectionS3URI": "", "LambdaValidateEmployeePhoneNumberARN": "", "LambdaSubmitAbsenceARN": "", "PromptEnterEmployeeIdSSML": "<speak><amazon:domain name='conversational'> Please enter your employee I D using your keypad followed by pound key. If you do not know your employee I D, please disconnect and contact your employer to report your absence.</amazon:domain></speak>", "LambdaGetPrimaryReasonMenuARN": "", "LambdaValidateAbsenceDateARN": "", "LambdaValidateShiftStartTimeARN": "", "LambdaValidateShiftEndTimeARN": "", "CallFlowEnterReasonARN": "", "CallFlowSubmitAbsenceIfErrorARN": "", "LambdaSaveSecondaryReasonARN": "", "MaxTotalMissedAbsenceDays": "5", "LambdaAuthenticateEmployeeARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-AuthenticateEmployee", "IsEnterEmployeePhoneNumberRequired": "true", "LambdaSetReturnToWorkARN": "", "LambdaValidateReturnToWorkDateARN": "", "MaxRetryMenu1": "3", "MaxRetryMenu2": "", "Marker1": "test", "Marker": "<PERSON><PERSON> After Playing 3rd prompt", "CallFlowClosingARN": "", "MaxRetryLambda2": "3", "LambdaGetClientConfigARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetClientConfig-temppoc", "MaxRetryLambda1": "3", "Environment": "LAB", "CallFlowGetOpeningMsgV1ARN": "ec9c237c-1a16-4eed-a729-66061cdf0e53", "CallFlowEnterEmployeePhoneNumberARN": "", "PromptSpeicalOpeningMsgSSML": "", "MaxAllowAbsenceDaysPast": "14", "LambdaValidateDoBARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-ValidateDoB", "PromptMaxInvalidEmployeeIdSSML": "<speak><amazon:domain name='conversational'>Sorry, your absence cannot be recorded without your correct Employee number Please call back later Goodbye.</amazon:domain></speak>", "DefaultTimeFormatType": "1", "CallFlowTransferToCSRARN": "", "PreviousAbsenceWithinAA": "2", "LambdaGetSecondaryReasonMenuARN": "", "MaxAllowAbsenceDaysFuture": "60", "LambdaCheckAllAbsReportedARN": "", "CallFlowEnterAbsenceDateARN": "", "IsLambdaMaxErrorRetry": "false", "PromptInvalidEmployeeIdSSML1": "<speak><amazon:domain name='conversational'>The Employee ID you entered is</amazon:domain></speak>", "PromptInvalidEmployeeIdSSML2": "<speak><amazon:domain name='conversational'>This does not match with our records. Please try again</amazon:domain></speak>", "LambdaValidateRTWShiftStartTimeARN": "", "CallFlowValidateEmployeeARN": "f5467b78-f173-46fc-83b0-06391bdfc027", "IsMenuMaxInvalidRetry": "false", "IsAllowInteruppted": "false", "MutliLanguageId": "0", "LambdaSaveTotalAbsenceDaysARN": "", "LambdaGetCarrierTransferNumberARN": "", "EmployeeId": "00262843", "PromptUnexpectedErrorSSML": "put ssml here", "DefaultLanguage": "English", "CallFlowDynamicFlowV1ARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330/contact-flow/78498ff6-24e1-433c-a281-0aed5c8cc131", "LambdaGetOpeningMsgV1ARN": "arn:aws:lambda:us-east-1:389331747299:function:ABLARIVR-GetOpeningMsgV1-temppoc", "PromptOpeningSSML": "<speak><amazon:domain name='conversational'>Thank you for calling the Goodyear Absence Reporting System. Please note that effective 1/1/2023, UNUM administers leave under the Family Medical Leave Act (FMLA) and time away from work due to Short Term Disability (STD) and Accident and Sickness. Please listen carefully as the options might have changed.</amazon:domain></speak>", "CallFlowEnterTotalAbsenceDaysARN": "", "LambdaSaveAbsenceDateARN": "", "CallFlowSubmitAbsenceARN": "", "CallFlowEnterRTWARN": "", "CallFlowTransferToExternalARN": "", "PromptTransferNumberGeneralErrorSSML": ""}, "Channel": "VOICE", "ContactId": "3c3488fc-62db-40a1-9109-b9f1cf07600b", "CustomerEndpoint": {"Address": "+16782033318", "Type": "TELEPHONE_NUMBER"}, "CustomerId": null, "Description": null, "InitialContactId": "3c3488fc-62db-40a1-9109-b9f1cf07600b", "InitiationMethod": "INBOUND", "InstanceARN": "arn:aws:connect:us-east-1:389331747299:instance/fc1f9343-a702-436d-88c9-e44a25c2d330", "LanguageCode": "en-US", "MediaStreams": {"Customer": {"Audio": null}}, "Name": null, "PreviousContactId": "3c3488fc-62db-40a1-9109-b9f1cf07600b", "Queue": null, "References": {}, "SystemEndpoint": {"Address": "+16472438396", "Type": "TELEPHONE_NUMBER"}}, "Parameters": {"ClientCode": "starbucks", "ClientName": "GOODYEAR", "EmployeeId": "00262843"}}, "Name": "ContactFlowEvent"}