import { flowEvent } from "../mocks/mock-all";
import { validateRtwHandler } from '@/functions';
import { AbsenceReportingService } from '@/services';
import { startDb, stopDb, createTables, deleteTables } from "jest-dynalite";
import {MockAll, TearDown} from '../mocks/mock-all'
import { RtwResponse } from "@/models";
import { ValidationErrors } from '@/util';
import Sinon from "sinon";
jest.setTimeout(60000)


describe('Unit test for app handler',  () => {

    beforeAll(async () => {});
    beforeEach( async() => { 
        MockAll();  
        ////&console.log("In create");
    })

    afterEach ( async () => {
        ////&console.log("In delete");
        TearDown();
        await deleteTables();
        await createTables();
    })
    afterAll(async () => {
        ////&console.log("In stopdb");
        await stopDb();
    })
    it('verifies successful response', async () => {
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ReturnDayToWork: '03122023',
                RTWDateFormatType: '1'
              }
          }
        };
        
        const result = await validateRtwHandler(flowObject)
        console.log(result);
        expect(result).toBeDefined();
        expect((<RtwResponse>result).IsSaveSuccess).toBe(true);
        expect((<RtwResponse>result).IsRTWValid).toBe(true);
        expect((<RtwResponse>result).RTWNotValidReason).toBeNull();
        expect((<RtwResponse>result).ReturnDayToWorkplayable).toBeDefined();                
    });

    it('check for failing result', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ReturnDayToWork: '99999999',
                RTWDateFormatType: '1'
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await validateRtwHandler(flowObject)
        console.log(result);
        expect(result).toBeDefined();
        expect((<RtwResponse>result).IsSaveSuccess).toBe(false);
        expect((<RtwResponse>result).IsRTWValid).toBe(false);
        expect((<RtwResponse>result).RTWNotValidReason).toBe(ValidationErrors.InvalidRTWDateFormat);
        expect((<RtwResponse>result).ReturnDayToWorkplayable).toBeDefined();
    });

    it('check for failing result for RTW too early', async () => {
       
        let flowObject: any =  {
            Details : {
             ContactData: {
              Attributes: {},
              ContactId:  'k6gkw3e3-a66b-4362-af33-65c752a6844d',
              },
              Parameters : {
                ReturnDayToWork: '11232022',
                RTWDateFormatType: '1'
              }
          }
            };
        flowObject = {...flowEvent, ...flowObject}
        const result = await validateRtwHandler(flowObject)
        console.log(result);
        expect(result).toBeDefined();
        expect((<RtwResponse>result).IsSaveSuccess).toBe(false);
        expect((<RtwResponse>result).IsRTWValid).toBe(false);
        expect((<RtwResponse>result).RTWNotValidReason).toBe(ValidationErrors.ReturnToWorkDateTooEarly);
        expect((<RtwResponse>result).ReturnDayToWorkplayable).toBeDefined();
    });
})
