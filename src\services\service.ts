import dotenv from 'dotenv'
import { DateHelper, LogService } from '@/util'
import { AxiosInstance } from 'axios'
/***** */
import {DynamoDBClient } from '@aws-sdk/client-dynamodb'
import { DynamoDbDTO, LocaleModel} from '@/models'
/***** */
//FUTURE: Export Types in single file



//FUTURE: MAYBE AN INTERFACE INSTEAD
export class BaseService implements IService {
protected log: LogService;
protected dateHelper: DateHelper = new DateHelper();
//protected dynamoDbClient: DynamoDBClient = new DynamoDBClient({}); //FUTURE - MOVE THIS OUT, VERY BAD...or create a wrapper
//protected dynamoDbClient: IDBService;
protected tableName: string = '';
get TableName() {
    return this.tableName;
}
//FUTURE -> move this out of this class
//FUTURE: Make logOptionsDTO
constructor(logOptions? : any) {
    //&console.log('Base Constructor called');
    dotenv.config();
    this.log =  new LogService(logOptions);
}

    set Logger(logOptions: any) {
        // FUTURE: Add log re-config option
        this.log = new LogService(logOptions);
    }

    get Logger() : LogService {
        return this.log;
    }
    MapLocaleText(from: any[], to: LocaleModel[]) {  
        if(Array.isArray(from) && from.length>0)  {
        from.forEach ((x: any) => {
          console.log(JSON.stringify(x));
            to.push({
                Locale: x.locale || x.Locale,
                Description :x.description || x.Description
            })
           })
          }
        }
}

export interface IDBService {
    db: DynamoDBClient;
    //set DynamoDb(db: DynamoDBClient);
    get DynamoDb() : DynamoDBClient;
    get(query: DynamoDbDTO): Promise<any>;
    put(query:  DynamoDbDTO): Promise<any>;
    query(query: DynamoDbDTO): Promise<any>;
}

export interface IService {
    get TableName(): string;
}

export interface IHttpService {
   AxiosClient: AxiosInstance;
   //FUTURE: Considerations for scoped lifecycle
   //setHeaders()
   getAsync(url:string): Promise<any>;
   putAsync(url: string, payload: any): Promise<any>;
   postAsync(url: string, payload: any): Promise<any>;
   //delete
   //patch
   //etc.
}

export interface IDynamoService {

}

export interface IPreparedRequest {
    payload: any;
    config: any;
}

export interface IAuthService { //TODO: Extend HTTPService?
    ContactId: string;
    getToken(contactId: string):Promise<any>;
    prepareRequest(contactId: string, payload: any): Promise<IPreparedRequest>; 
}
